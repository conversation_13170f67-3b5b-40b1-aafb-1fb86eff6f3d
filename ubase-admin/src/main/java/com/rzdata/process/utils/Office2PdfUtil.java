package com.rzdata.process.utils;


import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * 调用转换工具类
 */
@Slf4j
public class Office2PdfUtil {


    public static boolean isLinux() {
        return System.getProperty("os.name").toLowerCase().contains("linux");
    }

    public static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }

    /**
     * 调用liboffice接口生成pdf
     *
     * @param srcPath
     * @param desPath
     */

    public static boolean Word2Pdf(String srcPath, String desPath) {
        //linux系统先生成当前文件夹同名文件
        if (isLinux()) {
            try {
                String tempPath = "/home/<USER>/uploadBranch/";
                String officeCmd = "/usr/bin/soffice";
                ProcessBuilder pb = new ProcessBuilder(officeCmd, "--headless", "--invisible", "--convert-to", "pdf", srcPath, "--outdir", tempPath, "test.pdf");
                //docker执行脚本
//                ProcessBuilder pb = new ProcessBuilder("/usr/bin/docker","exec","-i","offce1","/usr/bin/soffice","--headless","--invisible","--convert-to","pdf",srcPath,"--outdir",tempPath,"test.pdf");
                int runningStatus;
                Process p = pb.start();
                InputStream is = p.getErrorStream();
                BufferedReader br = new BufferedReader(new InputStreamReader(is));
                String line;
                while ((line = br.readLine()) != null) {
                    System.err.println(line);
                }
                br.close();
                InputStream is1 = p.getInputStream();
                BufferedReader br1 = new BufferedReader(new InputStreamReader(is1));
                String line1;
                while ((line1 = br1.readLine()) != null) {
                    System.out.println(line1);
                }
                br1.close();
                runningStatus = p.waitFor();

                if (runningStatus == 0) {
                    String fileName = null;//得到后缀名
                    if (srcPath != null) {
                        fileName = srcPath.substring(srcPath.lastIndexOf("/") + 1);
                        fileName = tempPath + fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
                        FileUtils.copyFile(FileUtil.file(fileName), FileUtil.file(desPath));
                        FileUtil.del(fileName);
                    }
                    return true;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;

            }
            return false;
        } else if (isWindows()) {
            String libreOfficePath = "C:\\Program Files\\LibreOffice\\program\\soffice.exe";
            //命令:"C:\Program Files\LibreOffice\program\soffice.exe" --convert-to pdf "D:\GoogleDownload\a3pvn-hqqoh.docx" --outdir C:\Users\<USER>\Desktop\Test
            StringBuilder cmd = new StringBuilder();
            cmd.append("\"");
            cmd.append(libreOfficePath);
            cmd.append("\"");
            cmd.append(" --convert-to pdf ");
            cmd.append("\"");
            cmd.append(srcPath);
            cmd.append("\"");
            cmd.append(" --outdir ");
            cmd.append(desPath);
            System.out.println("cmd = " + cmd.toString());
            try {
                return runCMD(cmd.toString());
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            } catch (InterruptedException e) {
                e.printStackTrace();
                return false;
            }
        }
        return false;

    }

    public static boolean runCMD(String cmd) throws IOException, InterruptedException {
        Process p = Runtime.getRuntime().exec(cmd);
        BufferedReader br = null;
        try {
            br = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String readLine = br.readLine();
            StringBuilder builder = new StringBuilder();
            while (readLine != null) {
                readLine = br.readLine();
                builder.append(readLine);
            }
            p.waitFor();
            int i = p.exitValue();
            if (i == 0) {
                return true;
            } else {
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (br != null) {
                br.close();
            }
        }
    }
}
