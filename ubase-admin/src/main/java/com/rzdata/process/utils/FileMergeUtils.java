package com.rzdata.process.utils;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.JsonUtils;
import com.rzdata.process.domain.bo.DocMergeApprovalTemplateBo;
import com.rzdata.process.domain.bo.DocMergeBasicInfoBo;
import com.rzdata.process.domain.bo.DocMergeHeadAndFootTemplateBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.FileLocalStoreConfig;
import com.rzdata.process.service.IBasicFileService;
import com.spire.doc.*;
import com.spire.doc.collections.CommentsCollection;
import com.spire.doc.documents.WatermarkLayout;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2022/1/19 11:44
 * @Version 1.0
 * @Description 文件合并工具类
 */
@Component
@Slf4j
public class FileMergeUtils {
    private static IBasicFileService iBasicFileService;

    private static ConfigService configService;

    public static String wordToPdf(String docPath) {
        File file = new File(docPath);
        Document document = new Document(docPath);
        //文件类型 doc docx
        String fileType = FilenameUtils.getExtension(file.getName());
        log.info("fileType = " + fileType);
        String pfdFilepath = docPath.replaceAll(fileType,"pdf");
        log.info("pfdFilepath = " + pfdFilepath);
        document.saveToFile(pfdFilepath,FileFormat.PDF);
        document.dispose();
        return pfdFilepath;
    }

    /**
     * 清除批注
     * @param docPath 文档地址
     * @return
     */
    public static void clearComments(String docPath) {
        Document document= new Document(docPath);
        CommentsCollection comments = document.getComments();
        comments.clear();
        document.saveToFile(docPath,document.getDetectedFormatType());
    }

    @Autowired
    public void setiBasicFileService(IBasicFileService iBasicFileService) {
        FileMergeUtils.iBasicFileService = iBasicFileService;
    }
    @Autowired
    public void setConfigService(ConfigService configService) {
        FileMergeUtils.configService = configService;
    }
    /**
     * 格式化页眉页脚 想从别的文件复制页眉页脚 先调用removeHeadFoot移除自身的页眉页脚
     * @param mainPath 主文件地址
     */
    public static void copyHeadFoot(String mainPath,String templatePath){
        //主文件操作
        Document document = new Document(mainPath);
        Document template = new Document(templatePath);
        HeaderFooter header = template.getSections().get(0).getHeadersFooters().getHeader();
        HeaderFooter footer = template.getSections().get(0).getHeadersFooters().getFooter();
        //遍历mainDocument的section
        for (int i = 0; i< document.getSections().getCount();i++) {
            Section section = document.getSections().get(i);
            //遍历页眉中的对象
            for(int j = 0 ; j< header.getChildObjects().getCount();j++) {
                //获取页眉中的所有子对象
                Object object1 = header.getChildObjects().get(j);
                //添加header到文档
                section.getHeadersFooters().getHeader().getChildObjects().add(((DocumentObject) object1).deepClone());
            }
            //同理复制页脚
            for(int z = 0 ; z< footer.getChildObjects().getCount();z++) {
                Object object2 = footer.getChildObjects().get(z);
                //先清除原来的页码
                section.getHeadersFooters().getFooter().getChildObjects().add(((DocumentObject) object2).deepClone());
            }
        }
        document.saveToFile(mainPath);
    }

    /**
     * 移除页眉页脚
     * @param mainPath 主文件地址
     */
    public static void removeHeadFoot(String mainPath){
        //主文件操作
        Document document = new Document(mainPath);
        //遍历mainDocument的section
        for (int i = 0; i< document.getSections().getCount();i++) {
            Section section = document.getSections().get(i);
            section.getHeadersFooters().getHeader().getChildObjects().clear();
            section.getHeadersFooters().getFooter().getChildObjects().clear();
        }
        document.saveToFile(mainPath);
    }


    /**
     * 文档合并 复制copyDoc中的内容到mainDoc中
     * @param mainDocPath 主文件
     * @param copyDocPath 复制的文件
     */
    public static void mergeDoc(String mainDocPath,String copyDocPath){
        //加载第一个文档
        Document mainDoc = new Document(mainDocPath);
        Document copyDoc = new Document(copyDocPath);
        //获取第一个文档的最后一个section
        Section lastSection = mainDoc.getLastSection();
        //将第二个文档的段落作为新的段落添加到第一个文档的最后一个section
        for (Section section:(Iterable <Section>)copyDoc.getSections()) {
            for (DocumentObject obj:(Iterable <DocumentObject>)section.getBody().getChildObjects()
            ) {
                lastSection.getBody().getChildObjects().add(obj.deepClone());
            }
        }
        //原路径保存 覆盖旧文件
        mainDoc.saveToFile(mainDocPath);
    }

    /**
     * 文档合并 复制copyDoc中的内容到mainDoc中
     * @param mainDocPath 主文件
     * @param copyDocPath 复制的文件
     */
    public static void mergeInsertDoc(String mainDocPath,String copyDocPath){
        try {
            //加载第一个文档
//        Document mainDoc = new Document(mainDocPath);
//        Document copyDoc = new Document(copyDocPath);
            Document mainDoc = new Document(mainDocPath);
            Document document = new Document(copyDocPath);
            //使用insertTextFromFile方法将copyDoc的内容插入到mainDoc
            mainDoc.insertTextFromFile(copyDocPath,document.getDetectedFormatType());
            //原路径保存 覆盖旧文件
            mainDoc.saveToFile(mainDocPath);
            mainDoc.dispose();
            document.dispose();
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }

    }


    /**
     * 手动新开一页
     * @param Path
     */
    public static void addSection(String Path) {
        Document mainDoc = new Document(Path);
        mainDoc.addSection();
        mainDoc.saveToFile(Path);
    }

    /**
     * 清楚最后一页的所有属性
     * @param path
     */
    public static void clearSection(String path) {
        Document document = new Document(path);
        document.getLastSection().getBody().getChildObjects().clear();
        document.saveToFile(path);
    }

    /**
     * 文档合并 复制copyDoc中的内容到mainDoc中
     * @param mainDocPath 主文件
     * @param copyDocPathList 复制的文件集合
     */
    public static void mergeDoc(String mainDocPath, List<String> copyDocPathList){
        try {
            File file = FileUtils.getFile(mainDocPath);
            if (!file.exists()){
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            for (String copyDocPath: copyDocPathList) {
                //加载第一个文档
                Document mainDoc = new Document(mainDocPath);
                Document document = new Document(copyDocPath);
                //使用insertTextFromFile方法将copyDoc的内容插入到mainDoc
                mainDoc.insertTextFromFile(copyDocPath,document.getDetectedFormatType());
                //原路径保存 覆盖旧文件
                mainDoc.saveToFile(mainDocPath);
                mainDoc.dispose();
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("文件合稿失败:"+e.getMessage(),e);
        }

    }
    /**
     * 对传入的模板doc进行赋值处理并且另存为一个新文件
     * @param basicInfoBo 模板doc信息
     * @return 新文件存放绝对路径
     */
    public static String dealBasicInfoTemplate( DocMergeBasicInfoBo basicInfoBo) throws IOException {
        //通过模板文件的id 找到他的位置
        String templateDocPath = getFilePathById(basicInfoBo.getTemplateDocId());
        //String templateDocPath = "store/file/20220120103617/Template_BasicInfo.docx";
        String itemTemplatePath = copyFileRename(templateDocPath,"_item");
        formatTemplate(itemTemplatePath,basicInfoBo,Configure.createDefault());
        return itemTemplatePath;
    }

    private static void formatTemplate(String templateDocPath, Object data, Configure configure) throws IOException {
        File templateFile = FileUtils.getFile(templateDocPath);
        XWPFTemplate template = XWPFTemplate.compile(templateFile,configure).render(data);
        FileOutputStream out = FileUtils.openOutputStream(templateFile);
        template.write(out);
        out.flush();
        IOUtils.close(out);
        IOUtils.close(template);
    }

    /**
     * 对传入的模板doc进行赋值处理并且另存为一个新文件
     * @param headTemplateBo 模板doc信息
     * @return 新文件存放绝对路径
     */
    public static String dealHeadTemplate(DocMergeHeadAndFootTemplateBo headTemplateBo) throws IOException {
        String templateDocPath = getFilePathById(headTemplateBo.getTemplateDocId());
        //String templateDocPath = "store\\file\\20220120103617\\Template_HeadFoot.docx";
        String itemTemplatePath = copyFileRename(templateDocPath,"_item");
        Map<String, Object> data = new HashMap<>();
        // 本地图片
        if (StringUtils.isNotEmpty(headTemplateBo.getPicPath())) {
            data.put("picPath", new PictureRenderData(80, 100,headTemplateBo.getPicPath()));
        }
        Map<String,String> map = JsonUtils.parseMap(headTemplateBo.getValue());
        for (Map.Entry<String, String> entry : map.entrySet()) {
            data.put(entry.getKey(),entry.getValue());
        }
        formatTemplate(itemTemplatePath, data,Configure.createDefault());
        return itemTemplatePath;
    }

    /**
     * 设置文字水印
     * @param docPath doc文件地址
     * @param text 水印文字
     */
    public static void insertTextWatermark(String docPath,String text) {
        Document document = new Document(docPath);
        for (Section section:(Iterable <Section>)document.getSections()) {
            TextWatermark txtWatermark = new TextWatermark();
            txtWatermark.setText(text);
            txtWatermark.setFontSize(50);
            txtWatermark.setColor(Color.black);
            txtWatermark.setLayout(WatermarkLayout.Horizontal);
            section.getDocument().setWatermark(txtWatermark);
        }
        document.saveToFile(docPath);
        document.dispose();
    }

    /**
     * 设置图片水印
     * @param docPath doc文件地址
     * @param picPath 水印图片地址
     */
    public static void insertPicWatermark(String docPath,String picPath) {
        Document document = new Document(docPath);
        for (Section section:(Iterable <Section>)document.getSections()) {
            PictureWatermark picture = new PictureWatermark();
            picture.setPicture(picPath);
            picture.setScaling(5);
            picture.isWashout(false);
            section.getDocument().setWatermark(picture);
        }
        document.saveToFile(docPath);
    }


    /**
     * 拷贝一份文件并且重命名为:原名+appendName 然后保存在和源文件同一目录下
     * @param sourceFilePath 被复制的文件路径
     * @param appendName 复制后追加的文件名
     * @return 复制生成的新文件地址
     */
    public static String copyFileRename(String sourceFilePath,String appendName) throws IOException {
        try {
            File mainFile = FileUtils.getFile(sourceFilePath);
            //文件后缀
            String mainFilePre = mainFile.getName().substring(mainFile.getName().lastIndexOf("."));
            //文件名
            String mainFileName = mainFile.getName().substring(0,mainFile.getName().lastIndexOf("."));
            //新文件名
            String copyFileName = mainFileName+appendName+mainFilePre;
            String copyFilePath = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH) + File.separator + mainFile.getParentFile().getName()+File.separator+copyFileName;
            File copyFile = FileUtils.getFile(copyFilePath);
            FileUtils.deleteQuietly(copyFile);
            FileUtils.copyFile(mainFile,copyFile);
            return copyFilePath;
        }catch (FileNotFoundException e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过文档ID 获取到该文件路径
     * @param fileId
     * @return 文件路径
     */
    public static String getFilePathById(String fileId) throws FileNotFoundException {
        //上传文件存储在本地的根路径KEY
        //查出文件对象
        BasicFileVo fileVo = iBasicFileService.getVoById(fileId);
        if (ObjectUtils.isNotEmpty(fileVo)){
            String filePath = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH)+ File.separator + fileVo.getFilePath();
            return filePath;
        }else {
            throw new FileNotFoundException("未查询到该文件");
        }
    }

    public static String dealApprovalTemplate(DocMergeApprovalTemplateBo approvalTemplate) throws IOException {
        String approvalTemplateDocPath = getFilePathById(approvalTemplate.getTemplateDocId());
        //String approvalTemplateDocPath = "store\\file\\20220120103617\\Template_ApprovalRecord.docx";
        String itemApprovalTemplateDocPath = copyFileRename(approvalTemplateDocPath,"_item");
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure configure = Configure.builder().bind("approvals", policy).build();
        formatTemplate(itemApprovalTemplateDocPath,approvalTemplate,configure);
        return itemApprovalTemplateDocPath;
    }
}
