package com.rzdata.process.utils;

import cn.net.drm.edi.client.DrmAgent;
import cn.net.drm.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.*;

/**
 * 使用天融信进行文件加密解密
 *
 * <AUTHOR>
 * @Date 2022/4/18 17:26
 * @Version 1.0
 * @Description
 */
public class DrmUtils {


    @Value("drm.enable")
    private boolean enable;

    /**
     * 加密文件
     *
     * @param filePath        被加密文件路径
     * @param encryptFilePath 被加密之后的文件存放路径
     */
    private void encryptFile(String filePath, String encryptFilePath) {
        if (enable) {
            InputStream fis = null;
            OutputStream fos = null;
            try {
                //long start = System.currentTimeMillis();
                fis = new FileInputStream(filePath);
                BufferedInputStream bis = new BufferedInputStream(fis);
                byte[] header = new byte[256];

                bis.mark(256);
                bis.read(header);
                bis.reset();

                boolean isEncryptFile = DrmAgent.getInstance().isEncrypted(header);

                if (!isEncryptFile) {
                    fis = DrmAgent.getInstance().encryptBasicStream(bis, "test", 5);
                }
                fos = new FileOutputStream(encryptFilePath);
                int len;
                byte[] buffer = new byte[4096];
                while ((len = fis.read(buffer)) > 0) {
                    fos.write(buffer, 0, len);
                }
                //System.out.println("used:" + (System.currentTimeMillis() - start));
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeQuietly(fis);
                IOUtils.closeQuietly(fos);
            }
        }
    }


    /**
     * 解密文件
     *
     * @param encryptFilePath 已加密文件地址
     * @param decryptFilePath 解密之后的文件存放路径
     */
    private void decryptFile(String encryptFilePath, String decryptFilePath) {
        if (enable) {
            InputStream fis = null;
            OutputStream fos = null;
            try {
                //long start = System.currentTimeMillis();
                fis = new FileInputStream(encryptFilePath);
                BufferedInputStream bis = new BufferedInputStream(fis);
                byte[] header = new byte[256];

                bis.mark(256);
                bis.read(header);
                bis.reset();

                boolean isEncryptFile = DrmAgent.getInstance().isEncrypted(header);

                if (isEncryptFile) {
                    fis = DrmAgent.getInstance().decrypt(bis);
                }
                fos = new FileOutputStream(decryptFilePath);
                int len = 0;
                byte[] buffer = new byte[4096];
                while ((len = fis.read(buffer)) > 0) {
                    fos.write(buffer, 0, len);
                }
                //System.out.println("used:" + (System.currentTimeMillis() - start));
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeQuietly(fis);
                IOUtils.closeQuietly(fos);
            }
        }
    }
}
