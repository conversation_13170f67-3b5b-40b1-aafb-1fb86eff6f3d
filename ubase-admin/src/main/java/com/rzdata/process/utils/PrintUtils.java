package com.rzdata.process.utils;

import javax.print.*;
import javax.print.attribute.DocAttributeSet;
import javax.print.attribute.HashDocAttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.swing.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.vo.FileLocalStoreConfig;
import com.spire.doc.Document;
import com.spire.ms.Printing.PrinterSettings;

/**
 * @auther xcy
 * @create 2022-01-18 14:57
 * 打印
 */
public class PrintUtils {

    public static void printWord(String filePath, String printerName) {
        //加载Word文档
        Document document = new Document();
        String path = SpringUtil.getBean(ConfigService.class).getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH);
        String absoluteFilePath = path + File.separator + filePath;
        document.loadFromFile(absoluteFilePath);
        PrintService defaultService = PrintServiceLookup.lookupDefaultPrintService();
        //创建PrinterSettings对象
        PrinterSettings printerSettings = new PrinterSettings();
        if(ObjectUtil.isEmpty(printerName)) {
            printerName = defaultService.getName();
        }
        //指定物理打印机名称
        printerSettings.setPrinterName(printerName);

        //设置打印份数
        printerSettings.setCopies((short) 1);

        //设置打印范围
        printerSettings.setFromPage(2);
        printerSettings.setToPage(4);

        //应用打印设置
        document.getPrintDocument().setPrinterSettings(printerSettings);

        //执行打印
        document.getPrintDocument().print();
    }

}
