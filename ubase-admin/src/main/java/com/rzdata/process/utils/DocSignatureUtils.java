package com.rzdata.process.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ZipUtil;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.domain.vo.FileLocalStoreConfig;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import lombok.extern.slf4j.Slf4j;
import net.qiyuesuo.sdk.SDKClient;
import net.qiyuesuo.sdk.api.ContractService;
import net.qiyuesuo.sdk.api.SignService;
import net.qiyuesuo.sdk.bean.company.TenantType;
import net.qiyuesuo.sdk.bean.contract.Action;
import net.qiyuesuo.sdk.bean.contract.ActionType;
import net.qiyuesuo.sdk.bean.contract.CreateContractRequest;
import net.qiyuesuo.sdk.bean.contract.ViewUrlRequest;
import net.qiyuesuo.sdk.bean.document.CreateDocumentRequest;
import net.qiyuesuo.sdk.bean.document.CreateDocumentResult;
import net.qiyuesuo.sdk.bean.sign.SignUrlRequest;
import net.qiyuesuo.sdk.bean.sign.Signatory;
import net.qiyuesuo.sdk.common.http.StreamFile;
import net.qiyuesuo.sdk.common.utils.IOUtils;
import net.qiyuesuo.sdk.impl.ContractServiceImpl;
import net.qiyuesuo.sdk.impl.SignServiceImpl;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/1/26 17:31
 * @Version 1.0
 * @Description 文件签章工具类
 */
@Component
@Slf4j
public class DocSignatureUtils {

    private static IBasicFileService iBasicFileService;

    private static ConfigService configService;

    private static FileUploadDownloadService fileUploadService;

    private static ISysConfigService sysConfigService;

    private static IStandardService standardService;
    private static IVersionService versionService;


    @Autowired
    public void setiBasicFileService(IBasicFileService iBasicFileService) {
        DocSignatureUtils.iBasicFileService = iBasicFileService;
    }
    @Autowired
    public void setConfigService(ConfigService configService) {
        DocSignatureUtils.configService = configService;
    }

    @Autowired
    public void setStandardService(IStandardService standardService) {
        DocSignatureUtils.standardService = standardService;
    }
    @Autowired
    public void setFileUploadService(FileUploadDownloadService fileUploadService) {
        this.fileUploadService = fileUploadService;
    }
    @Autowired
    public void setSysConfigService(ISysConfigService sysConfigService) {
        this.sysConfigService = sysConfigService;
    }
    @Autowired
    public static void setVersionService(IVersionService versionService) {
        DocSignatureUtils.versionService = versionService;
    }

    /**
     * 第一步
     * @param client 实例对象
     * @param inputStream 文档流
     * @param fileName 契约锁显示名称
     * @param fileType 文件类型
     * @return 文档id
     * @throws Exception
     */
    public static Long createByFile(SDKClient client, InputStream inputStream, String fileName, String fileType) throws Exception {
        Long documentId;
        ContractService contractService = new ContractServiceImpl(client);
        //方法调用
        CreateDocumentRequest request = new CreateDocumentRequest();
        request.setFile(new StreamFile(inputStream));
        request.setFileType(fileType);
        request.setTitle(fileName);
        CreateDocumentResult result = contractService.createByFile(request);
        documentId = result.getDocumentId();
        IOUtils.safeClose(inputStream);
        return documentId;
    }




    /**
     * 第二步
     * 1.1.4.1 用业务分类创建合同
     * @param subject    合同名称 必传
     * @param send       是否立即发起合同，默认true。（true：立即发起；false：保存为草稿）
     * @param bizId      业务系统ID
     * @param sealIdList 印章编号集合
     * @param documents 文档id
     * @return
     */
    public static Long createContractByCategory(SDKClient client, String subject, boolean send, String bizId, List<Long> sealIdList, List<Long> documents,String tenantName) throws Exception {
        Long contractId = null;
        ContractService contractService = new ContractServiceImpl(client);
        CreateContractRequest createContractRequest = new CreateContractRequest();
        createContractRequest.setSubject(subject);
        createContractRequest.setCategoryId(Long.parseLong(bizId));
        createContractRequest.setSend(send);
        createContractRequest.setBizId(bizId);
        createContractRequest.setDocuments(documents);

        //一、发起方
        List<Signatory> signatories = new ArrayList<>();
        Signatory signatory = new Signatory();
        signatory.setContact("");
        signatory.setTenantType(TenantType.COMPANY);
        signatory.setTenantName(tenantName);
        //1、指定印章
        List<Action> actionList = new ArrayList<>();
        Action action = new Action();
        action.setType(ActionType.CORPORATE);
        action.setSerialNo(1);
        Set<Long> sealset = new HashSet<>(sealIdList);
        action.setSealIds(sealset);
        actionList.add(action);
        signatory.setActions(actionList);
        signatories.add(signatory);
        createContractRequest.setSignatories(signatories);
        contractId = contractService.createContractByCategory(createContractRequest);
        return contractId;
    }


    public static String signUrl(Long contractId,SDKClient client,String tenantName){
        String signurl = "";
        SignService signService = new SignServiceImpl(client);
        //方法调用
        SignUrlRequest request = new SignUrlRequest();
        request.setContractId(contractId);
        request.setTenantType(TenantType.COMPANY);
        request.setTenantName(tenantName);
        try {
            signurl = signService.signUrl(request);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return signurl;
    }

    /**第六部
     * 1.4.1.2 合同浏览页面
     * @param contractId
     * @param expireTime 链接过期时间，取值范围：5分钟 - 3天，默认30分钟，单位（秒）例如：1800（30分钟）
     * @return
     */
    public static String viewUrl(Long contractId, Integer expireTime,SDKClient client) throws Exception{

        SignService signService = new SignServiceImpl(client);
        ContractService contractService = new ContractServiceImpl(client);
        //方法调用
        ViewUrlRequest request = new ViewUrlRequest();
        request.setContractId(contractId);
        //页面预览类型：DETAIL（详情页），CONTENT（合同正文），默认DETAIL
        request.setPageType(ViewUrlRequest.PageType.CONTENT);
        request.setExpireTime(expireTime);
        String viewUrl = contractService.viewUrl(request);
        return viewUrl;
    }

    /**
     *1.4.2.1 下载合同
     * @param contractId 合同id
     */
    public static void download(Long contractId, SDKClient client,String path,String docId,String parentName) throws Exception{
        ContractService contractService = new ContractServiceImpl(client);
        String completePath = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH) + File.separator + path;
        System.out.println("completePath = " + completePath);
        File file = FileUtils.getFile(completePath);
        if (!file.exists()){
            System.out.println("file.createNewFile()");
            file.createNewFile();
        }
        OutputStream outputStream = FileUtils.openOutputStream(file);
        contractService.download(contractId, outputStream);
        IOUtils.safeClose(outputStream);
        //解压zip文件
        File unZipDirFile = ZipUtil.unzip(file);
        System.out.println("unZipDirFile.getPath() = " + unZipDirFile.getPath());
        System.out.println("unZipDirFile.getName() = " + unZipDirFile.getName());
        //筛选出解压后的文件夹内的pdf文件
        Collection <File> test = FileUtils.listFiles(unZipDirFile,new String[]{"pdf"},false);
        //文件夹的内容为(签章文件名(根据你上传的文件变动).pdf和 签署摘要.pdf(固定名称))
        //去除签章摘要.pdf(没用)
        test=test.stream().filter(file1 ->
                !ObjectUtil.equal(file1.getName(),"签署摘要.pdf")).collect(Collectors.toList());
        //files就剩下一个文件 就是签章文件的pdf
        List<File> files = test.stream().collect(Collectors.toList());
        //下载完之后把文件数据插入到数据库
        BasicFileBo basicFileBo = new BasicFileBo();
        //拿到名称
        System.out.println("files.get(0).getName() = " + files.get(0).getName());
        basicFileBo.setFileName(files.get(0).getName());
        //拼接地址
        //basicFileBo.setFilePath(parentName+File.separator+files.get(0).getParentFile().getName()+parentName+File.separator+files.get(0).getName());
        System.out.println("files.get(0).getPath() = " + files.get(0).getPath());
        basicFileBo.setFilePath(path.replace(".zip","")+File.separator+files.get(0).getName());
        System.out.println("basicFileBo = " + basicFileBo.getFilePath());
        basicFileBo.setCreateTime(new Date());
        Boolean asPlatform = Boolean.valueOf(sysConfigService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(files.get(0).getPath());
            basicFileBo.setExternalFilePath(fileOsbeginuploadRes.getName());
            basicFileBo.setExternalFileId(fileOsbeginuploadRes.getDocid());
            basicFileBo.setExternalRev(fileOsbeginuploadRes.getRev());
        }
        //拿到主键id
        String id = iBasicFileService.insertByBo(basicFileBo);
        //用docId作为key 签章后的id作为value 传入redis中 用于修订结束后 获取修订后的签章文件id ModifyApplyServiceImpl.handleCreateVersion
        SpringUtils.getBean(RedisCache.class).setCacheObject(docId,id);

        //StandardVo voById = standardService.getVoById(docNo);
        //LambdaQueryWrapper<Version> query = new LambdaQueryWrapper();
        //query.eq(Version::getDocId,voById.getId());
        //query.eq(Version::getVersionValue,voById.getVersions());
        //Version voOne = versionService.getOne(query);
        /*if (voOne!=null){
            voOne.setEncryptFileId(id);
            versionService.updateById(voOne);
        }*/
    }
}
