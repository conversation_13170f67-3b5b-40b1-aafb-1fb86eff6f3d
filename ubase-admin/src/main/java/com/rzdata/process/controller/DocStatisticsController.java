package com.rzdata.process.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo;
import com.rzdata.process.domain.vo.DocStatisticsChangeTypeVo;
import com.rzdata.process.domain.vo.DocStatisticsRecDisVo;
import com.rzdata.process.service.IDocStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 13:43
 * @Version 1.0
 * @Description 文件统计控制器
 */

@Validated
@Slf4j
@Api(value = "文件统计控制器", tags = {"文件统计管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-statistics")
public class DocStatisticsController{

    @Resource
    IDocStatisticsService docStatisticsService;

    /**
     * 变更类型统计
     */
    @ApiOperation(value="变更类型统计")
    @PostMapping("/change-type")
    AjaxResult<List<DocStatisticsChangeTypeVo>> changeType(@RequestBody DocStatisticsBo docStatisticsBo){
        return docStatisticsService.changeType(docStatisticsBo);
    }

    /**
     * 变更要素统计
     */
    @ApiOperation(value="变更要素统计")
    @PostMapping("/change-factor")
    AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(@RequestBody DocStatisticsBo docStatisticsBo){
        return docStatisticsService.changeFactor(docStatisticsBo);
    }

    /**
     * 分发回收统计
     */
    @ApiOperation(value="分发回收统计")
    @PostMapping("/recovery")
    AjaxResult<List<DocStatisticsRecDisVo>> recovery(@RequestBody StandardBo bo){
        return docStatisticsService.recovery(bo);
    }

    /**
     * 签收回收率统计
     */
    @ApiOperation(value="签收回收率统计")
    @PostMapping("/distribute")
    AjaxResult<List<HashMap>> distribute(@RequestBody DocDisStatisticsBo bo){
        return docStatisticsService.distribute(bo);
    }


    /**
     * 个人阅读
     */
    @ApiOperation(value="个人阅读")
    @PostMapping("/personal-reading")
    AjaxResult<List<DocStatisticsRecDisVo>> personalReading(@RequestBody StandardBo bo){
        return docStatisticsService.recovery(bo);
    }

}
