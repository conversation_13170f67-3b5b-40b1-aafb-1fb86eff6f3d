package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.bo.DocVersionFavoritesBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import com.rzdata.process.domain.vo.DocVersionFavoritesVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.service.IDocVersionFavoritesService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 我的收藏Controller
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Validated
@Api(value = "我的收藏控制器", tags = {"我的收藏管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/versionFavorites")
public class DocVersionFavoritesController extends BaseController {

    private final IDocVersionFavoritesService iDocVersionFavoritesService;

    @ApiOperation("查询我的收藏列表")
    @GetMapping("/page")
    public TableDataInfo<DocDistributeLogVo> page(DocDistributeLogBo bo) {
        return iDocVersionFavoritesService.queryPageFavorites(bo);
    }

    /**
     * 查询记录文件列表
     */
    @ApiOperation("查询我的收藏记录文件列表")
    @GetMapping("/page/record")
    public TableDataInfo<VersionVo> pageRecordFile(@Validated(QueryGroup.class) VersionBo bo) {
        return iDocVersionFavoritesService.queryPageFavoritesRecordFile(bo);
    }


    /**
     * 新增我的收藏
     */
    @ApiOperation("新增或者删除收藏")
    @PreAuthorize("@ss.hasPermi('system:versionFavorites:add')")
    @Log(title = "我的收藏", businessType = BusinessType.INSERT)
    @GetMapping("/saveOrRemove/{versionId}")
    public AjaxResult<Void> saveOrRemove(@ApiParam("主键串")
                                    @NotEmpty(message = "主键不能为空")
                                    @PathVariable String versionId) {
        return toAjax(iDocVersionFavoritesService.saveOrRemoveFavorites(versionId) ? 1 : 0);
    }

    /**
     * 删除我的收藏
     */
    @ApiOperation("删除我的收藏")
    @PreAuthorize("@ss.hasPermi('system:versionFavorites:remove')")
    @Log(title = "我的收藏" , businessType = BusinessType.DELETE)
    @GetMapping("/remove/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocVersionFavoritesService.deleteWithValidByIds(Arrays.asList(ids)) ? 1 : 0);
    }
}
