package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.WorkflowApplyLogVo;
import com.rzdata.process.service.IWorkflowApplyLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 流程申请记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Validated
@Api(value = "流程申请记录控制器", tags = {"流程申请记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/workflowApplyLog")
public class WorkflowApplyLogController extends BaseController {

    private final IWorkflowApplyLogService iWorkflowApplyLogService;

    /**
     * 查询流程申请记录列表
     */
    @ApiOperation("查询流程申请记录列表")
    @GetMapping("/list")
    public TableDataInfo<WorkflowApplyLogVo> list(@Validated(QueryGroup.class) WorkflowApplyLogBo bo) {
        return iWorkflowApplyLogService.queryPageList(bo);
    }

    /**
     * 根据类型和业务id获取详情
     */
    @ApiOperation("根据类型和业务id获取详情")
    @GetMapping("/selectDetail")
    public Map<String, Object> selectDetail(@RequestParam("type") String type, @RequestParam("busId") String busId) {
        Map<String, Object> selectDetail = iWorkflowApplyLogService.selectDetail(type, busId);
        return selectDetail;
    }

    /**
     * 导出流程申请记录列表
     */
    @ApiOperation("导出流程申请记录列表")
    @Log(title = "流程申请记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated WorkflowApplyLogBo bo, HttpServletResponse response) {
        List<WorkflowApplyLogVo> list = iWorkflowApplyLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "流程申请记录", WorkflowApplyLogVo.class, response);
    }

    /**
     * 获取流程申请记录详细信息
     */
    @ApiOperation("获取流程申请记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<WorkflowApplyLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iWorkflowApplyLogService.queryById(id));
    }

    /**
     * 新增流程申请记录
     */
    @ApiOperation("新增流程申请记录")
    @Log(title = "流程申请记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody WorkflowApplyLogBo bo) {
        return toAjax(iWorkflowApplyLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改流程申请记录
     */
    @ApiOperation("修改流程申请记录")
    @Log(title = "流程申请记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody WorkflowApplyLogBo bo) {
        return toAjax(iWorkflowApplyLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除流程申请记录
     */
    @ApiOperation("删除流程申请记录")
    @Log(title = "流程申请记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iWorkflowApplyLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @GetMapping("/updateStatusByBusId")
    @ApiOperation("根据业务id更新数据状态")
    public AjaxResult<Void> updateStatusByBusId(@RequestParam("busId") String busId, @RequestParam("status") String status) {
        iWorkflowApplyLogService.updateStatusByBusId(busId, status, "","");
        return AjaxResult.success();
    }

    @GetMapping("/selectStatusByDocId")
    @ApiOperation("根据文件id获取流程状态")
    public AjaxResult<Integer> selectStatusByDocId(@RequestParam("docId") String docId,
                                                   @RequestParam("versionId") String versionId) {
        return iWorkflowApplyLogService.selectStatusByDocId(docId,versionId);
    }
}
