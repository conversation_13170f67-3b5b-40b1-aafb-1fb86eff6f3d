package com.rzdata.process.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.blueland.bpmclient.model.*;
import com.google.common.collect.Maps;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.ApiTypeEnum;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.WorkFlowPageVo;
import com.rzdata.system.domain.SysUserTenant;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.ISysUserTenantService;
import com.rzdata.system.service.WorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Validated
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping(value = "/workflow")
@Api(value = "流程服务", tags = {"流程服务"})
public class WorkflowController extends BaseController {

	private final WorkflowService workflowService;
	private final ISysUserService userService;
	@Resource
	private ISysUserTenantService sysUserTenantService;


	@CrossOrigin
	@ApiOperation(value = "根据流程定义Key获取流程定义信息", notes = "根据流程定义Key获取流程定义信息")
	@ApiImplicitParams({@ApiImplicitParam(name = "procDefKey", value = "流程定义key", dataType = "string", paramType = "path") })
	@GetMapping(value = "process/key/{procDefKey}")
	@ResponseBody
	public AjaxResult<ProcessDefinitionModel> getProcessDefinitionModel(@PathVariable("procDefKey") String procDefKey) throws Exception {

		ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(
				procDefKey);
		return AjaxResult.success(processDefinitionModel);

	}

	@CrossOrigin
	@ApiOperation(value = "获取流程第一个人工环节定义", notes = "获取流程第一个人工环节定义")
	@GetMapping(value = "startactdef/{procDefId}")
	@ResponseBody
	public AjaxResult<ActivityDefinitionModel> getStartActdef(
			@PathVariable("procDefId") String procDefId) throws Exception{
		ActivityDefinitionModel activityDefinitionModel = workflowService.getStartactDef(procDefId);
		return AjaxResult.success(activityDefinitionModel);

	}

	@CrossOrigin
	@ApiOperation(value = "新建流程，获取下一环节流程定义信息", notes = "新建流程，获取下一环节流程定义信息")
	@PostMapping(value = "nextacts/new")
	@ResponseBody
	public AjaxResult<List<ActivityDefinitionModel>> getNextActsByNew(@RequestBody Map<String, Object> params) {
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.putAll(params);
		List<ActivityDefinitionModel> list = workflowService.getNextActs(searchQuery);
		return AjaxResult.success(list);
	}

	@CrossOrigin
	@ApiOperation(value = "待办流程，获取下一环节流程定义信息", notes = "待办流程，获取下一环节流程定义信息")
	@PostMapping(value = "nextacts/pending")
	@ResponseBody
	public AjaxResult<List<ActivityDefinitionModel>> getNextActsByPending(@RequestBody SearchQuery searchQuery) {
		List<ActivityDefinitionModel> list = workflowService.getNextActs(searchQuery);
		return AjaxResult.success(list);
	}

	@CrossOrigin
	@ApiOperation(value = "新建流程，获取下一环节用户定义信息", notes = "新建流程，获取下一环节用户定义信息")
	@GetMapping(value = "nextactuser/new")
	@ResponseBody
	public AjaxResult<List<ActivityResourceModel>> getNextActUsersByNew(
			@RequestParam(value = "userOrgId") String userOrgId,
			@RequestParam(value = "curActDefId") String curActDefId,
			@RequestParam(value = "destActDefId") String destActDefId,
			@RequestParam(value = "procDefId") String procDefId, HttpServletRequest request) {
		String userId = SecurityUtils.getLoginUser().getUsername();
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(userOrgId);
		searchQuery.setCurActDefId(curActDefId);
		searchQuery.setDestActDefId(destActDefId);
		searchQuery.setProcDefId(procDefId);
		List<ActivityResourceModel> rs = workflowService.getNextActUsers(searchQuery);
		return AjaxResult.success(rs);
	}

	@CrossOrigin
	@ApiOperation(value = "新建流程，获取下一环节用户定义信息", notes = "新建流程，获取下一环节用户定义信息")
	@PostMapping(value = "nextactuser/new")
	@ResponseBody
	public AjaxResult<List<ActivityResourceModel>> getNextActUsersByNew(
			@RequestParam(value = "userOrgId") String userOrgId,
			@RequestParam(value = "curActDefId") String curActDefId,
			@RequestParam(value = "destActDefId") String destActDefId,
			@RequestParam(value = "procDefId") String procDefId, @RequestBody Map<String, Object> fields,
			HttpServletRequest request) {
		String userId = SecurityUtils.getLoginUser().getUsername();
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(userOrgId);
		searchQuery.setCurActDefId(curActDefId);
		searchQuery.setDestActDefId(destActDefId);
		searchQuery.setProcDefId(procDefId);
		searchQuery.putAll(fields);
		List<ActivityResourceModel> rs = workflowService.getNextActUsers(searchQuery);
		return AjaxResult.success(rs);
	}

	@CrossOrigin
	@ApiOperation(value = "待办流程，获取下一环节用户定义信息", notes = "待办流程，获取下一环节用户定义信息")
	@GetMapping(value = "nextactuser/pending")
	@ResponseBody
	public AjaxResult<List<ActivityResourceModel>> getNextActUsersByPending(
			@RequestParam(value = "userOrgId") String userOrgId,
			@RequestParam(value = "curActInstId") String curActInstId,
			@RequestParam(value = "destActDefId") String destActDefId, HttpServletRequest request) {
		String userId = SecurityUtils.getLoginUser().getUsername();
		String tenantId = SecurityUtils.getLoginUser().getTenantId();
		SearchQuery searchQuery = new SearchQuery();
		//searchQuery.setTenantId(SecurityUtils.getLoginUser().getTenantId());
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(userOrgId);
		searchQuery.setDestActDefId(destActDefId);
		searchQuery.setCurActInstId(curActInstId);
		List<ActivityResourceModel> rs = workflowService.getNextActUsers(searchQuery);
		//过滤非当前租户的用户信息
		List<ActivityResourceModel> ids = rs.stream().filter(model->ObjectUtil.equals("USER",model.getType())).collect(Collectors.toList());
		ids.forEach(id->{
			LambdaQueryWrapper<SysUserTenant> query = new LambdaQueryWrapper<>();
			query.eq(SysUserTenant::getUserName,id.getId());
			List<SysUserTenant> list = sysUserTenantService.list(query);
			boolean pass = false;
			for ( SysUserTenant sysUserTenant:list) {
				if (sysUserTenant.getTenantId().equals(tenantId)){
					pass=true;
					break;
				}
			}
			if (!pass){
				rs.remove(id);
			}
		});
		return AjaxResult.success(rs);
	}

	@CrossOrigin
	@ApiOperation(value = "待办流程，获取下一环节用户定义信息", notes = "待办流程，获取下一环节用户定义信息")
	@PostMapping(value = "nextactuser/pending")
	@ResponseBody
	public AjaxResult<List<ActivityResourceModel>> getNextActUsersByPending(
			@RequestParam(value = "userOrgId") String userOrgId,
			@RequestParam(value = "curActInstId") String curActInstId,
			@RequestParam(value = "destActDefId") String destActDefId, @RequestBody Map<String, Object> fields,
			HttpServletRequest request) {
		String userId = SecurityUtils.getLoginUser().getUsername();
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(userOrgId);
		searchQuery.setDestActDefId(destActDefId);
		searchQuery.setCurActInstId(curActInstId);
		searchQuery.putAll(fields);
		List<ActivityResourceModel> rs = workflowService.getNextActUsers(searchQuery);
		return AjaxResult.success(rs);
	}

	@CrossOrigin
	@ApiOperation(value = "获取待办实例信息", notes = "获取待办实例信息")
	@GetMapping(value = "flowinfo/pending")
	@ResponseBody
	public AjaxResult<HashMap<String, Object>> getPendingFlowInstance(
			@RequestParam(value = "curActInstId") String curActInstId, HttpServletRequest request) throws Exception{
		String userId = SecurityUtils.getLoginUser().getUsername();
		SysUser user = userService.selectUserByUserName(userId);
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(user.getDeptId().toString());
		searchQuery.setCurActInstId(curActInstId);
		searchQuery.setStatus(1);
		HashMap<String, Object> flowInfoMap = workflowService.getFLowInfo(searchQuery);
		return AjaxResult.success(flowInfoMap);
	}

	@CrossOrigin
	@ApiOperation(value = "获取已办实例信息", notes = "获取已办实例信息")
	@GetMapping(value = "flowinfo/yiban")
	@ResponseBody
	public AjaxResult<HashMap<String, Object>> getYiBanFlowInstance(
			@RequestParam(value = "curActInstId") String curActInstId, HttpServletRequest request) throws Exception{
		String userId = SecurityUtils.getLoginUser().getUsername();
		SysUser user = userService.selectUserByUserName(userId);
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(user.getDeptId().toString());
		searchQuery.setCurActInstId(curActInstId);
		searchQuery.setStatus(2);
		HashMap<String, Object> flowInfoMap = workflowService.getFLowInfo(searchQuery);
		return AjaxResult.success(flowInfoMap);
	}

	@CrossOrigin
	@ApiOperation(value = "获取办结实例信息", notes = "获取办结实例信息")
	@GetMapping(value = "flowinfo/banjie")
	@ResponseBody
	public AjaxResult<HashMap<String, Object>> getBanJieFlowInstance(
			 @RequestParam(value = "procInstId") String procInstId,
			HttpServletRequest request) throws Exception{
		String userId = SecurityUtils.getLoginUser().getUsername();
		SysUser user = userService.selectUserByUserName(userId);
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setUserId(userId);
		searchQuery.setUserOrgId(user.getDeptId().toString());
		searchQuery.setProcInstId(procInstId);
		searchQuery.setStatus(3);
		HashMap<String, Object> flowInfoMap = workflowService.getFLowInfo(searchQuery);
		return AjaxResult.success(flowInfoMap);
	}

	@CrossOrigin
	@ApiOperation(value = "新建页面，判断下一环节是否自动提交", notes = "新建页面，判断下一环节是否自动提交")
	@GetMapping(value = "autocommit/new/{procDefId}/{curActDefId}")
	@ResponseBody
	public AjaxResult<String> checkAutoCommitByNew(
			@PathVariable("procDefId") String procDefId, @PathVariable("curActDefId") String curActDefId) throws Exception{
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setProcDefId(procDefId);
		searchQuery.setCurActDefId(curActDefId);
		String actDefId = workflowService.checkAutoCommit(searchQuery);
		return AjaxResult.success("",actDefId);

	}

	@CrossOrigin
	@ApiOperation(value = "待办页面，判断下一环节是否自动提交", notes = "待办页面，判断下一环节是否自动提交")
	@GetMapping(value = "autocommit/pending/{curActInstId}")
	@ResponseBody
	public AjaxResult<String> checkAutoCommitByPending(
			@PathVariable("curActInstId") String curActInstId) throws Exception{
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setCurActInstId(curActInstId);
		String actDefId = workflowService.checkAutoCommit(searchQuery);
		return AjaxResult.success("",actDefId);
	}

	@CrossOrigin
	@ApiOperation(value = "待办页面，判断下一环节是否自动跳转", notes = "待办页面，判断下一环节是否自动跳转")
	@GetMapping(value = "autoJump/pending/{curActInstId}")
	@ResponseBody
	public AjaxResult<Map<String, Object>> checkAutoJumpByPending(
			@PathVariable("curActInstId") String curActInstId) throws Exception{
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setCurActInstId(curActInstId);
		Map<String, Object> actDefId = workflowService.checkAutoJump(searchQuery);
		return AjaxResult.success("",actDefId);
	}

	@CrossOrigin
	@ApiOperation(value = "新建页面，判断下一环节是否自动提交", notes = "新建页面，判断下一环节是否自动提交")
	@GetMapping(value = "autoJump/new/{procDefId}/{curActDefId}")
	@ResponseBody
	public AjaxResult<Map<String, Object>> checkAutoJumpByNew(
			@PathVariable("procDefId") String procDefId, @PathVariable("curActDefId") String curActDefId) throws Exception{
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setProcDefId(procDefId);
		searchQuery.setCurActDefId(curActDefId);
		Map<String, Object> actDefId = workflowService.checkAutoJump(searchQuery);
		return AjaxResult.success("",actDefId);

	}

	@CrossOrigin
	@ApiOperation(value = "提交流程", notes = "提交流程")
	@PostMapping(value = "submit")
	@ResponseBody
	public AjaxResult submitNewFLowInstance(@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{
		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.submit.name());
		return AjaxResult.success(processInstanceModel);
	}

	@CrossOrigin
	@ApiOperation(value = "暂存流程", notes = "暂存流程")
	@PostMapping(value = "save")
	@ResponseBody
	public AjaxResult<ProcessInstanceModel> saveFLowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{

		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.save.name());
		return AjaxResult.success(processInstanceModel);
	}

	/**
	 * 流程作废
	 *
	 * @param bpmClientInputModelBo
	 * @return
	 */
	@ApiOperation(value = "作废流程", notes = "作废流程")
	@PostMapping(value = "cancel")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ProcessInstanceModel> cancelFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{
		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.cancel.name());
		return AjaxResult.success(processInstanceModel);
	}

	/**
	 * 流程实例撤回到拟稿人环节
	 *
	 * @param bpmClientInputModelBo
	 * @return
	 */
	@ApiOperation(value = "流程实例撤回到拟稿人环节", notes = "流程实例撤回到拟稿人环节")
	@PostMapping(value = "backtostart")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ProcessInstanceModel> callBackStartFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{

		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.backtostart.name());
		return AjaxResult.success(processInstanceModel);
	}

	/**
	 * 流程实例撤回到上一环节
	 *
	 * @param bpmClientInputModelBo
	 * @return
	 */
	@ApiOperation(value = "流程实例撤回到上一环节", notes = "流程实例撤回到上一环节")
	@PostMapping(value = "backtoprev")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ProcessInstanceModel> backToPrevFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{

		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.backtoprev.name());
		return AjaxResult.success(processInstanceModel);
	}

	/**
	 * 提交流程驳回参数
	 *
	 * @param bpmClientInputModelBo
	 * @return
	 */
	@CrossOrigin
	@ApiOperation(value = "流程驳回", notes = "流程驳回")
	@PostMapping(value = "reject")
	@ResponseBody
	public AjaxResult<ProcessInstanceModel> rejectFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{

		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.reject.name());
		return AjaxResult.success(processInstanceModel);
	}

	/**
	 * 流程撤回
	 *
	 * @param bpmClientInputModelBo
	 * @return
	 */
	@CrossOrigin
	@ApiOperation(value = "流程撤回", notes = "流程撤回")
	@PostMapping(value = "back")
	@ResponseBody
	public AjaxResult<ProcessInstanceModel> backFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{
		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.back.name());
		return AjaxResult.success(processInstanceModel);
	}

	@ApiOperation(value = "流程转派", notes = "流程转派")
	@PostMapping(value = "transfer")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ProcessInstanceModel> transferFlowInstance(
			@RequestBody BpmClientInputModelBo bpmClientInputModelBo) throws Exception{
		ProcessInstanceModel processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
				WorkflowService.ENUM_ACTION.transfer.name());
		return AjaxResult.success(processInstanceModel);
	}

	@ApiOperation(value = "流程日志", notes = "流程日志")
	@PostMapping(value = "getLog")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<HashMap<String, Object>> getProcInstLog(
			@RequestBody BpmClientInputModel bpmClientInputModel) {
		HashMap<String, Object> procInstLog = workflowService.getProcInstLog(bpmClientInputModel);
		return AjaxResult.success(procInstLog);
	}

	@ApiOperation(value = "办件阅件列表查询", notes = "办件阅件列表查询")
	@PostMapping(value = "/list")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<PageResultModel> getRecordList(@RequestBody SearchQuery searchQuery) {
		PageResultModel pageResult = workflowService.findRecordList(searchQuery);
		return AjaxResult.success(pageResult);
	}

	@ApiOperation(value = "待办/已办列表查询", notes = "待办/已办列表查询")
	@PostMapping(value = "/toDoList")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<WorkFlowPageVo> getToDoList(@RequestBody ProcessBo bo) throws Exception{
		//接收人
		bo.setReceiverMan(SecurityUtils.getUsername());
		WorkFlowPageVo pageResult = workflowService.findToDoList(bo, ApiTypeEnum.PC);
		return AjaxResult.success(pageResult);
	}

	@ApiOperation(value = "待办/已办列表查询(移动端用)", notes = "待办/已办列表查询")
	@PostMapping(value = "/toDoListMobile")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<WorkFlowPageVo> getToDoListMobile(@RequestBody ProcessBo bo) {
		WorkFlowPageVo pageResult = workflowService.findToDoList(bo, ApiTypeEnum.MOBILE);
		return AjaxResult.success(pageResult);
	}

	@ApiOperation(value = "获取流程实例图形监控链接地址", notes = "获取流程实例图形监控链接地址")
	@GetMapping(value = "histasklogurl/{procInstId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<String> getHistasklogurl( @PathVariable String procInstId) throws Exception{
		String url = workflowService.getHistasklogurl(procInstId);
		return AjaxResult.success("",url);
	}

	@ApiOperation(value = "获取流程实例图形链接地址", notes = "获取流程实例图形监控链接地址")
	@GetMapping(value = "getImageUrl/{procDefId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<String> getProcessImageUrl( @PathVariable String procDefId) throws Exception{
		String url = workflowService.getImageUrl( procDefId);
		return AjaxResult.success("",url);
	}

	/**
	 * 获取流程状态
	 *
	 * @param procInstId
	 * @param curActInstId
	 * @return
	 */
	@ApiOperation(value = "获取流程状态", notes = "获取流程状态")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "procInstId", value = "流程实例id", dataType = "string", paramType = "path"),
			@ApiImplicitParam(name = "curActInstId", value = "当前环节实例id", dataType = "string", paramType = "path") })
	@GetMapping(value = "/getFlowStatus/{procInstId}/{curActInstId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<Integer> getFlowStatus( @PathVariable String procInstId,
			@PathVariable String curActInstId) throws Exception{
		int flowStatus = workflowService.getFlowStatus(procInstId, curActInstId);
		return AjaxResult.success(flowStatus);
	}

	@ApiOperation(value = "获取流程实例信息，包含当前用户所对应实例状态", notes = "获取流程实例信息，包含当前用户所对应实例状态")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "procInstId", value = "流程实例id", dataType = "string", paramType = "path"),
			@ApiImplicitParam(name = "curActInstId", value = "当前环节实例id", dataType = "string", paramType = "path") })
	@GetMapping(value = "instance/{procInstId}/{curActInstId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ProcessInstanceModel> getFlowProcessInstance(@PathVariable String procInstId, @PathVariable String curActInstId, HttpServletRequest request) throws Exception{
		String userId = SecurityUtils.getLoginUser().getUsername();
		ProcessInstanceModel procInst = workflowService.getProcessInstance(procInstId, curActInstId,
				userId);
		return AjaxResult.success(procInst);
	}

	@ApiOperation(value = "获取流程环节定义信息", notes = "获取流程环节定义信息")
	@GetMapping(value = "activitydefinition/{procDefId}/{actDefId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<ActivityDefinitionModel> getFlowActDef(
			@PathVariable String procDefId, @PathVariable String actDefId) throws Exception{
		ActivityDefinitionModel activityDefinitionModel = workflowService.getActDef( procDefId, actDefId);
		return AjaxResult.success(activityDefinitionModel);
	}

	@ApiOperation(value = "获取流程环节配置的表单", notes = "获取流程环节配置的表单")
	@GetMapping(value = "getFlowActForm/{procDefId}/{actDefId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<Map<String, String>> getFlowActForm(
			@PathVariable String procDefId, @PathVariable String actDefId) throws Exception{
		Map<String, String> formUrl = workflowService.getFlowActForm( procDefId, actDefId);
		return AjaxResult.success(formUrl);
	}

	@ApiOperation(value = "根据流程实例id获取流程实例信息", notes = "根据流程实例id获取流程实例信息")
	@GetMapping(value = "/procInstInfo/{procInstId}")
	@ResponseBody
	@CrossOrigin
	public AjaxResult<Map<String, Object>> getProcInstInfo(@PathVariable String procInstId)throws Exception {
		Map<String, Object> procInst = workflowService.getProcessInstById( procInstId);
		return AjaxResult.success(procInst);
	}

	@ApiOperation(value = "根据流程实例id获取流程实例信息和流程状态", notes = "根据流程实例id获取流程实例信息")
	@GetMapping(value = "/procInstInfoAndStatus/{procInstId}")
	@ResponseBody
	@CrossOrigin
	public Map<String, Object> procInstInfoAndStatus( @PathVariable String procInstId,
			HttpServletRequest request) {
		try {
			String userId = SecurityUtils.getLoginUser().getUsername();
			SearchQuery searchQuery = new SearchQuery();
			searchQuery.setRecUserId(userId);
			searchQuery.setStatus(1);
			PageResultModel pageResultModel = workflowService.findRecordList(searchQuery);
			if (pageResultModel == null || pageResultModel.getTotalCount() == 0) {
				return null;
			}
			List<Map<String, Object>> list = pageResultModel.getResult();
			return list.stream().filter(p -> p.get("topProcInstId").toString().equals(procInstId)).findFirst()
					.orElse(null);
		} catch (Exception ex) {
			log.error("获取流程信息 该用户的流程是待办还是已办", ex);
			return null;
		}
	}

	@ApiOperation(value = "获取当前用户流程实例数量", notes = "获取当前用户流程实例数量。status状态值{1:待办，2:已办，3:办结}")
	@GetMapping(value = "/count/{status}")
	@ResponseBody
	public Map<String, Integer> getRecordCount( @PathVariable int status,
			HttpServletRequest request) {
		try {
			String userId = SecurityUtils.getLoginUser().getUsername();
			SearchQuery searchQuery = new SearchQuery();
			searchQuery.setRecUserId(userId);
			searchQuery.setStatus(status);
			return workflowService.getRecordCount(searchQuery);
		} catch (Exception ex) {
			log.error("获取当前用户流程实例数量", ex);
			Map<String, Integer> result = Maps.newHashMap();
			result.put("count", 0);
			return result;
		}
	}

	@ApiOperation(value = "根据父流程实例id获取该流程的子流程，从而处理子流程", notes = "根据父流程实例id获取该流程的子流程，从而处理子流程")
	@GetMapping(value = "/subprocess/{status}/{procInstId}")
	public List<Map<String, Object>> getSubprocess( @PathVariable String status,
			@PathVariable String procInstId) {
		try {
			return workflowService.getSubprocess( status, procInstId);
		} catch (Exception ex) {
			log.error("根据父流程实例id获取该流程的子流程，从而处理子流程", ex);
			return Lists.newArrayList();
		}
	}

	@CrossOrigin
	@ApiOperation(value = "获取该用户是否处理过该流程", notes = "获取该用户是否处理该流程")
	@GetMapping(value = "/flowinfo/user/deal/{procInstId}")
	@ResponseBody
	public AjaxResult<Integer> getUserIsDealFlow( @PathVariable String procInstId,
			HttpServletRequest request) throws Exception{
		String userId = SecurityUtils.getLoginUser().getUsername();
		SearchQuery searchQuery = new SearchQuery();
		searchQuery.setRecUserId(userId);
		searchQuery.setProcInstId(procInstId);
		searchQuery.setStatus(1);
		Map<String, Integer> result = workflowService.getRecordCount(searchQuery);
		return AjaxResult.success(Integer.valueOf(result.get("count").toString()));
	}

	@CrossOrigin
	@ApiOperation(value = "获取扩展属性", notes = "获取扩展属性")
	@GetMapping("/getExtAttributeModel")
	public AjaxResult<List<ExtAttributeModel>> getExtAttributeModel(@RequestParam("procDefId") String procDefId, @RequestParam("actDefId") String actDefId){
		return AjaxResult.success(workflowService.getExtAttributeModel(procDefId, actDefId));
	}
}
