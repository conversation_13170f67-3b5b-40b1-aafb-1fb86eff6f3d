package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.RecoveryLogVo;
import com.rzdata.process.domain.bo.RecoveryLogBo;
import com.rzdata.process.service.IRecoveryLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件回收记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Validated
@Api(value = "文件回收记录控制器", tags = {"文件回收记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/recoveryLog")
public class RecoveryLogController extends BaseController {

    private final IRecoveryLogService iRecoveryLogService;

    /**
     * 查询文件回收记录列表
     */
    @ApiOperation("查询文件回收记录列表")
    @GetMapping("/list")
    public TableDataInfo<RecoveryLogVo> list(@Validated(QueryGroup.class) RecoveryLogBo bo) {
        return iRecoveryLogService.queryPageList(bo);
    }

    /**
     * 导出文件回收记录列表
     */
    @ApiOperation("导出文件回收记录列表")
    @Log(title = "文件回收记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated RecoveryLogBo bo, HttpServletResponse response) {
        List<RecoveryLogVo> list = iRecoveryLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件回收记录", RecoveryLogVo.class, response);
    }

    /**
     * 获取文件回收记录详细信息
     */
    @ApiOperation("获取文件回收记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<RecoveryLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iRecoveryLogService.queryById(id));
    }

    /**
     * 新增文件回收记录
     */
    @ApiOperation("新增文件回收记录")
    @Log(title = "文件回收记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody RecoveryLogBo bo) {
        return toAjax(iRecoveryLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件回收记录
     */
    @ApiOperation("修改文件回收记录")
    @Log(title = "文件回收记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody RecoveryLogBo bo) {
        return toAjax(iRecoveryLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件回收记录
     */
    @ApiOperation("删除文件回收记录")
    @Log(title = "文件回收记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iRecoveryLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
