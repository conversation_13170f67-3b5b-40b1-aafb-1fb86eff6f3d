package com.rzdata.process.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.bo.DocPreviewBo;
import com.rzdata.process.domain.bo.DocPreviewEditCallbackBo;
import com.rzdata.process.domain.bo.DocPreviewEditCallbackResponseBo;
import com.rzdata.process.domain.bo.DocPreviewEditorConfigBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.DocStatisticsRecDisVo;
import com.rzdata.process.domain.vo.FileLocalStoreConfig;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.DocStatusEnum;
import com.rzdata.process.enums.PreviewErrorEnum;
import com.rzdata.process.enums.PreviewSourceEnum;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IDocPreviewService;
import com.rzdata.process.service.IDocPreviewStatisticsService;
import com.rzdata.process.service.IVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wangyang
 * @date: 2022年1月21日14:07:16
 */
@Validated
@Slf4j
@Api(value = "文件预览控制器", tags = {"文件预览管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-preview")
public class DocPreviewController {

    @Resource
    private IDocPreviewService docPreviewService;

    @Resource
    private ConfigService configService;
    /**
     * 用于查文件
     */
    @Resource
    private IBasicFileService iBasicFileService;

    /**
     * 用于查文件
     */
    @Resource
    private IVersionService versionService;
    @Resource
    private IDocPreviewStatisticsService docPreviewStatisticsService;

    //TODO 写一个大的before？ 指定预览还是编辑？重复调用before编辑要更新redis缓存
    // viewer? editor?

    /**
     * 根据传入的文档路径预览文档,返回真正的预览地址："/view/{documentKey}"
     *
     * @param id 文档文件主键id
     */
    @ApiOperation(value = "预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "文档文件主键id")
    })
    @GetMapping("/view")
    public AjaxResult beforeView(@RequestParam(value = "id") String id, @RequestParam(value = "mode", defaultValue = "view") String mode) {

        BasicFileVo voById = iBasicFileService.getVoById(id);
        if (ObjectUtils.isEmpty(voById)) {
            return AjaxResult.error(PreviewErrorEnum.DOC_FILE_NOT_EXISTS.getMsg());
        }
        /**
         * 为了保密，此处先返回一个无明确规则地址，再由该地址呈现预览
         */
        String path = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH) + File.separator + voById.getFilePath();
        String documentKey = docPreviewService.buildDocument(path, voById.getFileName(), mode);
        DocPreviewBo docPreviewBo = docPreviewService.getDocument(documentKey);
        //Fixme 返回数据给调用方（可以添加过期时间,但是现在还没有做）
        Map<String, Object> data = new HashMap<>();
        data.put("document", docPreviewBo);
        DocPreviewEditorConfigBo editorConfig = DocPreviewEditorConfigBo.builder()
                .callbackUrl(String.format(DocConstants.OFFICE_API_CALLBACK, docPreviewService.getServerHost()))
                .mode(mode)
                .user(SecurityUtils.getLoginUser())
                .build();
        data.put("editorConfig", editorConfig);
        return AjaxResult.success(data);
    }

    /**
     * 根据传入的文档路径预览文档,返回真正的预览地址："/view/{documentKey}"
     *
     * @param id 文档文件主键id
     */
    @ApiOperation(value = "预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "文档文件version主键id")
    })
    @GetMapping("/view-by-version")
    public AjaxResult viewByVersion(@RequestParam(value = "id") String id, @RequestParam("mode") String mode) {

        VersionVo versionVo = versionService.getVoById(id);
        if (ObjectUtils.isEmpty(versionVo)) {
            return AjaxResult.error("未找到该版本文件", null);

        }
        String basicFileId = null;
        //优先级 签章文件>合稿文件>编制文件
        if (ObjectUtil.isNotEmpty(versionVo.getFileId())) {
            basicFileId = versionVo.getFileId();
        }
        if (ObjectUtil.isNotEmpty(versionVo.getMergeFileId())) {
            basicFileId = versionVo.getMergeFileId();
        }
        if (ObjectUtil.isNotEmpty(versionVo.getEncryptFileId())) {
            basicFileId = versionVo.getEncryptFileId();
        }
        BasicFileVo voById = iBasicFileService.getVoById(basicFileId);
        if (ObjectUtils.isEmpty(voById)) {
            return AjaxResult.error(PreviewErrorEnum.DOC_FILE_NOT_EXISTS.getMsg());
        }
        /**
         * 为了保密，此处先返回一个无明确规则地址，再由该地址呈现预览
         */
        String path = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH) + File.separator + voById.getFilePath();
        String documentKey = docPreviewService.buildDocument(path, voById.getFileName(), mode);
        DocPreviewBo docPreviewBo = docPreviewService.getDocument(documentKey);
        //Fixme 返回数据给调用方（可以添加过期时间,但是现在还没有做）
        Map<String, Object> data = new HashMap<>();
        data.put("document", docPreviewBo);
        DocPreviewEditorConfigBo editorConfig = DocPreviewEditorConfigBo.builder()
                .callbackUrl(String.format(DocConstants.OFFICE_API_CALLBACK, docPreviewService.getServerHost()))
                .mode(ObjectUtil.isNotEmpty(mode) ? mode : "edit")
                .user(SecurityUtils.getLoginUser())
                .build();
        data.put("editorConfig", editorConfig);
        return AjaxResult.success(data);
    }

    /**
     * @param path
     * @param name
     * @return
     */
    @ApiOperation(value = "编辑")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "文档标题")
    })
    @GetMapping("/before/edit")
    public AjaxResult beforeEdit(@RequestParam String path, @RequestParam(required = false) String name) {
        String documentKey = docPreviewService.buildDocument(path, name, "edit");
        Map<String, String> data = new HashMap<>();
        // TODO 如果存在 before 方法，应该在此阻止pdf编辑请求
        data.put("url", String.format(DocConstants.OFFICE_EDITOR, docPreviewService.getServerHost(), documentKey));
        return AjaxResult.success(data);
    }

    /**
     * 获取文件实体（下载）
     * @param documentKey
     */
    /**
     * 不生成swagger文档（onlyOffice document server 调用）
     */
    @ApiIgnore
    @GetMapping("/file/{documentKey}")
    public void getDocFile(@PathVariable String documentKey, HttpServletRequest request, HttpServletResponse response) throws IOException {
        docPreviewService.downloadDocumentFile(documentKey, request, response);
    }

    /**
     * 编辑后回调--保存文件实体
     * <p>onlyoffice在编辑后关闭文件的时候，会回调该接口</p>
     * @param callback
     */
    /**
     * 不生成swagger文档（onlyOffice document server 调用）
     */
    @ApiIgnore
    @PostMapping("/callback")
    public DocPreviewEditCallbackResponseBo saveDocumentFile(@RequestBody DocPreviewEditCallbackBo callback) throws IOException {
        log.info("编辑后回调, 回调信息：{}", JSON.toJSONString(callback));
        // 需要保存时写出文件
        if (callback.getStatus() == DocStatusEnum.READY_FOR_SAVING.getCode() ||
                callback.getStatus() == DocStatusEnum.BEING_EDITED_STATE_SAVED.getCode() /*||
                callback.getStatus() == DocStatusEnum.BEING_EDITED_STATE_SAVED.getCode()*/) {
            log.info("开始保存文件。。。");
            boolean success = docPreviewService.saveDocumentFile(callback.getKey(), callback.getUrl());
            log.info("保存文件结束！");
            if (!success) {
                return DocPreviewEditCallbackResponseBo.failue();
            } else {
                log.info("文件保存成功");
                Map<String, String> data = new HashMap<>();
                data.put("url", callback.getUrl());
                data.put("documentKey", callback.getKey());

                DocPreviewEditCallbackResponseBo<Map<String, String>> callbackResponse = DocPreviewEditCallbackResponseBo.success(data);
                log.info("返回的结果:" + JSON.toJSONString(callbackResponse));

                return callbackResponse;
            }
        }
        return DocPreviewEditCallbackResponseBo.success();
    }

    @ApiOperation(value = "获取文档信息")
    @ApiImplicitParam(name = "documentKey", value = "文档 key")
    @GetMapping("/doc/{documentKey}")
    public AjaxResult docData(@PathVariable String documentKey) {
        return AjaxResult.success(docPreviewService.getDocument(documentKey));
    }

    /**
     * 个人预览数据统计
     */
    @ApiOperation(value = "个人预览")
    @GetMapping("/personal-preview")
    AjaxResult<List<DocStatisticsRecDisVo>> insertPreviewStatist(String id, PreviewSourceEnum source) {
        return docPreviewStatisticsService.insertPreviewStatist(id, source);
        //return AjaxResult.success("操作成功",null);
    }

}
