package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.DistributeItemBo;
import com.rzdata.process.domain.bo.UpdateDistributeBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import com.rzdata.process.service.IDistributeItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 文件分发记录条目Controller
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Validated
@Api(value = "文件分发记录条目控制器", tags = {"文件分发记录条目管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/distributeItem")
public class DistributeItemController extends BaseController {

    private final IDistributeItemService iDistributeItemService;

    /**
     * 查询文件分发记录条目列表
     */
    @ApiOperation("查询文件分发记录条目列表")
    @GetMapping("/list")
    public TableDataInfo<DistributeItemVo> list(@Validated(QueryGroup.class) DistributeItemBo bo) {
        return iDistributeItemService.queryPageList(bo);
    }

    /**
     * 查询文件分发记录条目列表
     */
    @ApiOperation("查询文件分发记录条目列表")
    @GetMapping("/listByAppid")
    public TableDataInfo<DistributeItemVo> listByDisId(@Validated(QueryGroup.class) DistributeItemBo bo) {
        return iDistributeItemService.listByAppid(bo);
    }

    /**
     * 查询文件分发记录条目列表
     */
    @ApiOperation("查询文件分发记录条目列表")
    @GetMapping("/listByDisId")
    public TableDataInfo<DistributeItemVo> listByDocId(@Validated(QueryGroup.class) DistributeItemBo bo) {
        return iDistributeItemService.listByDisId(bo);
    }

    /**
     * 查询文件回收分发记录条目列表
     */
    @ApiOperation("查询文件分发记录条目列表")
    @GetMapping("/listRecoveryByAppid")
    public TableDataInfo<DistributeItemVo> listRecoveryByAppid(@Validated(QueryGroup.class) DistributeItemBo bo) {
        return iDistributeItemService.listRecoveryByAppid(bo);
    }

    /**
     * 导出文件分发记录条目列表
     */
    @ApiOperation("导出文件分发记录条目列表")
    @Log(title = "文件分发记录条目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated DistributeItemBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        List<DistributeItemVo> list = iDistributeItemService.listByDisId(bo).getRows();
        ExcelUtil.exportExcel(list, "文件分发记录条目", DistributeItemVo.class, response);
    }

    /**
     * 获取文件分发记录条目详细信息
     */
    @ApiOperation("获取文件分发记录条目详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DistributeItemVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDistributeItemService.queryById(id));
    }

    /**
     * 新增文件分发记录条目
     */
    /*@ApiOperation("新增文件分发记录条目")
    @Log(title = "文件分发记录条目", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DistributeItemBo bo) {
        return toAjax(iDistributeItemService.insertByBo(bo) ? 1 : 0);
    }*/

    /**
     * 修改文件分发记录条目
     */
    @ApiOperation("修改文件分发记录条目")
    @Log(title = "文件分发记录条目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DistributeItemBo bo) {
        return toAjax(iDistributeItemService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分发记录条目
     */
    @ApiOperation("删除文件分发记录条目")
    @Log(title = "文件分发记录条目" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDistributeItemService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 更新数据,回收明细
     */
    @ApiOperation("更新数据")
    @PostMapping("/updateByIds")
    public AjaxResult<Void> updateByIds(@RequestBody UpdateDistributeBo bo) {
        iDistributeItemService.updateByIds(bo.getIds(), bo.getType(), bo.getStatus(), bo.getId());
        return AjaxResult.success();
    }
}
