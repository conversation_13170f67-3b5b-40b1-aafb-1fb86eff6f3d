package com.rzdata.process.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

import cn.hutool.core.bean.BeanUtil;
import com.rzdata.process.domain.vo.VersionExportVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.service.IVersionService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件版本记录Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "文件版本记录控制器", tags = {"文件版本记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/version")
public class VersionController extends BaseController {

    private final IVersionService iVersionService;

    /**
     * 查询文件版本记录列表
     */
    @ApiOperation("查询文件版本记录列表")
    @GetMapping("/list")
    public TableDataInfo<VersionVo> list(@Validated(QueryGroup.class) VersionBo bo) {
        //记录文件查询
        if("REC".equals(bo.getLinkType())) {
            bo.setDocClass(bo.getLinkType());
            return iVersionService.selectRecordFileCompany(bo);
        }else {
            return iVersionService.queryPageList(bo);
        }

    }

    /**
     * 查询记录文件列表
     */
    @ApiOperation("查询记录文件列表")
    @GetMapping("/page/record")
    public TableDataInfo<VersionVo> selectRecordFile(@Validated(QueryGroup.class) VersionBo bo) {
        return iVersionService.selectRecordFile(bo);
    }

    /**
     * 查询部门文件列表
     */
    @ApiOperation("查询部门文件列表")
    @GetMapping("/page/dept")
    public TableDataInfo<VersionVo> selectDeptFile(@Validated(QueryGroup.class) VersionBo bo) {
        return iVersionService.selectDeptFile(bo);
    }

    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出公司文件列表数据")
    @Log(title = "导出公司文件列表数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportForList")
    public void exportForList(@Validated VersionBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        List<VersionExportVo> listVo = BeanUtil.copyToList(list(bo).getRows(),VersionExportVo.class);

        for (VersionExportVo versionExportVo : listVo) {
            if("1".equals(versionExportVo.getStatus())) {
                versionExportVo.setStatus("生效中");
            }else {
                versionExportVo.setStatus("失效中");
            }
        }
        ExcelUtil.exportExcel(listVo,"导出公司文件", VersionExportVo.class, response);
    }

    /**
     * 导出文件版本记录列表
     */
    @ApiOperation("导出文件版本记录列表")
    @Log(title = "文件版本记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated VersionBo bo, HttpServletResponse response) {
        List<VersionVo> list = iVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件版本记录", VersionVo.class, response);
    }

    /**
     * 获取文件版本记录详细信息
     */
    @ApiOperation("获取文件版本记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<VersionVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iVersionService.queryById(id));
    }

    /**
     * 新增文件版本记录
     */
    @ApiOperation("新增文件版本记录")
    @Log(title = "文件版本记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody VersionBo bo) {
        return toAjax(iVersionService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件版本记录
     */
    @ApiOperation("修改文件版本记录")
    @Log(title = "文件版本记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody VersionBo bo) {
        return toAjax(iVersionService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件版本记录
     */
    @ApiOperation("删除文件版本记录")
    @Log(title = "文件版本记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iVersionService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
