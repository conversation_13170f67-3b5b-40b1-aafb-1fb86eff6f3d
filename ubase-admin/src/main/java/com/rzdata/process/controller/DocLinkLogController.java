package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.process.domain.DocVersionLink;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.service.IDocVersionLinkService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.service.IDocLinkLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件关联记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Validated
@Api(value = "文件关联记录控制器", tags = {"文件关联记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/linkLog")
public class DocLinkLogController extends BaseController {

    private final IDocLinkLogService iDocLinkLogService;

    private final IDocVersionLinkService iDocVersionLinkService;

    /**
     * 查询文件关联记录列表
     */
    @ApiOperation("查询文件关联记录列表")
    @GetMapping("/list")
    public TableDataInfo<DocLinkLogVo> list(@Validated(QueryGroup.class) DocLinkLogBo bo) {
        return iDocLinkLogService.queryPageList(bo);
    }

    /**
     * 导出文件关联记录列表
     */
    @ApiOperation("导出文件关联记录列表")
    @Log(title = "文件关联记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated DocLinkLogBo bo, HttpServletResponse response) {
        List<DocLinkLogVo> list = iDocLinkLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件关联记录", DocLinkLogVo.class, response);
    }

    /**
     * 获取文件关联记录详细信息
     */
    @ApiOperation("获取文件关联记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocLinkLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocLinkLogService.queryById(id));
    }

    /**
     * 新增文件关联记录
     */
    @ApiOperation("新增文件关联记录")
    @Log(title = "文件关联记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocLinkLogBo bo) {
        return toAjax(iDocLinkLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件关联记录
     */
    @ApiOperation("修改文件关联记录")
    @Log(title = "文件关联记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocLinkLogBo bo) {
        return toAjax(iDocLinkLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件关联记录
     */
    @ApiOperation("删除文件关联记录")
    @Log(title = "文件关联记录" , businessType = BusinessType.DELETE)
    @GetMapping("/delete/{id}")
    public AjaxResult<Void> delete(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable("id") String id) {
        return toAjax(iDocLinkLogService.removeById(id) && iDocVersionLinkService.remove(new LambdaUpdateWrapper<DocVersionLink>().eq(DocVersionLink::getLinkId,id)));
    }

    /**
     * 删除文件关联记录
     */
    @ApiOperation("删除文件关联记录")
    @Log(title = "文件关联记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocLinkLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
