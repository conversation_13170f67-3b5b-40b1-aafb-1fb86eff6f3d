package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ModifyApplyDistributeVo;
import com.rzdata.process.domain.bo.ModifyApplyDistributeBo;
import com.rzdata.process.service.IModifyApplyDistributeService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件变更操作申请分发Controller
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Validated
@Api(value = "文件变更操作申请分发控制器", tags = {"文件变更操作申请分发管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/modifyApplyDistribute")
public class ModifyApplyDistributeController extends BaseController {

    private final IModifyApplyDistributeService iModifyApplyDistributeService;

    /**
     * 查询文件变更操作申请分发列表
     */
    @ApiOperation("查询文件变更操作申请分发列表")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:list')")
    @GetMapping("/list")
    public TableDataInfo<ModifyApplyDistributeVo> list(@Validated(QueryGroup.class) ModifyApplyDistributeBo bo) {
        return iModifyApplyDistributeService.queryPageList(bo);
    }

    /**
     * 导出文件变更操作申请分发列表
     */
    @ApiOperation("导出文件变更操作申请分发列表")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:export')")
    @Log(title = "文件变更操作申请分发", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ModifyApplyDistributeBo bo, HttpServletResponse response) {
        List<ModifyApplyDistributeVo> list = iModifyApplyDistributeService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更操作申请分发", ModifyApplyDistributeVo.class, response);
    }

    /**
     * 获取文件变更操作申请分发详细信息
     */
    @ApiOperation("获取文件变更操作申请分发详细信息")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:query')")
    @GetMapping("/{id}")
    public AjaxResult<ModifyApplyDistributeVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyDistributeService.queryById(id));
    }

    /**
     * 新增文件变更操作申请分发
     */
    @ApiOperation("新增文件变更操作申请分发")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:add')")
    @Log(title = "文件变更操作申请分发", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ModifyApplyDistributeBo bo) {
        return toAjax(iModifyApplyDistributeService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件变更操作申请分发
     */
    @ApiOperation("修改文件变更操作申请分发")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:edit')")
    @Log(title = "文件变更操作申请分发", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ModifyApplyDistributeBo bo) {
        return toAjax(iModifyApplyDistributeService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请分发
     */
    @ApiOperation("删除文件变更操作申请分发")
    @PreAuthorize("@ss.hasPermi('process:modifyApplyDistribute:remove')")
    @Log(title = "文件变更操作申请分发" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iModifyApplyDistributeService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
