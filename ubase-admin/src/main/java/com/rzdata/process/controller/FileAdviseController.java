package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.FileAdviseVo;
import com.rzdata.process.domain.bo.FileAdviseBo;
import com.rzdata.process.service.IFileAdviseService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件建议Controller
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Validated
@Api(value = "文件建议控制器", tags = {"文件建议管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/fileAdvise")
public class FileAdviseController extends BaseController {

    private final IFileAdviseService iFileAdviseService;

    /**
     * 查询文件建议列表
     */
    @ApiOperation("查询文件建议列表")
    @GetMapping("/list")
    public TableDataInfo<FileAdviseVo> list(@Validated(QueryGroup.class) FileAdviseBo bo) {
        return iFileAdviseService.queryPageList(bo);
    }

    /**
     * 导出文件建议列表
     */
    @ApiOperation("导出文件建议列表")
    @Log(title = "文件建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated FileAdviseBo bo, HttpServletResponse response) {
        List<FileAdviseVo> list = iFileAdviseService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件建议", FileAdviseVo.class, response);
    }

    /**
     * 获取文件建议详细信息
     */
    @ApiOperation("获取文件建议详细信息")
    @GetMapping("/{id}")
    public AjaxResult<FileAdviseVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iFileAdviseService.queryById(id));
    }

    /**
     * 新增文件建议
     */
    @ApiOperation("新增文件建议")
    @Log(title = "文件建议", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody FileAdviseBo bo) {
        return toAjax(iFileAdviseService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件建议
     */
    @ApiOperation("修改文件建议")
    @Log(title = "文件建议", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody FileAdviseBo bo) {
        return toAjax(iFileAdviseService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件建议
     */
    @ApiOperation("删除文件建议")
    @Log(title = "文件建议" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iFileAdviseService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
