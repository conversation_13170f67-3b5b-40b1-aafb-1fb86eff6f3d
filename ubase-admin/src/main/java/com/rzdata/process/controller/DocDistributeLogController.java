package com.rzdata.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import com.rzdata.process.service.IDocDistributeLogService;
import com.rzdata.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件分发记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Validated
@Api(value = "文件台账控制器", tags = {"文件台账管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/distributeLog")
public class DocDistributeLogController extends BaseController {

    private final IDocDistributeLogService iDocDistributeLogService;

    @Resource
    private ISysUserService sysUserService;

    /**
     * 查询文件分发记录列表
     */
    @ApiOperation("查询文件台账记录列表")
    @GetMapping("/list")
    public TableDataInfo<DocDistributeLogVo> list(@Validated(QueryGroup.class) DocDistributeLogBo bo) {
        return iDocDistributeLogService.queryPageList(bo);
    }


    /**
     * 查询文件分发记录列表,签收菜单
     */
    @ApiOperation("签收菜单查询文件分发记录列表")
    @GetMapping("/listForSign")
    @DataScope(deptAlias = "d")
    public TableDataInfo<DocDistributeLogVo> listForSign(@Validated(QueryGroup.class) DocDistributeLogBo bo) {
        //doc_distribute_log的分发部门
        bo.setDistributeDeptId(SecurityUtils.getDeptId());
        //bo.setSource("sign");
        TableDataInfo<DocDistributeLogVo> docDistributeLogVoTableDataInfo = iDocDistributeLogService.queryPageListForSign(bo);
        return docDistributeLogVoTableDataInfo;
    }

    /**
     * 查询文件分发记录列表,查询文件打印文件
     */
    @ApiOperation("打印菜单查询文件分发记录列表")
    @GetMapping("/listForPrint")
    public TableDataInfo<DocDistributeLogVo> listForPrint(@Validated(QueryGroup.class) DocDistributeLogBo bo) {
        //doc_distribute_log的分发部门
        //bo.setSource("print");
        bo.setDistributeDeptId(SecurityUtils.getDeptId());
        return iDocDistributeLogService.queryPageList(bo);
    }

    /**
     * 查询文件分发记录列表
     */
    @ApiOperation("查询文件分发记录列表")
    @GetMapping("/listNoProcess")
    public TableDataInfo<DocDistributeLogVo> listNoProcess(@Validated(QueryGroup.class) DocDistributeLogBo bo) {
        return iDocDistributeLogService.queryNoProcessList(bo);
    }


    /**
     * 导出文件分发记录列表
     */
    @ApiOperation("导出文件分发记录列表")
    @Log(title = "文件分发记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated DocDistributeLogBo bo, HttpServletResponse response) {
        List<DocDistributeLogVo> list = iDocDistributeLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件分发记录", DocDistributeLogVo.class, response);
    }

    /**
     * 获取文件分发记录详细信息
     */
    @ApiOperation("获取文件分发记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocDistributeLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocDistributeLogService.queryById(id));
    }

    /**
     * 获取文件分发记录详细信息
     */
    @ApiOperation("根据app_id获取文件分发记录详细信息")
    @GetMapping("/appid/{appid}")
    public AjaxResult<List<DistributeItemVo>> getInfoByAppid(@ApiParam("主键")
                                                             @NotNull(message = "主键不能为空")
                                                             @PathVariable("appid") String id) {
        //Map<String,Object> params = new HashMap<>();
        //params.put("appid",id);
        List<DistributeItemVo> DistributeItemVos = iDocDistributeLogService.queryDocDistributeLogVoByDisId(id);
        return AjaxResult.success(DistributeItemVos);
    }

    /**
     * 新增文件分发记录
     */
    @ApiOperation("新增文件分发记录")
    @Log(title = "文件分发记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocDistributeLogBo bo) {
        return toAjax(iDocDistributeLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件分发记录
     */
    @ApiOperation("修改文件分发记录")
    @Log(title = "文件分发记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocDistributeLogBo bo) {
        return toAjax(iDocDistributeLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分发记录
     */
    @ApiOperation("删除文件分发记录")
    @Log(title = "文件分发记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iDocDistributeLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation("签收文件")
    @GetMapping("/sign/{id}")
    public AjaxResult<Void> sign(@PathVariable("id") String id) {
        iDocDistributeLogService.signeFile(id);
        return AjaxResult.success();
    }

    @ApiOperation("本部门文件")
    @GetMapping("/queryPageByThisDept")
    public TableDataInfo<DocDistributeLogVo> queryPageByThisDept(DocDistributeLogBo bo) {
        return iDocDistributeLogService.queryPageByThisDept(bo);
    }
    @ApiOperation("外部门文件")
    @GetMapping("/queryPageByOtherDept")
    public TableDataInfo<DocDistributeLogVo> queryPageByOtherDept(DocDistributeLogBo bo) {
        return iDocDistributeLogService.queryPageByOtherDept(bo);
    }

    @ApiOperation("是否已签收")
    @GetMapping("/checkSign")
    public AjaxResult<Boolean> checkSign(@Param("docId") String docId,@Param("versionId") String versionId) {
        LambdaQueryWrapper<DocDistributeLog> queryWrapper = Wrappers.lambdaQuery(DocDistributeLog.class);
        queryWrapper.eq(DocDistributeLog::getDocId,docId);
        queryWrapper.eq(DocDistributeLog::getVersionId,versionId);
        //查未签收的数据 1=已签收 0=未签收
        queryWrapper.eq(DocDistributeLog::getReceiveStatus,'0');
        List<DocDistributeLog> list = iDocDistributeLogService.list(queryWrapper);
        //未签收数量=0 表示已经全部签收完了
        if (list.size()==0){
            return AjaxResult.success(Boolean.TRUE);
        }else {
            List<String> notSignDeptNameList = list.stream().map(o -> o.getDeptName()).collect(Collectors.toList());
            return AjaxResult.success(notSignDeptNameList +"还未签收,请签收后再进行操作",Boolean.FALSE);
        }
    }
}
