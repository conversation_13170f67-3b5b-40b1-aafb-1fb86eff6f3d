package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.domain.bo.WorkflowLogBo;
import com.rzdata.process.service.IWorkflowLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 流程审批记录Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "流程审批记录控制器", tags = {"流程审批记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/workflowLog")
public class WorkflowLogController extends BaseController {

    private final IWorkflowLogService iWorkflowLogService;

    /**
     * 查询流程审批记录列表
     */
    @ApiOperation("查询流程审批记录列表")
    @GetMapping("/list")
    public TableDataInfo<WorkflowLogVo> list(@Validated(QueryGroup.class) WorkflowLogBo bo) {
        return iWorkflowLogService.queryPageList(bo);
    }

    /**
     * 导出流程审批记录列表
     */
    @ApiOperation("导出流程审批记录列表")
    @Log(title = "流程审批记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated WorkflowLogBo bo, HttpServletResponse response) {
        List<WorkflowLogVo> list = iWorkflowLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "流程审批记录", WorkflowLogVo.class, response);
    }

    /**
     * 获取流程审批记录详细信息
     */
    @ApiOperation("获取流程审批记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<WorkflowLogVo> getInfo(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("id") String id) {
        return AjaxResult.success(iWorkflowLogService.queryById(id));
    }

    /**
     * 新增流程审批记录
     */
    @ApiOperation("新增流程审批记录")
    @Log(title = "流程审批记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody WorkflowLogBo bo) {
        return toAjax(iWorkflowLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改流程审批记录
     */
    @ApiOperation("修改流程审批记录")
    @Log(title = "流程审批记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody WorkflowLogBo bo) {
        return toAjax(iWorkflowLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除流程审批记录
     */
    @ApiOperation("删除流程审批记录")
    @Log(title = "流程审批记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iWorkflowLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation("根据业务Id获取流程记录")
    @GetMapping("/selectLogByBusinessId")
    public AjaxResult<List<WorkflowLogVo>> selectLogByBusinessId(@RequestParam("businessId") String businessId) {
        return AjaxResult.success(iWorkflowLogService.selectLogByBusinessId(businessId));
    }


}
