package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ReadLogVo;
import com.rzdata.process.domain.bo.ReadLogBo;
import com.rzdata.process.service.IReadLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件阅览记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Validated
@Api(value = "文件阅览记录控制器", tags = {"文件阅览记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/readLog")
public class ReadLogController extends BaseController {

    private final IReadLogService iReadLogService;

    /**
     * 查询文件阅览记录列表
     */
    @ApiOperation("查询文件阅览记录列表")
    @PreAuthorize("@ss.hasPermi('process:readLog:list')")
    @GetMapping("/list")
    public TableDataInfo<ReadLogVo> list(@Validated(QueryGroup.class) ReadLogBo bo) {
        return iReadLogService.queryPageList(bo);
    }

    /**
     * 导出文件阅览记录列表
     */
    @ApiOperation("导出文件阅览记录列表")
    @PreAuthorize("@ss.hasPermi('process:readLog:export')")
    @Log(title = "文件阅览记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ReadLogBo bo, HttpServletResponse response) {
        List<ReadLogVo> list = iReadLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件阅览记录", ReadLogVo.class, response);
    }

    /**
     * 获取文件阅览记录详细信息
     */
    @ApiOperation("获取文件阅览记录详细信息")
    @PreAuthorize("@ss.hasPermi('process:readLog:query')")
    @GetMapping("/{id}")
    public AjaxResult<ReadLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iReadLogService.queryById(id));
    }

    /**
     * 新增文件阅览记录
     */
    @ApiOperation("新增文件阅览记录")
    @PreAuthorize("@ss.hasPermi('process:readLog:add')")
    @Log(title = "文件阅览记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ReadLogBo bo) {
        return toAjax(iReadLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件阅览记录
     */
    @ApiOperation("修改文件阅览记录")
    @PreAuthorize("@ss.hasPermi('process:readLog:edit')")
    @Log(title = "文件阅览记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ReadLogBo bo) {
        return toAjax(iReadLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件阅览记录
     */
    @ApiOperation("删除文件阅览记录")
    @PreAuthorize("@ss.hasPermi('process:readLog:remove')")
    @Log(title = "文件阅览记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iReadLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
