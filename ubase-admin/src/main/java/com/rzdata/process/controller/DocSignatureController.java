package com.rzdata.process.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.QysCallbackBo;
import com.rzdata.process.service.IDocSignatureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/1/26 17:25
 * @Version 1.0
 * @Description 文档签章控制器
 */
@Validated
@Slf4j
@Api(value = "文档签章控制器", tags = {"文档签章管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-signature")
public class DocSignatureController {


    /**
     * 文件签章服务类
     */
    @Resource
    private IDocSignatureService docSignatureService;

    /**
     * 文件签章
     * @param id 文档文件主键id
     */
    @ApiOperation(value="签章")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "文档文件主键id" ,required = true),@ApiImplicitParam(name = "docNo", value = "文件编号",required = true)})
    @GetMapping("/signature")
    public AjaxResult signature(String id,String docNo,String applyId) {
        return docSignatureService.signature(id,docNo,applyId);
    }


    /**
     * 契约锁回调函数
     * @param qysCallback 契约锁回调函数
     */
    @ApiIgnore
    @PostMapping("/signature/callback")
    public AjaxResult callBack(QysCallbackBo qysCallback) {
        return docSignatureService.callBack(qysCallback);
    }
}
