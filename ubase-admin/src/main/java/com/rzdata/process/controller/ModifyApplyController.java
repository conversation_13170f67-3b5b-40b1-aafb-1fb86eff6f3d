package com.rzdata.process.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.domain.vo.SysUserExportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件变更操作申请Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件变更操作申请控制器", tags = {"文件变更操作申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/modifyApply")
public class ModifyApplyController extends BaseController {

    private final IModifyApplyService iModifyApplyService;

    private final IDocClassService iDocClassService;

    /**
     * 查询文件变更操作申请列表
     */
    @ApiOperation("查询文件变更操作申请列表")
    @GetMapping("/list")
    public TableDataInfo<ModifyApplyVo> list(ModifyApplyBo bo) {
        return iModifyApplyService.queryPageList(bo);
    }

    /**
     * 导出文件变更操作申请列表
     */
    @ApiOperation("导出文件变更操作申请列表")
    @Log(title = "文件变更操作申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ModifyApplyBo bo, HttpServletResponse response) {
        List<ModifyApplyVo> list = iModifyApplyService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更操作申请", ModifyApplyVo.class, response);
    }

    /**
     * 导出文件变更列表
     */
    @ApiOperation("导出文件变更列表")
    @Log(title = "文件变更导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportForChange")
    public void exportForChange(@Validated ModifyApplyBo bo, HttpServletResponse response) {
        bo.setPageSize(Integer.MAX_VALUE);
        List<ModifyApplyVo> list = this.list(bo).getRows();
        List<ModifyApplyExportVo> listVo = BeanUtil.copyToList(list, ModifyApplyExportVo.class);

        Map<String, String> map = iDocClassService.list().stream().
                collect(Collectors.toMap(DocClass::getId, DocClass::getClassName));

        for (ModifyApplyExportVo modifyApplyExportVo : listVo) {
            modifyApplyExportVo.setDocClass(map.get(modifyApplyExportVo.getDocClass()));
            if("NEW".equals(modifyApplyExportVo.getProcessStatus())) {
                modifyApplyExportVo.setProcessStatus("进行中");
            }else {
                modifyApplyExportVo.setProcessStatus("已完结");
            }
        }

        ExcelUtil.exportExcel(listVo, "文件变更", ModifyApplyExportVo.class, response);
    }

    /**
     * 获取文件变更操作申请详细信息
     */
    @ApiOperation("获取文件变更操作申请详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ModifyApplyVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyService.queryById(id));
    }

    /**
     * 生效分发
     */
    @ApiOperation("生效分发")
    @GetMapping("/distribute/{id}")
    public AjaxResult<Boolean> distribute(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyService.distribute(id));
    }

    /**
     * 新增文件变更操作申请
     */
    @ApiOperation("新增文件变更操作申请")
    @Log(title = "文件变更操作申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<ModifyApplyResultVo> add(@Validated(AddGroup.class) @RequestBody ModifyApplyBo bo) {
        Optional<ModifyApplyResultVo> resultVoOpt = iModifyApplyService.insertByBo(bo);
        return resultVoOpt.isPresent() ? AjaxResult.success("success", resultVoOpt.get()) : AjaxResult.error("fail", null);
    }

    /**
     * 修改文件变更操作申请
     */
    @ApiOperation("修改文件变更操作申请")
    @Log(title = "文件变更操作申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ModifyApplyBo bo) {
        return toAjax(iModifyApplyService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请
     */
    @ApiOperation("删除文件变更操作申请")
    @Log(title = "文件变更操作申请" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iModifyApplyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请
     */
    @ApiOperation("生成编号")
    @GetMapping("/getDocNoByApplyId/{id}")
    public AjaxResult<DocNoVo> getDocNoByApplyId(@PathVariable("id") String id) {
        return AjaxResult.success("成功",iModifyApplyService.getDocNoByApplyId(id));
    }

    @ApiOperation("判断文件编号是否存在，true:存在，false：不存在")
    @GetMapping("/check_docid")
    public AjaxResult<Boolean> checkDocIdExist(@RequestParam("docId")String docId) {
        return AjaxResult.success("成功",iModifyApplyService.checkDocIdExist(docId));
    }

    @ApiOperation("更新文件编号的流水号")
    @PostMapping("/doc_serialnumber")
    public AjaxResult<Boolean> updateDocSerialNumber(@RequestBody CreateNewNoBo bo) {
        try{
            return AjaxResult.success("成功",
                    iModifyApplyService.updateDocSerialNumber(bo));
        }
        catch(Exception ex){
            return new AjaxResult(500,ex.getMessage());
        }
    }

    @ApiOperation("更新记录文件编号的流水号")
    @PostMapping("/record_serialnumber")
    public AjaxResult<Boolean> updateRecordSerialNumber(@RequestBody List<CreateNewNoBo> listBo) {
        try{
            return AjaxResult.success("成功",
                    iModifyApplyService.updateRecordSerialNumber(listBo));
        }
        catch(Exception ex){
            return new AjaxResult(500,ex.getMessage());
        }
    }

    /**
     * 生成关联记录文件的文件编号
     */
    @ApiOperation("生成记录文件编号")
    @PostMapping("/getRecordDocNoByLinkId")
    public AjaxResult<List<DocNoVo>>
    getRecordDocNoByLinkId(@RequestBody Map<String,String> requestMap) {
        return AjaxResult.success("成功",iModifyApplyService.getRecordDocNoByLinkId(requestMap));
    }
}
