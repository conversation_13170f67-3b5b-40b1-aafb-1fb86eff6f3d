package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.RecoveryLogItemBo;
import com.rzdata.process.domain.vo.RecoveryLogItemVo;
import com.rzdata.process.service.IRecoveryLogItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 文件回收记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Validated
@Api(value = "文件回收记录详情控制器", tags = {"文件回收记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/recoveryLogItem")
public class RecoveryLogItemController extends BaseController {

    private final IRecoveryLogItemService iRecoveryLogItemService;

    /**
     * 查询文件回收记录列表
     */
    @ApiOperation("查询文件回收记录列表")
    @GetMapping("/list")
    public TableDataInfo<RecoveryLogItemVo> list(@Validated(QueryGroup.class) RecoveryLogItemBo bo) {
        return iRecoveryLogItemService.queryPageList(bo);
    }

    /**
     * 查询文件回收记录列表
     */
    @ApiOperation("文件回收")
    @PostMapping("/recovery")
    public AjaxResult recovery(@RequestBody RecoveryLogItemBo bo) {
        return iRecoveryLogItemService.recovery(bo);
    }

    /**
     * 导出文件回收记录列表
     */
    @ApiOperation("导出文件回收记录列表")
    @Log(title = "文件回收记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated RecoveryLogItemBo bo, HttpServletResponse response) {
        TableDataInfo<RecoveryLogItemVo> list = iRecoveryLogItemService.queryPageList(bo);
        bo.setPageSize(Integer.MAX_VALUE);
        ExcelUtil.exportExcel(list.getRows(), "文件回收记录", RecoveryLogItemVo.class, response);
    }

    /**
     * 获取文件回收记录详细信息
     */
    @ApiOperation("获取文件回收记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<RecoveryLogItemVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iRecoveryLogItemService.queryById(id));
    }

    /**
     * 新增文件回收记录
     */
    @ApiOperation("新增文件回收记录")
    @Log(title = "文件回收记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody RecoveryLogItemBo bo) {
        return toAjax(iRecoveryLogItemService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件回收记录
     */
    @ApiOperation("修改文件回收记录")
    @Log(title = "文件回收记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody RecoveryLogItemBo bo) {
        return toAjax(iRecoveryLogItemService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件回收记录
     */
    @ApiOperation("删除文件回收记录")
    @Log(title = "文件回收记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iRecoveryLogItemService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
