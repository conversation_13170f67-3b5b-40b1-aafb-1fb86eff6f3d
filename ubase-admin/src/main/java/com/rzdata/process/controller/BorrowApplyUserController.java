package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.BorrowApplyUserVo;
import com.rzdata.process.domain.bo.BorrowApplyUserBo;
import com.rzdata.process.service.IBorrowApplyUserService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件借阅选择借阅人Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件借阅选择借阅人控制器", tags = {"文件借阅选择借阅人管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/borrowApplyUser")
public class BorrowApplyUserController extends BaseController {

    private final IBorrowApplyUserService iBorrowApplyUserService;

    /**
     * 查询文件借阅选择借阅人列表
     */
    @ApiOperation("查询文件借阅选择借阅人列表")
    @GetMapping("/list")
    public TableDataInfo<BorrowApplyUserVo> list(@Validated(QueryGroup.class) BorrowApplyUserBo bo) {
        return iBorrowApplyUserService.queryPageList(bo);
    }

    /**
     * 导出文件借阅选择借阅人列表
     */
    @ApiOperation("导出文件借阅选择借阅人列表")
    @Log(title = "文件借阅选择借阅人", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BorrowApplyUserBo bo, HttpServletResponse response) {
        List<BorrowApplyUserVo> list = iBorrowApplyUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件借阅选择借阅人", BorrowApplyUserVo.class, response);
    }

    /**
     * 获取文件借阅选择借阅人详细信息
     */
    @ApiOperation("获取文件借阅选择借阅人详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BorrowApplyUserVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iBorrowApplyUserService.queryById(id));
    }

    /**
     * 新增文件借阅选择借阅人
     */
    @ApiOperation("新增文件借阅选择借阅人")
    @Log(title = "文件借阅选择借阅人", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody BorrowApplyUserBo bo) {
        return toAjax(iBorrowApplyUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件借阅选择借阅人
     */
    @ApiOperation("修改文件借阅选择借阅人")
    @Log(title = "文件借阅选择借阅人", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BorrowApplyUserBo bo) {
        return toAjax(iBorrowApplyUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件借阅选择借阅人
     */
    @ApiOperation("删除文件借阅选择借阅人")
    @Log(title = "文件借阅选择借阅人" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iBorrowApplyUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
