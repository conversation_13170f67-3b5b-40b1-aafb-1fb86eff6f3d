package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.PrintLogVo;
import com.rzdata.process.domain.bo.PrintLogBo;
import com.rzdata.process.service.IPrintLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件打印记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Validated
@Api(value = "文件打印记录控制器", tags = {"文件打印记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/printLog")
public class PrintLogController extends BaseController {

    private final IPrintLogService iPrintLogService;

    /**
     * 查询文件打印记录列表
     */
    @ApiOperation("查询文件打印记录列表")
    @GetMapping("/list")
    @DataScope()
    public TableDataInfo<PrintLogVo> list(@Validated(QueryGroup.class) PrintLogBo bo) {
        return iPrintLogService.queryPageList(bo);
    }

    /**
     * 查询文件分发记录列表,查询文件打印文件
     */
    @ApiOperation("打印菜单查询文件分发记录列表")
    @GetMapping("/listForPrint")
    @DataScope()
    public TableDataInfo<PrintLogVo> listForPrint(@Validated(QueryGroup.class) PrintLogBo bo) {
        //doc_distribute_log的分发部门
        //bo.setSource("print");
        //bo.setDeptId(SecurityUtils.getDeptId());
        return iPrintLogService.queryPageList(bo);
    }

    /**
     * 导出文件打印记录列表
     */
    @ApiOperation("导出文件打印记录列表")
    @Log(title = "文件打印记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated PrintLogBo bo, HttpServletResponse response) {
        List<PrintLogVo> list = iPrintLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件打印记录", PrintLogVo.class, response);
    }

    /**
     * 获取文件打印记录详细信息
     */
    @ApiOperation("获取文件打印记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<PrintLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iPrintLogService.queryById(id));
    }

    /**
     * 新增文件打印记录
     */
    @ApiOperation("新增文件打印记录")
    @Log(title = "文件打印记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody PrintLogBo bo) {
        return AjaxResult.success("成功", iPrintLogService.insertByBo(bo));
    }

    /**
     * 修改文件打印记录
     */
    @ApiOperation("修改文件打印记录")
    @Log(title = "文件打印记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody PrintLogBo bo) {
        return toAjax(iPrintLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件打印记录
     */
    @ApiOperation("删除文件打印记录")
    @Log(title = "文件打印记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iPrintLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
