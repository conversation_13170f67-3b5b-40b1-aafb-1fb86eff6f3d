package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.process.domain.bo.ReprintLogBo;
import com.rzdata.process.domain.vo.ReprintLogVo;
import com.rzdata.process.service.IReprintLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文件打印记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Validated
@Api(value = "文件重打记录控制器", tags = {"文件重打记录控制器"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/reprintLog")
public class ReprintLogController extends BaseController {

    private final IReprintLogService iReprintLogService;

    /**
     * 查询文件重打记录列表
     */
    @ApiOperation("查询文件重打记录列表")
    @GetMapping("/list")
    public TableDataInfo<ReprintLogVo> list(@Validated(QueryGroup.class) ReprintLogBo bo) {
        return iReprintLogService.queryPageList(bo);
    }

    /**
     * 新增文件重打记录
     */
    @ApiOperation("新增文件重打记录")
    @Log(title = "文件重打记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody ReprintLogBo bo) {
        boolean success = iReprintLogService.insertByBo(bo);
        if (success){
            return AjaxResult.success("成功",null);
        }
        return AjaxResult.error("新增重打记录失败",null);
    }
}
