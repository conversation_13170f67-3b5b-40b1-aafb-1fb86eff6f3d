package com.rzdata.process.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.PrintLogItem;
import com.rzdata.process.domain.bo.PrintLogItemBo;
import com.rzdata.process.domain.vo.PrintLogItemVo;
import com.rzdata.process.service.IPrintLogItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 文件打印记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Validated
@Api(value = "文件打印详情控制器", tags = {"文件打印详情管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/printLogItem")
public class PrintLogItemController extends BaseController {

    private final IPrintLogItemService iPrintLogItemService;

    /**
     * 查询文件打印记录列表
     */
    @ApiOperation("查询文件打印记录列表")
    @GetMapping("/list")
    //@DataScope()
    public TableDataInfo<PrintLogItem> list(@Validated(QueryGroup.class) PrintLogItemBo bo) {
        return iPrintLogItemService.queryPageList(bo);
    }

    /**
     * 打印文件
     */
    @ApiOperation("打印文件")
    @PostMapping("/print")
    @RepeatSubmit()
    public AjaxResult<Void> print( @RequestBody PrintLogItemBo bo) {
        return iPrintLogItemService.updatePrintStatusById(bo.getIds());
    }

    /**
     * 重打文件
     */
    @ApiOperation("重打文件")
    @PostMapping("/reprint")
    public AjaxResult reprint( @RequestBody PrintLogItemBo bo) {
        return iPrintLogItemService.reprint((bo.getIds()));
    }
    /**
     * 导出文件打印记录列表
     */
    @ApiOperation("导出文件打印记录列表")
    @Log(title = "文件打印记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated PrintLogItemBo bo, HttpServletResponse response) {
        //bo.setPageSize(Integer.MAX_VALUE);
        List<PrintLogItemVo> list = iPrintLogItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件打印记录", PrintLogItemVo.class, response);
    }

    /**
     * 获取文件打印记录详细信息
     */
    @ApiOperation("获取文件打印记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<PrintLogItemVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iPrintLogItemService.queryById(id));
    }

    /**
     * 新增文件打印记录
     */
    @ApiOperation("新增文件打印记录")
    @Log(title = "文件打印记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody PrintLogItemBo bo) {
        return AjaxResult.success("成功", iPrintLogItemService.insertByBo(bo));
    }

    /**
     * 修改文件打印记录
     */
    @ApiOperation("修改文件打印记录")
    @Log(title = "文件打印记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody PrintLogItemBo bo) {
        return toAjax(iPrintLogItemService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件打印记录
     */
    @ApiOperation("删除文件打印记录")
    @Log(title = "文件打印记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iPrintLogItemService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
