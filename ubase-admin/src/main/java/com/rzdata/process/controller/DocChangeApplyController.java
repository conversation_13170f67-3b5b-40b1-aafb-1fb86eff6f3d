package com.rzdata.process.controller;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.DocChangeApplyBo;
import com.rzdata.process.domain.vo.DocChangeApplyDetailVo;
import com.rzdata.process.domain.vo.DocChangeApplyVo;
import com.rzdata.process.service.IDocChangeApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 文件变更申请Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件变更申请控制器", tags = {"文件变更申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/changeApply")
public class DocChangeApplyController extends BaseController {

    private final IDocChangeApplyService iDocChangeApplyService;

    /**
     * 查询文件变更申请列表
     */
    @ApiOperation("查询文件变更申请列表")
    @GetMapping("/list")
    public TableDataInfo<DocChangeApplyVo> list(@Validated(QueryGroup.class) DocChangeApplyBo bo) {
        return iDocChangeApplyService.queryPageList(bo);
    }

    /**
     * 导出文件变更申请列表
     */
    @ApiOperation("导出文件变更申请列表")
    @Log(title = "文件变更申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocChangeApplyBo bo, HttpServletResponse response) {
        List<DocChangeApplyVo> list = iDocChangeApplyService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更申请", DocChangeApplyVo.class, response);
    }

    /**
     * 获取文件变更申请详细信息
     */
    @ApiOperation("获取文件变更申请详细信息")
    @GetMapping("/{id}")
    public AjaxResult<DocChangeApplyDetailVo> getInfo(@ApiParam("主键")
                                                      @NotNull(message = "主键不能为空")
                                                      @PathVariable("id") String id) {
        return AjaxResult.success(iDocChangeApplyService.queryById(id));
    }

    /**
     * 新增文件变更申请
     */
    @ApiOperation("新增文件变更申请")
    @Log(title = "文件变更申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<ProcessInstanceModel> add(@Validated(AddGroup.class) @RequestBody DocChangeApplyBo bo) {
        return AjaxResult.success("成功", iDocChangeApplyService.insertByBo(bo));
    }

    /**
     * 修改文件变更申请
     */
    @ApiOperation("修改文件变更申请")
    @Log(title = "文件变更申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocChangeApplyBo bo) {
        return toAjax(iDocChangeApplyService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件变更申请
     */
    @ApiOperation("删除文件变更申请")
    @Log(title = "文件变更申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iDocChangeApplyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
