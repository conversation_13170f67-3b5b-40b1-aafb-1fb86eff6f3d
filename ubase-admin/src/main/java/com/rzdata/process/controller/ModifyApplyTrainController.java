package com.rzdata.process.controller;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.bo.ModifyApplyTrainBo;
import com.rzdata.process.domain.vo.ModifyApplyTrainVo;
import com.rzdata.process.service.IModifyApplyTrainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 文件变更操作申请培训记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Validated
@Api(value = "文件变更操作申请培训记录控制器", tags = {"文件变更操作申请培训记录管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/modifyApplyTrain")
public class ModifyApplyTrainController extends BaseController {

    private final IModifyApplyTrainService iModifyApplyTrainService;

    /**
     * 查询文件变更操作申请培训记录列表
     */
    @ApiOperation("查询文件变更操作申请培训记录列表")
    @GetMapping("/list")
    public TableDataInfo<ModifyApplyTrainVo> list(@Validated(QueryGroup.class) ModifyApplyTrainBo bo) {
        return iModifyApplyTrainService.queryPageList(bo);
    }

    /**
     * 导出文件变更操作申请培训记录列表
     */
    @ApiOperation("导出文件变更操作申请培训记录列表")
    @Log(title = "文件变更操作申请培训记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ModifyApplyTrainBo bo, HttpServletResponse response) {
        List<ModifyApplyTrainVo> list = iModifyApplyTrainService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更操作申请培训记录", ModifyApplyTrainVo.class, response);
    }

    /**
     * 获取文件变更操作申请培训记录详细信息
     */
    @ApiOperation("获取文件变更操作申请培训记录详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ModifyApplyTrainVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyTrainService.queryById(id));
    }

    /**
     * 新增文件变更操作申请培训记录
     */
    @ApiOperation("新增文件变更操作申请培训记录")
    @Log(title = "文件变更操作申请培训记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody ModifyApplyTrainBo bo) {
        Boolean success = iModifyApplyTrainService.insertByBo(bo);
        if (success){
            if (ObjectUtil.isNotEmpty(bo.getId())){
                return AjaxResult.success("新增成功",bo.getId());
            }else {
                return  AjaxResult.success("新增成功",null);
            }
        }else {
            return AjaxResult.error("新增培训文件失败",null);
        }
    }

    /**
     * 修改文件变更操作申请培训记录
     */
    @ApiOperation("修改文件变更操作申请培训记录")
    @Log(title = "文件变更操作申请培训记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ModifyApplyTrainBo bo) {
        return toAjax(iModifyApplyTrainService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请培训记录
     */
    @ApiOperation("删除文件变更操作申请培训记录")
    @Log(title = "文件变更操作申请培训记录" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iModifyApplyTrainService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
