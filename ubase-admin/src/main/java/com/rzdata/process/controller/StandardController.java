package com.rzdata.process.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DictConstants;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.listener.ExcelListener;
import com.rzdata.process.listener.ibo.LinkLogIBo;
import com.rzdata.process.listener.ibo.StandardIBo;
import com.rzdata.process.listener.ivo.StandardIVo;
import com.rzdata.process.mapper.WorkflowApplyLogMapper;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysDictTypeService;
import com.rzdata.system.service.ISysUserService;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准文件Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "标准文件控制器", tags = {"标准文件管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/standard")
public class StandardController extends BaseController {

    private final IStandardService iStandardService;

    private final IBasicFileService iBasicFileService;

    private final IGenerateIdService iGenerateIdService;

    private final IVersionService iVersionService;

    private final ISysDictTypeService dictTypeService;

    private final ISysUserService iSysUserService;

    private final ISysDeptService iSysDeptService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    private WorkflowApplyLogMapper workflowApplyLogMapper;

    /**
     * 查询标准文件列表
     */
    @ApiOperation("查询标准文件列表")
    @GetMapping("/list")
    public TableDataInfo<StandardVo> list(@Validated(QueryGroup.class) StandardBo bo) {
        return iStandardService.selectPageList(bo);
    }


    /**
     * 历史文件初始化上传文件
     */
    @ApiOperation("历史文件初始化上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/upload")
    public AjaxResult<Boolean> uploading(@RequestPart("file") MultipartFile file, @RequestParam("docClass") String docClass) throws Exception{
        if(file.isEmpty()){
            return AjaxResult.success("文件不能为空",Boolean.FALSE);
        }
        Map<String, String> map = iBasicFileService.uploading(file);
        StandardBo bo = new StandardBo();
        bo.setDocClass(docClass);
        String filename = file.getOriginalFilename();
        if(StringUtils.contains(filename,Constants.SLASH)){
            filename = filename.substring(filename.indexOf(Constants.SLASH)+1);
            bo.setDocName(filename);
        }
        if(file.getSize() > 104857600){
            return AjaxResult.success(filename + "文件过太",Boolean.FALSE);
        }
        // 文件类型判断
        if(!StringUtils.endsWithIgnoreCase(filename,"DOC") && !StringUtils.endsWithIgnoreCase(filename,"DOCX") &&
                !StringUtils.endsWithIgnoreCase(filename,"XLS") && !StringUtils.endsWithIgnoreCase(filename,"XLSX") &&
                !StringUtils.endsWithIgnoreCase(filename,"PDF")){
            return AjaxResult.success(filename+"文件格式不对",Boolean.FALSE);
        }

        // 文件名重复判断
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Standard::getDocName,StringUtils.trim(filename));
        long count = iStandardService.count(queryWrapper);
        if (count > 0) {
            return AjaxResult.success(filename + "文件名称重复",Boolean.FALSE);
        }
        bo.setStatus(Constants.ZERO);
        bo.setFileId(map.get(DocConstants.KEY_FILE_ID));
        bo.setInitFile(Constants.ONE);
        iStandardService.insertByBo(bo);
        return  AjaxResult.success(Boolean.TRUE);
    }

    /**
     * 替换文件
     */
    @ApiOperation("替换文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/replace/file")
    public AjaxResult<Void> replaceFile(@RequestPart("file") MultipartFile file,@RequestParam("id") String id,@RequestParam("versionId") String versionId,@RequestParam("docId")String docId) throws Exception{
        if(file.getSize() > 104857600){
            return AjaxResult.error("文件过大");
        }
        if(StringUtils.isNotBlank(versionId)) {
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, docId);
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, versionId);
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return AjaxResult.error("已产生新数据,不可替换");
            }
        }
        String filename = file.getOriginalFilename();
        if(StringUtils.contains(filename,Constants.SLASH)){
            filename = filename.substring(filename.indexOf(Constants.SLASH)+1);
        }
        // 文件名重复判断
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Standard::getDocName,StringUtils.trim(filename));
        long count = iStandardService.count(queryWrapper);
        if (count > 0) {
            return AjaxResult.error(filename + "文件名称重复");
        }
        Map<String, String> map = iBasicFileService.uploading(file);
        return toAjax(iStandardService.update(new LambdaUpdateWrapper<Standard>().set(Standard::getDocName,map.get(DocConstants.KEY_FILE_NAME)).set(Standard::getFileId,map.get(DocConstants.KEY_FILE_ID)).eq(Standard::getId,id)));
    }

    /**
     * 导入文件修改数据
     */
    @ApiOperation("导入文件修改数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "标准文件和关联", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/import")
    public AjaxResult<StandardIVo> importData(@RequestPart("file") MultipartFile file) throws Exception{
        StandardIVo ivo = new StandardIVo();
        InputStream inputStream = file.getInputStream();
        if(file.isEmpty() || !StringUtils.endsWith(file.getOriginalFilename(),"xlsx") || StringUtils.endsWith(file.getOriginalFilename(),"xls")){
           return AjaxResult.error("不符合要求，请重新上传",null);
        }
        //实例化实现了AnalysisEventListener接口的类
        ExcelListener listener = new ExcelListener();
        //传入参数
        ExcelReader excelReader = EasyExcel.read(inputStream,listener).build();
        //读取信息
        ReadSheet readSheet1 = EasyExcel.readSheet(0).head(StandardIBo.class).build();
        //读取数据
        excelReader.read(readSheet1);
        //获取数据
        List<Object> list = listener.getDatas();
        if(CollectionUtils.isEmpty(list)){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        for (int i = 1; i < list.size(); i++) {
            if(iStandardService.updateByImport((StandardIBo) list.get(i),i+2,ivo)) {
                ivo.initFileSum();
            }
        }
        list.clear();
        //读取数据
        ReadSheet readSheet2 = EasyExcel.readSheet(1).head(LinkLogIBo.class).build();
        //获取数据
        excelReader.read(readSheet2);

        list = listener.getDatas();
        if(CollectionUtils.isEmpty(list)){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        if (ObjectUtil.isNotEmpty(list)) {
            for (int i = 1; i < list.size(); i++) {
                if(ObjectUtil.isNotNull(list.get(i))) {
                    if (iStandardService.updateLinkLogByImport((LinkLogIBo) list.get(i), LinkTypeEnum.REF_DOC.name(), i + 2, ivo)) {
                        ivo.linkFileSum();
                    }
                }
            }
        }

        list.clear();
        //读取数据
        ReadSheet readSheet3 = EasyExcel.readSheet(2).head(LinkLogIBo.class).build();
        //获取数据
        excelReader.read(readSheet3);

        list = listener.getDatas();
        if(CollectionUtils.isEmpty(list)){
            return AjaxResult.error("不符合要求，请重新上传",null);
        }
        if(ObjectUtil.isNotEmpty(list)) {
            for (int i = 1; i < list.size(); i++) {
                if(ObjectUtil.isNotNull(list.get(i))) {
                    if (iStandardService.updateLinkLogByImport((LinkLogIBo) list.get(i), LinkTypeEnum.RECORD.name(), i + 2, ivo)) {
                        ivo.linkRecordSum();
                    }
                }
            }
        }
        return AjaxResult.success(ivo);
    }


    /**
     * 导出标准文件列表
     */
    @ApiOperation("导出标准文件列表")
    @Log(title = "标准文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated StandardBo bo, HttpServletResponse response) throws IOException {
        String path = "/ubase-admin/templates/xtcshmb.xlsx";
        List<SysDictData> standardStatusList = dictTypeService.selectDictDataByType(DictConstants.STANDARD_STATUS);
        List<SysDictData> changeTypeList = dictTypeService.selectDictDataByType(DictConstants.CHANGE_TYPE);
        List<SysUser> userList = iSysUserService.selectList();
        List<SysDept> deptList = iSysDeptService.selectList();
        Map<String, List<?>> dataMap = new HashMap<>(3);
        //填充sheet1
        List<StandardVo> list = iStandardService.selectList(bo);
        list.parallelStream().forEachOrdered(sv->{
            if (StringUtils.isNotBlank(sv.getStatus())) {
                standardStatusList.stream().filter(i -> i.getDictValue().equals(sv.getStatus())).findFirst().ifPresent(i->sv.setStatus(i.getDictLabel()));
            }
            if (StringUtils.isNotBlank(sv.getChangeType())) {
                changeTypeList.stream().filter(i -> i.getDictValue().equals(sv.getChangeType())).findFirst().ifPresent(i->sv.setChangeType(i.getDictLabel()));
            }
            sv.setDocClass(sv.getClassName());
            if (StringUtils.isBlank(sv.getChangeReason())) {
                sv.setChangeReason(Constants.VALUE_INIT);
            }
            if (StringUtils.isBlank(sv.getContent())) {
                sv.setContent(Constants.VALUE_INIT);
            }
            if (StringUtils.isBlank(sv.getChangeType())) {
                sv.setChangeType(Constants.VALUE_ADD);
            }
            if (StringUtils.isNotBlank(sv.getUserName())) {
                userList.stream().filter(i -> i.getUserName().equals(sv.getUserName())).findFirst().ifPresent(i->sv.setNickName(i.getNickName()));
            }
            if (ObjectUtil.isNotEmpty(sv.getDeptId())) {
                deptList.stream().filter(i -> i.getDeptId().equals(sv.getDeptId())).findFirst().ifPresent(i->sv.setDeptName(i.getDeptName()));
            }
        });
        dataMap.put("0", list);
        //填充sheet2
        bo.setLinkType(LinkTypeEnum.REF_DOC.name());
        List<String> ids = list.stream().map(StandardVo::getId).collect(Collectors.toList());
        List<DocLinkLogVo> list1 =iStandardService.exportLinkLog(bo,ids);
        dataMap.put("1", list1);
        //填充sheet3
        bo.setLinkType(LinkTypeEnum.RECORD.name());
        List<DocLinkLogVo> list2 =iStandardService.exportLinkLog(bo,ids);
        dataMap.put("2", list2);
        ExcelUtil.exportExcel(dataMap, path,response);
    }

    /**
     * 获取标准文件详细信息
     */
    @ApiOperation("获取标准文件详细信息")
    @GetMapping("/{id}")
    public AjaxResult<StandardVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iStandardService.queryById(id));
    }

    /**
     * 获取标准文件全部的详细信息
     */
    @ApiOperation("获取标准文件全部的详细信息")
    @GetMapping("/getDetail")
    public AjaxResult<StandardVo> getDetail(@ApiParam("文档ID") @NotNull(message = "主键不能为空")
                                                        String docId, @ApiParam("版本ID") String versionId, @ApiParam("版本ID") String flag) {
        StandardVo standardVo = iStandardService.queryDetail(docId, versionId, flag);
        return AjaxResult.success(standardVo);
    }


    /**
     * 加入关联记录
     */
    @ApiOperation("加入关联记录")
    @Log(title = "关联记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/join")
    public AjaxResult<Void> join(@RequestBody StandardBo bo) {
        StandardVo standard =iStandardService.queryById(bo.getId());
        if(StringUtils.isBlank(standard.getVersionId())){
            return AjaxResult.error("文件编号不能为空");
        }
        return toAjax(iStandardService.join(bo));
    }

    /**
     * 新增标准文件
     */
    @ApiOperation("新增标准文件")
    @Log(title = "标准文件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody StandardBo bo) {
        return toAjax(iStandardService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改标准文件和版本记录
     */
    @ApiOperation("修改标准文件和版本记录")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/update")
    public AjaxResult<Void> update(@Validated @RequestBody StandardBo bo) {
        StandardVo voById = iStandardService.queryInfoById(bo.getId());

        VersionVo versionVo = iVersionService.selectVersionByDocId(bo.getDocId());
        if(Objects.nonNull(versionVo) && ObjectUtil.isNotNull(voById) && !StringUtils.equals(voById.getDocId(),bo.getDocId())){
            return AjaxResult.error("文件编号重复");
        }

        if(ObjectUtil.isNotNull(voById)) {
            if (StringUtils.isNotBlank(voById.getVersionId()) && StringUtils.isNotBlank(voById.getDocId())) {
                QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, voById.getDocId());
                queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, voById.getVersionId());
                Long count = workflowApplyLogMapper.selectCount(queryWrapper);
                if (count > 0) {
                    return AjaxResult.error("已产生新数据,不可修改");
                }
            }
        }
        // 文件名重复判断
        String filename = bo.getDocName();
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Standard::getDocName,StringUtils.trim(filename));
        Standard byId = iStandardService.getById(bo.getId());
        if(ObjectUtil.isNotNull(byId)) {
            if (StringUtils.equals(filename, byId.getDocName())) {
                queryWrapper.lambda().ne(Standard::getDocName, StringUtils.trim(byId.getDocName()));
            }
        }
        long count = iStandardService.count(queryWrapper);
        if (count > 0) {
            return AjaxResult.error(filename + "文件名称重复");
        }
        return toAjax(iStandardService.updateBoAndVersion(bo) ? 1 : 0);
    }

    /**
     * 修改标准文件
     */
    @ApiOperation("修改标准文件")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody StandardBo bo) {
        return toAjax(iStandardService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 批量生效
     */
    @ApiOperation("批量生效")
    @Log(title = "标准文件", businessType = BusinessType.UPDATE)
    @GetMapping("/update/status/{ids}")
    public AjaxResult<Void> updateStatus(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iStandardService.update(new LambdaUpdateWrapper<Standard>().set(Standard::getStatus,Constants.ONE).in(Standard::getId,ids)));
    }

    @ApiOperation("判断文件名称是否存在 true 存在 false 不存在")
    @GetMapping("/isExistByName/{name}")
    public AjaxResult<Boolean> isExistByName(@PathVariable("name") String name) {
        QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Standard:: getDocName, name);
        queryWrapper.lambda().eq(Standard:: getStatus, NumberConstants.ONE+"");
        return AjaxResult.success(iStandardService.count(queryWrapper) > 0 ? true : false);
    }

    /**
     * 删除标准文件和版本记录
     */
    @ApiOperation("删除标准文件")
    @Log(title = "标准文件" , businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Void> delete(@RequestBody StandardBo bo) {
        if(StringUtils.isNotBlank(bo.getVersionId()) && StringUtils.isNotBlank(bo.getDocId())) {
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, bo.getDocId());
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, bo.getVersionId());
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return AjaxResult.error("已产生新数据,不可删除");
            }
        }
        if(iStandardService.deleteByBo(bo)){
            return AjaxResult.success();
        }else {
            return AjaxResult.error("删除失败");
        }
    }

    /**
     * 删除标准文件
     */
    @ApiOperation("删除标准文件")
    @Log(title = "标准文件" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iStandardService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
