package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.service.IReviewApplyService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件复审申请Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "文件复审申请控制器", tags = {"文件复审申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/reviewApply")
public class ReviewApplyController extends BaseController {

    private final IReviewApplyService iReviewApplyService;

    /**
     * 查询文件复审申请列表
     */
    @ApiOperation("查询文件复审申请列表")
    @GetMapping("/list")
    public TableDataInfo<ReviewApplyVo> list(@Validated(QueryGroup.class) ReviewApplyBo bo) {
        return iReviewApplyService.queryPageList(bo);
    }

    /**
     * 导出文件复审申请列表
     */
    @ApiOperation("导出文件复审申请列表")
    @Log(title = "文件复审申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ReviewApplyBo bo, HttpServletResponse response) {
        List<ReviewApplyVo> list = iReviewApplyService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件复审申请", ReviewApplyVo.class, response);
    }

    /**
     * 获取文件复审申请详细信息
     */
    @ApiOperation("获取文件复审申请详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ReviewApplyVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iReviewApplyService.queryById(id));
    }

    /**
     * 新增文件复审申请
     */
    @ApiOperation("新增文件复审申请")
    @Log(title = "文件复审申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ReviewApplyBo bo) {
        iReviewApplyService.insertByBo(bo);
        return AjaxResult.success();
    }

    /**
     * 修改文件复审申请
     */
    @ApiOperation("修改文件复审申请")
    @Log(title = "文件复审申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ReviewApplyBo bo) {
        return toAjax(iReviewApplyService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件复审申请
     */
    @ApiOperation("删除文件复审申请")
    @Log(title = "文件复审申请" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iReviewApplyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
