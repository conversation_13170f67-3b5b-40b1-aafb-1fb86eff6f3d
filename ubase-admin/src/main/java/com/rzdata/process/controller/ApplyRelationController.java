package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ApplyRelationVo;
import com.rzdata.process.domain.bo.ApplyRelationBo;
import com.rzdata.process.service.IApplyRelationService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件申请流程关联Controller
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Validated
@Api(value = "文件申请流程关联控制器", tags = {"文件申请流程关联管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/applyRelation")
public class ApplyRelationController extends BaseController {

    private final IApplyRelationService iApplyRelationService;

    /**
     * 查询文件申请流程关联列表
     */
    @ApiOperation("查询文件申请流程关联列表")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:list')")
    @GetMapping("/list")
    public TableDataInfo<ApplyRelationVo> list(@Validated(QueryGroup.class) ApplyRelationBo bo) {
        return iApplyRelationService.queryPageList(bo);
    }

    /**
     * 导出文件申请流程关联列表
     */
    @ApiOperation("导出文件申请流程关联列表")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:export')")
    @Log(title = "文件申请流程关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ApplyRelationBo bo, HttpServletResponse response) {
        List<ApplyRelationVo> list = iApplyRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件申请流程关联", ApplyRelationVo.class, response);
    }

    /**
     * 获取文件申请流程关联详细信息
     */
    @ApiOperation("获取文件申请流程关联详细信息")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:query')")
    @GetMapping("/{id}")
    public AjaxResult<ApplyRelationVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iApplyRelationService.queryById(id));
    }

    /**
     * 新增文件申请流程关联
     */
    @ApiOperation("新增文件申请流程关联")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:add')")
    @Log(title = "文件申请流程关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ApplyRelationBo bo) {
        return toAjax(iApplyRelationService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件申请流程关联
     */
    @ApiOperation("修改文件申请流程关联")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:edit')")
    @Log(title = "文件申请流程关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ApplyRelationBo bo) {
        return toAjax(iApplyRelationService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件申请流程关联
     */
    @ApiOperation("删除文件申请流程关联")
    @PreAuthorize("@ss.hasPermi('process:applyRelation:remove')")
    @Log(title = "文件申请流程关联" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iApplyRelationService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
