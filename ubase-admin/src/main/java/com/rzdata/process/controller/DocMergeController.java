package com.rzdata.process.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.DocMergeBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.service.IDocMergeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/1/19 10:32
 * @Version 1.0
 * @Description
 */
@Validated
@Api(value = "文件合稿控制器", tags = {"文件合稿申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-merge")
public class DocMergeController {

    @Resource
    IDocMergeService docMergeService;

    /**
     * 文件合并API
     */
    @ApiOperation("附件合并到主文件")
    @PostMapping("/merge")
    public AjaxResult merge(@RequestBody DocMergeBo bo) {
        return docMergeService.merge(bo);
    }

    /**
     * 文件合并API
     */
    @ApiOperation("关联记录文件合并")
    @PostMapping("/mergeForRecord")
    public AjaxResult mergeForRecord(@RequestBody DocMergeBo bo) {
        return docMergeService.mergeForRecord(bo);
    }
}
