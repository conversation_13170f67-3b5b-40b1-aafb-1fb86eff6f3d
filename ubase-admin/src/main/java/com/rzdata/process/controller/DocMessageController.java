package com.rzdata.process.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.bo.DocMessageBo;
import com.rzdata.process.domain.vo.DocMessageVo;
import com.rzdata.process.service.IDocMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/11 9:59
 * @Version 1.0
 * @Description 文件消息控制器
 */
@Validated
@Slf4j
@Api(value = "消息控制器", tags = {"消息查询管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/doc-message")
public class DocMessageController {

    @Resource
    IDocMessageService docMessageService;

    /**
     * 查询消息
     *
     * @param docMessageBo 查询消息参数实体
     */
    @ApiOperation(value = "消息查询")
    @PostMapping("/query")
    public AjaxResult query(@RequestBody DocMessageBo docMessageBo) {
        try {
            TableDataInfo<DocMessageVo> dataInfo = docMessageService.queryListByPage(docMessageBo);
            return AjaxResult.success(dataInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询消息失败");
        }
    }


    @ApiOperation(value = "消息速递")
    @GetMapping("/delivery")
    public AjaxResult messageDelivery() {
        try {
            List<DocMessageVo> dataInfo = docMessageService.queryMessageDelivery();
            //List<DocMessageVo> docMessageList=dataInfo.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(DocMessageVo :: getDocName))), ArrayList::new));
            return AjaxResult.success(dataInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询消息失败");
        }
    }

    /**
     * 将消息设置为已读
     *
     * @param id 消息id
     */
    @ApiOperation(value = "消息设置为已读")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "文档主键 设置已读", required = true)})
    @GetMapping("/read")
    public AjaxResult read(@RequestParam String id) {
        try {
            DocMessage docMessage = new DocMessage();
            docMessage.setId(id);
            docMessage.setMsgStatus(1);
            docMessage.setRecoveryTime(new Date());
            docMessageService.updateById(docMessage);
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新消息状态失败");
        }
    }

    /**
     * 将消息设置为已读
     *
     * @param docMessageBo 查询消息参数实体
     */
    @ApiOperation(value = "获取未读消息数")
    @PostMapping("/unread-num")
    public AjaxResult unread(@RequestBody DocMessageBo docMessageBo) {
        try {
            return AjaxResult.success(docMessageService.getUnreadNum(docMessageBo));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新消息状态失败");
        }
    }
}
