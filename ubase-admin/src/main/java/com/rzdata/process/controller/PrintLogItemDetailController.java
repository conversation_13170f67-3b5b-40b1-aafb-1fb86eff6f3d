package com.rzdata.process.controller;

import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.bo.PrintLogItemDetailBo;
import com.rzdata.process.domain.vo.PrintLogItemDetailVo;
import com.rzdata.process.service.IPrintLogItemDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件打印记录Controller
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Validated
@Api(value = "文件打印详情m明细控制器", tags = {"文件打印详情管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/printLogItemDetail")
public class PrintLogItemDetailController extends BaseController {

    private final IPrintLogItemDetailService iPrintLogItemDetailService;

    /**
     * 查询文件打印记录列表
     */
    @ApiOperation("查询文件打印记录列表")
    @GetMapping("/list")
    //@DataScope()
    public TableDataInfo<PrintLogItemDetailVo> list(PrintLogItemDetailBo bo) {
        return iPrintLogItemDetailService.queryPageList(bo);
    }

}
