package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.service.IModifyApplyLinkService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件变更操作申请引用Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件变更操作申请引用控制器", tags = {"文件变更操作申请引用管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/modifyApplyLink")
public class ModifyApplyLinkController extends BaseController {

    private final IModifyApplyLinkService iModifyApplyLinkService;

    /**
     * 查询文件变更操作申请引用列表
     */
    @ApiOperation("查询文件变更操作申请引用列表")
    @GetMapping("/list")
    public TableDataInfo<ModifyApplyLinkVo> list(@Validated(QueryGroup.class) ModifyApplyLinkBo bo) {
        return iModifyApplyLinkService.queryPageList(bo);
    }

    /**
     * 导出文件变更操作申请引用列表
     */
    @ApiOperation("导出文件变更操作申请引用列表")
    @Log(title = "文件变更操作申请引用", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ModifyApplyLinkBo bo, HttpServletResponse response) {
        List<ModifyApplyLinkVo> list = iModifyApplyLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件变更操作申请引用", ModifyApplyLinkVo.class, response);
    }

    /**
     * 查询文件关联记录列表
     */
    @ApiOperation("查询文件关联记录列表")
    @GetMapping("/list/link")
    public AjaxResult<List<ModifyApplyLinkVo>> selectListByVersionLink(ModifyApplyLinkBo bo) {
        return AjaxResult.success(iModifyApplyLinkService.selectListByVersionLink(bo));
    }

    /**
     * 获取文件变更操作申请引用详细信息
     */
    @ApiOperation("获取文件变更操作申请引用详细信息")
    @GetMapping("/{id}")
    public AjaxResult<ModifyApplyLinkVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iModifyApplyLinkService.queryById(id));
    }

    /**
     * 新增文件变更操作申请引用
     */
    @ApiOperation("新增文件变更操作申请引用")
    @Log(title = "文件变更操作申请引用", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ModifyApplyLinkBo bo) {
        return toAjax(iModifyApplyLinkService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件变更操作申请引用
     */
    @ApiOperation("修改文件变更操作申请引用")
    @Log(title = "文件变更操作申请引用", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ModifyApplyLinkBo bo) {
        return toAjax(iModifyApplyLinkService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件变更操作申请引用
     */
    @ApiOperation("删除文件变更操作申请引用")
    @Log(title = "文件变更操作申请引用" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iModifyApplyLinkService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
