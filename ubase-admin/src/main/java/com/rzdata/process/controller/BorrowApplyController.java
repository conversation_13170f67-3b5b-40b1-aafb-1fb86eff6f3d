package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.service.IStoreFileService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import com.rzdata.process.domain.bo.BorrowApplyBo;
import com.rzdata.process.service.IBorrowApplyService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件借阅申请Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件借阅申请控制器", tags = {"文件借阅申请管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/borrowApply")
public class BorrowApplyController extends BaseController {

    private final IBorrowApplyService iBorrowApplyService;

    /**
     * 查询文件借阅申请列表
     */
    @ApiOperation("查询文件借阅申请列表")
    @GetMapping("/list")
    public TableDataInfo<BorrowApplyVo> list(@Validated(QueryGroup.class) BorrowApplyBo bo) {
        return iBorrowApplyService.queryPageListNew(bo);
    }

    /**
     * 导出文件借阅申请列表
     */
    @ApiOperation("导出文件借阅申请列表")
    @Log(title = "文件借阅申请", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BorrowApplyBo bo, HttpServletResponse response) {
        List<BorrowApplyVo> list = iBorrowApplyService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件借阅申请", BorrowApplyVo.class, response);
    }

    /**
     * 获取文件借阅申请详细信息
     */
    @ApiOperation("获取文件借阅申请详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BorrowApplyVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iBorrowApplyService.queryById(id));
    }

    /**
     * 新增文件借阅申请
     */
    @ApiOperation("新增文件借阅申请")
    @Log(title = "文件借阅申请", businessType = BusinessType.INSERT, changeOperation = true)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<ProcessInstanceModel> add(@Validated(AddGroup.class) @RequestBody BorrowApplyBo bo) {
        return AjaxResult.success("成功", iBorrowApplyService.insertByBo(bo));
    }

    /**
     * 修改文件借阅申请
     */
    @ApiOperation("修改文件借阅申请")
    @Log(title = "文件借阅申请", businessType = BusinessType.UPDATE, changeOperation = true)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BorrowApplyBo bo) {
        return toAjax(iBorrowApplyService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件借阅申请
     */
    @ApiOperation("删除文件借阅申请")
    @Log(title = "文件借阅申请" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iBorrowApplyService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
