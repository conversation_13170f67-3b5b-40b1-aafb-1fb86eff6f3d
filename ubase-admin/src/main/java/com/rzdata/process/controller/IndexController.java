package com.rzdata.process.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.bo.IndexBo;
import com.rzdata.process.domain.bo.ReadLogBo;
import com.rzdata.process.domain.vo.DocPreviewStatisticsVo;
import com.rzdata.process.domain.vo.IndexDeptVo;
import com.rzdata.process.domain.vo.IndexVo;
import com.rzdata.process.domain.vo.ReadLogVo;
import com.rzdata.process.mapper.FileAdviseMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.WorkflowApplyLogMapper;
import com.rzdata.process.service.IDocMessageService;
import com.rzdata.process.service.IDocPreviewStatisticsService;
import com.rzdata.process.service.IReadLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @auther xcy
 * @create 2022-02-09 9:56
 */
@Validated
@Api(value = "首页控制器", tags = {"首页"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/index")
public class IndexController extends BaseController {

    @Autowired
    IReadLogService iReadLogService;

    @Autowired
    WorkflowApplyLogMapper workflowApplyLogMapper;

    @Autowired
    StandardMapper standardMapper;

    @Autowired
    FileAdviseMapper fileAdviseMapper;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    private IDocPreviewStatisticsService docPreviewStatisticsService;
    @ApiOperation("最近查询top5")
    @PostMapping("/selectReadList")
    public AjaxResult<List<ReadLogVo>> selectReadList(@RequestBody ReadLogBo bo){
        return AjaxResult.success(iReadLogService.selectReadListTop5(bo));
    }

    @ApiOperation("文件统计")
    @PostMapping("/selectFileStatistic")
    public AjaxResult<List<IndexDeptVo>> selectFileStatistic(@RequestBody IndexBo bo){
        List<IndexVo> list = workflowApplyLogMapper.selectFileStatistic(bo);
        List<IndexDeptVo> deptVoList = workflowApplyLogMapper.selectDeptList();
        if (ObjectUtil.isNotEmpty(list) && ObjectUtil.isNotEmpty(deptVoList)) {
            for (IndexDeptVo indexDeptVo : deptVoList) {
                Long num = list.stream().filter(x -> x.getDeptId().contains(indexDeptVo.getDeptId())).mapToLong(x -> x.getNum()).sum();
                indexDeptVo.setNum(num);
            }
        }
        return AjaxResult.success(deptVoList);
    }

    @ApiOperation("文件生效")
    @PostMapping("/selectFileEffect")
    public AjaxResult<List<IndexVo>> selectFileEffect(@RequestBody IndexBo bo){
        return AjaxResult.success(standardMapper.selectFileEffect(bo));
    }

    @ApiOperation("文件建议")
    @PostMapping("/selectFileAdvise")
    public AjaxResult<List<IndexVo>> selectFileAdvise(@RequestBody IndexBo bo){
        if (ObjectUtil.isEmpty(bo.getSearchType())) {
            throw new RuntimeException("查询类型不能为空");
        }
        if (NumberConstants.ZERO.equals(bo.getSearchType())){
            return AjaxResult.success(fileAdviseMapper.selectFileAdviseByDept(bo));
        } else {
            return AjaxResult.success(fileAdviseMapper.selectFileAdviseByUser(bo));
        }
    }


    @ApiOperation(value = "消息查询")
    @GetMapping("/selectMessage")
    public AjaxResult query() {
        try {
            QueryWrapper<DocMessage> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DocMessage:: getMsgStatus, NumberConstants.ZERO);
            queryWrapper.lambda().orderByDesc(DocMessage:: getCreateTime);
            List<DocMessage> dataInfo = docMessageService.list(queryWrapper);
            if(ObjectUtil.isNotEmpty(dataInfo)) {
                dataInfo = CollectionUtil.page(0, 5, dataInfo);
            }
            return AjaxResult.success(dataInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询消息失败");
        }
    }


    @ApiOperation(value="个人预览统计")
    @GetMapping("/selectPreviewStatistic")
    AjaxResult<List<DocPreviewStatisticsVo>> previewStatist(){
        return docPreviewStatisticsService.previewStatist();
    }
}
