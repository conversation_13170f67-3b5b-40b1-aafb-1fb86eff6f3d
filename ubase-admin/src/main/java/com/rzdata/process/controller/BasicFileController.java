package com.rzdata.process.controller;

import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.asas7.util.OSSException;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.vo.UploadFileVo;
import com.rzdata.process.service.IStoreFileService;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.ApiException;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.framework.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 附件Controller
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Validated
@Api(value = "附件控制器", tags = {"附件管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/file")
public class BasicFileController extends BaseController {

    private final IBasicFileService iBasicFileService;
    private final IStoreFileService iStoreFileService;

    @Autowired
    FileUploadDownloadService fileUploadService;

    @Autowired
    ISysConfigService configService;

    /**
     * 查询附件列表
     */
    @ApiOperation("查询附件列表")
    @GetMapping("/list")
    public TableDataInfo<BasicFileVo> list(@Validated(QueryGroup.class) BasicFileBo bo) {
        return iBasicFileService.queryPageList(bo);
    }

    /**
     * 导出附件列表
     */
    @ApiOperation("导出附件列表")
    @Log(title = "附件", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated BasicFileBo bo, HttpServletResponse response) {
        List<BasicFileVo> list = iBasicFileService.queryList(bo);
        ExcelUtil.exportExcel(list, "附件", BasicFileVo.class, response);
    }

    /**
     * 获取附件详细信息
     */
    @ApiOperation("获取附件详细信息")
    @GetMapping("/{id}")
    public AjaxResult<BasicFileVo> getInfo(@ApiParam("主键")
                                           @NotNull(message = "主键不能为空")
                                           @PathVariable("id") String id) {
        return AjaxResult.success(iBasicFileService.queryById(id));
    }

    /**
     * 新增附件
     */
    @ApiOperation("新增附件")
    @Log(title = "附件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<String> add(@Validated(AddGroup.class) @RequestBody BasicFileBo bo) {
        return AjaxResult.success("操作成功", iBasicFileService.insertByBo(bo));
    }

    /**
     * 处理文件上传
     */
    @ApiOperation("上传本地存储")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件", dataType = "java.io.File", required = true),
    })
    @Log(title = "本地存储", businessType = BusinessType.INSERT)
    @PostMapping(value = "/local_upload")
    public AjaxResult<Map<String, String>> uploading(@RequestPart("file") MultipartFile file) throws Exception{
        return AjaxResult.success(iBasicFileService.uploading(file));
    }

    @ApiOperation("下载本地存储")
    @GetMapping("/local_download/{fileId}")
    public void downLoad(@PathVariable String fileId, HttpServletResponse response) throws Exception {
        BasicFileVo vo = iBasicFileService.getVoById(fileId);
        iStoreFileService.downloadFile(vo, response);
        Boolean asPlatform = Boolean.valueOf(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if(asPlatform && ObjectUtil.isNotEmpty(vo.getExternalFilePath())) {
            fileUploadService.singleDownload("C:/", vo.getExternalFilePath());
        }
    }

    /**
     * 上传附件
     */
    @ApiOperation(value = "上传附件", notes = "上传附件", httpMethod = "POST")
    @PreAuthorize("@ss.hasPermi('process:file:upload')")
    @Log(title = "上传附件", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping(value = "/upload", consumes = "multipart/form-data", headers = "content-type=multipart/form-data")
    public AjaxResult<UploadFileVo> upload(@ApiParam(value = "附件", required = true) MultipartFile file) {
        try {
            return AjaxResult.success(iStoreFileService.uploadFile(file));
        } catch (IOException e) {
            return AjaxResult.error(e.getMessage(), null);
        }
    }

    /**
     * 修订附件
     */
    @ApiOperation("修订附件")
    @Log(title = "附件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/modify")
    public AjaxResult<Map<String, String>> modify(@ApiParam(value = "附件", required = true) MultipartFile file,
                                   @ApiParam("主键串")
                            @NotEmpty(message = "主键不能为空")
                            @PathVariable String[] ids) throws Exception{
        this.remove(ids);
        return this.uploading(file);
    }

    /**
     * 修改附件
     */
    @ApiOperation("修改附件")
    @PreAuthorize("@ss.hasPermi('process:file:edit')")
    @Log(title = "附件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody BasicFileBo bo) {
        return toAjax(iBasicFileService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除附件
     */
    @ApiOperation("删除附件")
    @PreAuthorize("@ss.hasPermi('process:file:remove')")
    @Log(title = "附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                   @NotEmpty(message = "主键不能为空")
                                   @PathVariable String[] ids) {
        return toAjax(iBasicFileService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation("获取爱数token")
    @GetMapping("/getAsAccessToken")
    public AjaxResult<String> getAsAccessToken() {
        String token = fileUploadService.getAccessToken();
        return AjaxResult.success("成功", token);
    }

    @ApiOperation("爱数平台下载文件")
    @GetMapping("/downloadFileAs")
    public AjaxResult<Void> downloadFileAs(@RequestParam("savePath") String savePath, @RequestParam("filePath") String filePath) throws Exception {
        if (ObjectUtil.isEmpty(savePath)) {
            savePath = "C:/";
        }
        fileUploadService.singleDownload(savePath, filePath);
        return AjaxResult.success();
    }


}
