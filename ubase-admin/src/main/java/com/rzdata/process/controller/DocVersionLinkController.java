package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.DocVersionLinkVo;
import com.rzdata.process.domain.bo.DocVersionLinkBo;
import com.rzdata.process.service.IDocVersionLinkService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 版本-文件关联记录-关联Controller
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Validated
@Api(value = "版本-文件关联记录-关联控制器", tags = {"版本-文件关联记录-关联管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/versionLink")
public class DocVersionLinkController extends BaseController {

    private final IDocVersionLinkService iDocVersionLinkService;

    /**
     * 查询版本-文件关联记录-关联列表
     */
    @ApiOperation("查询版本-文件关联记录-关联列表")
    @PreAuthorize("@ss.hasPermi('process:versionLink:list')")
    @GetMapping("/list")
    public TableDataInfo<DocVersionLinkVo> list(@Validated(QueryGroup.class) DocVersionLinkBo bo) {
        return iDocVersionLinkService.queryPageList(bo);
    }

    /**
     * 导出版本-文件关联记录-关联列表
     */
    @ApiOperation("导出版本-文件关联记录-关联列表")
    @PreAuthorize("@ss.hasPermi('process:versionLink:export')")
    @Log(title = "版本-文件关联记录-关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocVersionLinkBo bo, HttpServletResponse response) {
        List<DocVersionLinkVo> list = iDocVersionLinkService.queryList(bo);
        ExcelUtil.exportExcel(list, "版本-文件关联记录-关联", DocVersionLinkVo.class, response);
    }

    /**
     * 获取版本-文件关联记录-关联详细信息
     */
    @ApiOperation("获取版本-文件关联记录-关联详细信息")
    @PreAuthorize("@ss.hasPermi('process:versionLink:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocVersionLinkVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocVersionLinkService.queryById(id));
    }

    /**
     * 新增版本-文件关联记录-关联
     */
    @ApiOperation("新增版本-文件关联记录-关联")
    @PreAuthorize("@ss.hasPermi('process:versionLink:add')")
    @Log(title = "版本-文件关联记录-关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocVersionLinkBo bo) {
        return toAjax(iDocVersionLinkService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改版本-文件关联记录-关联
     */
    @ApiOperation("修改版本-文件关联记录-关联")
    @PreAuthorize("@ss.hasPermi('process:versionLink:edit')")
    @Log(title = "版本-文件关联记录-关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocVersionLinkBo bo) {
        return toAjax(iDocVersionLinkService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除版本-文件关联记录-关联
     */
    @ApiOperation("删除版本-文件关联记录-关联")
    @PreAuthorize("@ss.hasPermi('process:versionLink:remove')")
    @Log(title = "版本-文件关联记录-关联" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocVersionLinkService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
