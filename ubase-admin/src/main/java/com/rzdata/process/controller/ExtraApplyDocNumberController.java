package com.rzdata.process.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.process.domain.vo.ExtraApplyDocNumberVo;
import com.rzdata.process.domain.bo.ExtraApplyDocNumberBo;
import com.rzdata.process.service.IExtraApplyDocNumberService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件增发申请文件份数Controller
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Validated
@Api(value = "文件增发申请文件份数控制器", tags = {"文件增发申请文件份数管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/process/extraApplyDocNumber")
public class ExtraApplyDocNumberController extends BaseController {

    private final IExtraApplyDocNumberService iExtraApplyDocNumberService;

    /**
     * 查询文件增发申请文件份数列表
     */
    @ApiOperation("查询文件增发申请文件份数列表")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:list')")
    @GetMapping("/list")
    public TableDataInfo<ExtraApplyDocNumberVo> list(@Validated(QueryGroup.class) ExtraApplyDocNumberBo bo) {
        return iExtraApplyDocNumberService.queryPageList(bo);
    }

    /**
     * 导出文件增发申请文件份数列表
     */
    @ApiOperation("导出文件增发申请文件份数列表")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:export')")
    @Log(title = "文件增发申请文件份数", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated ExtraApplyDocNumberBo bo, HttpServletResponse response) {
        List<ExtraApplyDocNumberVo> list = iExtraApplyDocNumberService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件增发申请文件份数", ExtraApplyDocNumberVo.class, response);
    }

    /**
     * 获取文件增发申请文件份数详细信息
     */
    @ApiOperation("获取文件增发申请文件份数详细信息")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:query')")
    @GetMapping("/{id}")
    public AjaxResult<ExtraApplyDocNumberVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iExtraApplyDocNumberService.queryById(id));
    }

    /**
     * 新增文件增发申请文件份数
     */
    @ApiOperation("新增文件增发申请文件份数")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:add')")
    @Log(title = "文件增发申请文件份数", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody ExtraApplyDocNumberBo bo) {
        return toAjax(iExtraApplyDocNumberService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件增发申请文件份数
     */
    @ApiOperation("修改文件增发申请文件份数")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:edit')")
    @Log(title = "文件增发申请文件份数", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody ExtraApplyDocNumberBo bo) {
        return toAjax(iExtraApplyDocNumberService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件增发申请文件份数
     */
    @ApiOperation("删除文件增发申请文件份数")
    @PreAuthorize("@ss.hasPermi('process:extraApplyDocNumber:remove')")
    @Log(title = "文件增发申请文件份数" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iExtraApplyDocNumberService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
