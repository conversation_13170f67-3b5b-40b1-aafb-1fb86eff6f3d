package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.WorkflowLog;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流程审批记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface WorkflowLogMapper extends BaseMapperPlus<WorkflowLog> {

    List<WorkflowLogVo> selectLogByBusinessId(@Param("businessId") String businessId);

    String queryRecentLog(@Param("businessId") String id);

    Page<WorkflowLogVo> queryToDoList(@Param("page") Page<WorkflowLogVo> page,@Param("bo") ProcessWorkFlowBo bo);

    List<WorkflowLogVo> queryApprovalRecord(@Param("applyId")String applyId,@Param("actDefName")String actDefName);

}
