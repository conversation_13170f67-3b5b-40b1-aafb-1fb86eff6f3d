package com.rzdata.process.mapper;

import com.rzdata.process.domain.ModifyApplyDistribute;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ModifyApplyDistributeBo;
import com.rzdata.process.domain.vo.ModifyApplyDistributeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 文件变更操作申请分发Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
public interface ModifyApplyDistributeMapper extends BaseMapperPlus<ModifyApplyDistribute> {

    Integer queryNumsByMap(Map<String, Object> params);

    List<ModifyApplyDistributeVo> queryListByChangeIdApplyId(@Param("bo") ModifyApplyDistributeBo distributeBo);
}
