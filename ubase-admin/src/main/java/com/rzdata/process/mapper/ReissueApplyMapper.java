package com.rzdata.process.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.ReissueApply;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.ReissueApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件补发申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Mapper
public interface ReissueApplyMapper extends BaseMapperPlus<ReissueApply> {

    List<Map<String, Object>> selectReissueApplyList(@Param("bo") ProcessBo bo, @Param("ids")List<String> ids);

    Collection<? extends Map<String, Object>> selectReissueApplyList4New(@Param("bo")ProcessWorkFlowBo processBo, @Param("ids")List<String> businessIds);

    List<ReissueApply> listByDisItemId(@Param("disItemId") String disItemId);
}
