package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.ModifyApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件变更操作申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Mapper
public interface ModifyApplyMapper extends BaseMapperPlus<ModifyApply> {

    List<Map<String, Object>> selectModifyApplyList(@Param("bo") ProcessBo bo, @Param("ids") List<String> ids);

    String getDocTypeByDocId(@Param("docId") String docId);

    String getDocVersionByDocId(@Param("docId") String docId);

    List<String> getApplyIdByDocId(@Param("docId") String docId);

    Page<ModifyApply> getApplyModifyList(@Param("page") Page<ModifyApply> page,@Param("bo") ModifyApplyBo bo);

    Collection<? extends Map<String, Object>> selectModifyApplyList4New(@Param("bo") ProcessWorkFlowBo processBo, @Param("ids") List<String> businessIds);
}
