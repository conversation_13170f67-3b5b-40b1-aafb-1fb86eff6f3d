package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.bo.DistributeItemBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件分发记录条目Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Mapper
public interface DistributeItemMapper extends BaseMapperPlus<DistributeItem> {

    Page<DistributeItemVo> selectDistributeItemPage(@Param("page") Page<DistributeItemVo> page, @Param("bo") DistributeItemBo bo);



    List<DistributeItemVo>  selectDistributeItemList(@Param("bo") DistributeItemBo bo);

    Long selectPrintNumsById(@Param("id")String id);

    Page<DistributeItemVo> listByAppid(@Param("page") Page<DistributeItemVo> page,@Param("bo") DistributeItemBo bo);

    /**
     * 通过distributeId 查出对应的分发信息
     * @param page
     * @param bo
     * @return DistributeItemVo
     */
    Page<DistributeItemVo> listByDisId(@Param("page") Page<DistributeItemVo> page,@Param("bo") DistributeItemBo bo);

    Page<DistributeItemVo> listRecoveryByAppid(@Param("page") Page<DistributeItemVo> page,@Param("bo") DistributeItemBo bo);
}
