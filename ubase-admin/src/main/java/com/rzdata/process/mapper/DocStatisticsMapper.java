package com.rzdata.process.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo;
import com.rzdata.process.domain.vo.DocStatisticsChangeTypeVo;
import com.rzdata.process.domain.vo.DocStatisticsDistributeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 17:05
 * @Version 1.0
 * @Description
 */
@Mapper
public interface DocStatisticsMapper extends BaseMapperPlus<DocDistributeLog> {
    /**
     * 变更类型统计
     * @param bo 文档统计参数实体
     * @return DocStatisticsVo
     */
    List<DocStatisticsChangeTypeVo> selectChangeType(@Param("bo") DocStatisticsBo bo);

    /**
     * 变更要素统计
     * @param bo 文档统计参数实体
     * @return DocStatisticsVo
     */
    List<DocStatisticsChangeFactorVo> selectChangeFactor(@Param("bo") DocStatisticsBo bo);

    /**
     * 文件分发回收统计
     * @param bo 查询实体
     * @return result
     */
    List<DocStatisticsChangeFactorVo> selectRecovery(@Param("bo")DocStatisticsBo bo);

    /**
     * 文件分发回收统计
     * @param bo 查询实体
     * @return result
     */
    List<DocStatisticsDistributeVo> selectDistribute(@Param("bo") DocDisStatisticsBo bo);
}
