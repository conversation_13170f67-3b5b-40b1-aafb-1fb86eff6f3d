package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.ReviewApply;
import com.rzdata.process.domain.ReviewApplyItem;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件复审申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface ReviewApplyItemMapper extends BaseMapperPlus<ReviewApplyItem> {

    /**
     * 根据入参查询复审信息
     * @param bo
     * @return
     */
    List<ReviewApplyItemVo> queryItemList(@Param("bo")ReviewApplyItemBo bo);

}
