package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.VersionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 文件版本记录Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface VersionMapper extends BaseMapperPlus<Version> {

    Page<VersionVo> selectVersionPage(@Param("page") Page<VersionVo> page, @Param("bo") VersionBo bo);

    VersionVo selectDetailById(@Param("id") String id);

    List<VersionVo> selectVersionListByAppId(@Param("applyId") String applyId);

    List<VersionVo> selectVersionList(@Param("ids") List<String> ids);

    List<VersionVo> selectVersionListByDocId(@Param("docId") String docId);

    VersionVo selectVersionByDocId(@Param("docId") String docId);

    List<VersionVo> selectVersionListByDocIdAndVersionId(@Param("docId") String docId,@Param("versionId") String versionId);

    Page<VersionVo> selectRecordFile(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo);

    Page<VersionVo> selectRecordFileCompany(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo);

    Page<VersionVo> selectDeptFile(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo);

    /**
     * 检查是否有借阅权限
     * @param docId 文档id
     * @param versionId 版本id
     * @return 0表示没有借阅权限 大于0表示有借阅权限
     */
    int checkAuthByBorrow(@Param("docId") String docId, @Param("versionId") String versionId,@Param("date") Date now);

    /**
     * 查看是否分发该文件 分发和增发因为都会进distribute_log表 并且只有通过了才会在distribute_log表存在数据
     * 所以直接查该表就行
     * @param docId 文档id
     * @param versionId 版本id
     * @param deptId 当前用户所属部门 用于判断分发部门是不是当前用户所属部门的
     * @return 0=表示该版本文件没有分发给当前登录用户所在部门 大于0表示分发了
     */
    int checkAuthByDis(@Param("docId") String docId, @Param("versionId") String versionId,@Param("deptId") String deptId);

}
