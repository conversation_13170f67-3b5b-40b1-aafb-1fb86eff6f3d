package com.rzdata.process.mapper;

import com.rzdata.process.domain.BasicFile;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 附件Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Mapper
public interface BasicFileMapper extends BaseMapperPlus<BasicFile> {

    List<Map<String, Object>> selectFileByDocId(@Param("docId") String docId);

    List<BasicFile> selectBasicFileList();
}
