package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.PrintLog;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.PrintLogBo;
import com.rzdata.process.domain.vo.PrintLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件打印记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Mapper
public interface PrintLogMapper extends BaseMapperPlus<PrintLog> {

    PrintLogVo selectDetailById(@Param("id") String id);

    Page<PrintLogVo> selectPrintLogPage(@Param("page") Page<PrintLogVo> page, @Param("bo") PrintLogBo bo);

    Page<PrintLogVo> queryPageList(@Param("page") Page<PrintLogVo> page, @Param("bo") PrintLogBo bo);

}
