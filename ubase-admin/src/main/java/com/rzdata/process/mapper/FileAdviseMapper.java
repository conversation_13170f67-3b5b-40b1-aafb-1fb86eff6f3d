package com.rzdata.process.mapper;

import com.rzdata.process.domain.FileAdvise;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.IndexBo;
import com.rzdata.process.domain.vo.IndexVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件建议Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Mapper
public interface FileAdviseMapper extends BaseMapperPlus<FileAdvise> {

    String selectUserName(@Param("name")String name);

    List<IndexVo> selectFileAdviseByDept(@Param("bo")IndexBo bo);

    List<IndexVo> selectFileAdviseByUser(@Param("bo")IndexBo bo);
}
