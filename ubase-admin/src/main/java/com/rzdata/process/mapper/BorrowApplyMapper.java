package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.BorrowApply;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.BorrowApplyBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件借阅申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Mapper
public interface BorrowApplyMapper extends BaseMapperPlus<BorrowApply> {

    List<Map<String, Object>> selectBorrowApplyList(@Param("bo") ProcessBo bo, @Param("ids")List<String> ids);

    Page<BorrowApplyVo> selectBorrowApplyPage(@Param("page") Page<BorrowApplyVo> page, @Param("bo") BorrowApplyBo bo);

    List<BorrowApplyVo> selectBorrowList();

    Collection<? extends Map<String, Object>> selectBorrowApplyList4New(@Param("bo") ProcessWorkFlowBo processBo, @Param("ids") List<String> businessIds);
}
