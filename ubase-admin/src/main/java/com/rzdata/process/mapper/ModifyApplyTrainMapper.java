package com.rzdata.process.mapper;

import com.rzdata.process.domain.ModifyApplyTrain;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;

import java.util.List;

/**
 * 文件变更操作申请培训记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
public interface ModifyApplyTrainMapper extends BaseMapperPlus<ModifyApplyTrain> {

    /**
     * 查找申请的培训记录
     * @param applyId
     * @param isDeleted
     * @return
     */
    List<ModifyApplyTrain> selectTrainList(String applyId, Integer isDeleted);
}
