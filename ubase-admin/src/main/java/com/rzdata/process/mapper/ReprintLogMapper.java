package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.ReprintLog;
import com.rzdata.process.domain.bo.ReprintLogBo;
import com.rzdata.process.domain.vo.ReprintLogVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:59
 * @Version 1.0
 * @Description
 */
public interface ReprintLogMapper extends BaseMapperPlus<ReprintLog> {

    Page<ReprintLogVo> selectReprintLogPage(@Param("page") Page<ReprintLogVo> page, @Param("bo")ReprintLogBo bo);
}
