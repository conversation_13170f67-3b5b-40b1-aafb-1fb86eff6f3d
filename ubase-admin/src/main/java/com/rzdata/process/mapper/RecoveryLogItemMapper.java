package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.RecoveryLogItem;
import com.rzdata.process.domain.bo.RecoveryLogItemBo;
import com.rzdata.process.domain.vo.RecoveryLogItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件回收记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Mapper
public interface RecoveryLogItemMapper extends BaseMapperPlus<RecoveryLogItem> {

    Page<RecoveryLogItemVo> queryPageList(@Param("page") Page<RecoveryLogItemVo> page, @Param("bo") RecoveryLogItemBo bo);
}
