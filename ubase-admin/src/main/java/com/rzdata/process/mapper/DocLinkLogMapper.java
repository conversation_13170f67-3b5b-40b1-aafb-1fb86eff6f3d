package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件关联记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
public interface DocLinkLogMapper extends BaseMapperPlus<DocLinkLog> {

    DocLinkLogVo queryLinkVo(@Param("docId")String docId,@Param("linkCode")String linkCode,@Param("versionId")String versionId);

    Page<DocLinkLogVo> getPageList(@Param("page") Page<DocLinkLogVo> page, @Param("bo") DocLinkLogBo bo);

    /**
     * 查询文件关联记录
     * @param docId 文件编号
     * @param versionId 版本号
     * @param linkType 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
     * @return
     */
    List<DocLinkLogVo> queryDocLinkVo(@Param("docId") String docId, @Param("versionId")String versionId, @Param("linkType") String linkType);
}
