package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.Standard;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.IndexBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.DocInfoVo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.IndexVo;
import com.rzdata.process.domain.vo.StandardVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标准文件Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Mapper
public interface StandardMapper extends BaseMapperPlus<Standard> {

    Page<StandardVo> selectPageList(@Param("page") Page<StandardVo> page, @Param("bo") StandardBo bo);

    List<DocLinkLogVo> exportLinkLog(@Param("bo") StandardBo bo,@Param("ids") List<String> ids);

    List<DocInfoVo> selectDocInfo(@Param("docIds") List<String> docIds);

    DocInfoVo selectDocInfoByFileId(@Param("fileId") String fileId);

    List<IndexVo> selectFileEffect(@Param("bo")IndexBo bo);

    /**
     *  查出有效的文件列表
     *  定时任务专用
     * @return 文件列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<StandardVo> selectValidFile();

    /**
     * 通过versionId查出标准文件
     * @param versionId
     * @return
     */
    StandardVo getStandardVoByVersionId(@Param("versionId") String versionId);

    /**
     * 根据id查询文件信息
     * @param id
     * @return
     */
    StandardVo queryInfoById(@Param("id") String id);
}
