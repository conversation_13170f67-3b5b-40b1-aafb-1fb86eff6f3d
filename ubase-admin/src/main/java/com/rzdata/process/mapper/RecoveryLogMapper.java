package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.RecoveryLogBo;
import com.rzdata.process.domain.vo.RecoveryLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件回收记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Mapper
public interface RecoveryLogMapper extends BaseMapperPlus<RecoveryLog> {

    Page<RecoveryLogVo> selectRecoveryLogPage(@Param("page") Page<RecoveryLogVo> page, @Param("bo") RecoveryLogBo bo);


    Page<RecoveryLogVo> listPage(@Param("page") Page<RecoveryLogVo> page, @Param("bo") RecoveryLogBo bo);

}
