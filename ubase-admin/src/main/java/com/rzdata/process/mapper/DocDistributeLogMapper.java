package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 文件分发记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Mapper
public interface DocDistributeLogMapper extends BaseMapperPlus<DocDistributeLog> {

    Page<DocDistributeLogVo> selectDocDistributeLogPage(@Param("page") Page<DocDistributeLogVo> page, @Param("bo") DocDistributeLogBo bo);

    Page<DocDistributeLogVo> selectDistributeNoProcessPage(@Param("page") Page<DocDistributeLogVo> page, @Param("bo") DocDistributeLogBo bo);

    void updateByDocId(@Param("docId") String docId);

    List<DocDistributeLogVo> selectDocDistributeLogList(@Param("bo") DocDistributeLogBo bo);

    Page<DocDistributeLogVo> selectDocDistributeLog4Page(@Param("page")  Page<DocDistributeLogVo> page,@Param("bo")  DocDistributeLogBo bo);


    //DocDistributeLogVo queryDocDistributeLogVoByAppId(Map<String, Object> params);

    List<DocDistributeLogVo> queryDocDistributeLogListVoByAppId(Map<String, Object> params);

    /**
     *  查出文件分发到哪些部门
     *  定时任务专用
     * @return 部门Id列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> selectDistributeDept(String docId);

    Page<DocDistributeLogVo> queryPageListForSign(@Param("page")Page<DocDistributeLogVo> page, @Param("bo") DocDistributeLogBo bo);

    List<DocDistributeLogVo> getDocDistributeNumsByDocId(@Param("docId")String docId, @Param("versionId")String versionId);

    /**
     * 本部门
     * @param page
     * @param bo
     * @param loginDeptId 当前登录人的所属部门
     * @return
     */
    Page<DocDistributeLogVo> queryPageByThisDept(@Param("page")Page<DocDistributeLogVo> page,@Param("bo")DocDistributeLogBo bo,@Param("loginDeptId") String loginDeptId);

    /**
     * 外部门
     * @param page
     * @param bo
     * @param loginDeptId 当前登录人的所属部门
     * @return
     */
    Page<DocDistributeLogVo> queryPageByOtherDept(@Param("page")Page<DocDistributeLogVo> page,@Param("bo")DocDistributeLogBo bo,@Param("loginDeptId") String loginDeptId);

}
