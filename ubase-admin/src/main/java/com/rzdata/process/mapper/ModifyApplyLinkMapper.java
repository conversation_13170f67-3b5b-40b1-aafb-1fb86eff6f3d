package com.rzdata.process.mapper;

import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件变更操作申请引用Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface ModifyApplyLinkMapper extends BaseMapperPlus<ModifyApplyLink> {

    List<ModifyApplyLinkVo> selectListByVersionLink(@Param("bo") ModifyApplyLinkBo bo);

    /**
     * 通过业务id查询ModifyApplyLink表的关联记录(ModifyApplyLink表这是暂存 因为业务中文件的关系可能会调整变化 最终生效文件的关联记录在doc_link_log表中)
     * @param applyId  业务id
     * @param linkType 关联类型 {@link com.rzdata.process.enums.LinkTypeEnum}
     * @return
     */
    List<ModifyApplyLinkVo> queryDocByApplyIdAndType(@Param("applyId") String applyId, @Param("linkType")String linkType);
}
