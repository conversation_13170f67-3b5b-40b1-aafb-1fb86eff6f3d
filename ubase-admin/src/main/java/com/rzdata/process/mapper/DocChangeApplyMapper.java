package com.rzdata.process.mapper;

import com.rzdata.process.domain.DocChangeApply;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.DocChangeApplyDetailVo;
import com.rzdata.process.domain.vo.DocChangeApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件变更申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Mapper
public interface DocChangeApplyMapper extends BaseMapperPlus<DocChangeApply> {

    List<Map<String, Object>> selectChangeApplyList(@Param("bo") ProcessBo bo, @Param("ids")List<String> ids);

    DocChangeApplyVo selectDetailById(@Param("id")String id);

    Collection<? extends Map<String, Object>> selectChangeApplyList4New(@Param("bo") ProcessWorkFlowBo processBo, @Param("ids")List<String> businessIds);
}
