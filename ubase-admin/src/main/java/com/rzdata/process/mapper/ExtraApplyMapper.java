package com.rzdata.process.mapper;

import com.rzdata.process.domain.ExtraApply;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.vo.ExtraApplyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件增发申请Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Mapper
public interface ExtraApplyMapper extends BaseMapperPlus<ExtraApply> {

    List<Map<String, Object>> selectExtraApplyList(@Param("bo") ProcessBo bo, @Param("ids")List<String> ids);

    ExtraApplyVo selectDetail(@Param("id") String id);

    Collection<? extends Map<String, Object>> selectExtraApplyList4New(@Param("bo") ProcessWorkFlowBo processBo, @Param("ids") List<String> businessIds);
}
