package com.rzdata.process.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocVersionFavorites;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import com.rzdata.process.domain.vo.VersionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 我的收藏Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
public interface DocVersionFavoritesMapper extends BaseMapperPlus<DocVersionFavorites> {

    Page<DocDistributeLogVo> queryPageFavorites(@Param("page")Page<DocDistributeLogVo> page, @Param("bo") DocDistributeLogBo bo, @Param("loginDeptId") String loginDeptId, @Param("userId") String userId);

    Page<VersionVo> queryPageFavoritesRecordFile(@Param("page") Page<VersionVo> page,@Param("bo") VersionBo bo,@Param("deptId") String deptId, @Param("userId") String userId);
}
