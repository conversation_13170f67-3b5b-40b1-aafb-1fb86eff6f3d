package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 我的收藏视图对象 doc_version_favorites
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@ApiModel("我的收藏视图对象")
@ExcelIgnoreUnannotated
public class DocVersionFavoritesVo {

	private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
	@ExcelProperty(value = "用户id")
	@ApiModelProperty("用户id")
	private Long userId;

    /**
     * 主文件版本ID
     */
	@ExcelProperty(value = "主文件版本ID")
	@ApiModelProperty("主文件版本ID")
	private String versionId;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;


}
