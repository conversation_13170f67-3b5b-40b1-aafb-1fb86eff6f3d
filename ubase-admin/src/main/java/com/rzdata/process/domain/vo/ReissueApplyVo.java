package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件补发申请视图对象 doc_reissue_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件补发申请视图对象")
@ExcelIgnoreUnannotated
public class ReissueApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 申请标题
     */
	@ExcelProperty(value = "申请标题")
	@ApiModelProperty("申请标题")
	private String applyTitle;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 补发部门
     */
	@ExcelProperty(value = "补发部门")
	@ApiModelProperty("补发部门")
	private String deptId;

	/**
	 * 补发部门名称
	 */
	@ExcelProperty(value = "补发部门名称")
	@ApiModelProperty("补发部门名称")
	private String deptName;
    /**
     * 分发号,多选
     */
	@ExcelProperty(value = "分发号,多选")
	@ApiModelProperty("分发号,多选")
	private String distributeItemIds;

    /**
     * 申请人
     */
	@ExcelProperty(value = "申请人")
	@ApiModelProperty("申请人")
	private String userName;

	/**
	 * 申请人名称
	 */
	@ExcelProperty(value = "申请人名称")
	@ApiModelProperty("申请人名称")
	private String nickName;
    /**
     * 申请原因
     */
	@ExcelProperty(value = "申请原因")
	@ApiModelProperty("申请原因")
	private String applyReason;

    /**
     * 申请状态
     */
	@ExcelProperty(value = "申请状态")
	@ApiModelProperty("申请状态")
	private String status;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 编制文件id
	 */
	@ApiModelProperty(value = "编制文件id")
	private String fileId;

	/**
	 * 合稿文件id
	 */
	@ApiModelProperty(value = "合稿文件id")
	private String mergeFileId;

	/**
	 * 签章文件id
	 */
	@ApiModelProperty(value = "签章文件id")
	private String encryptFileId;
}
