package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 流程审批记录对象 doc_workflow_log
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_workflow_log")
public class WorkflowLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程实例ID
     */
    private String procInstId;
    /**
     * 业务ID
     */
    private String businessId;
    /**
     * 环节定义名称
     */
    private String actDefName;
    /**
     * 环节实例ID
     */
    private String actInstId;
    /**
     * 是否同意
     */
    private String yNApprove;
    /**
     * 意见
     */
    private String opinion;
    /**
     * 发送人
     */
    private String sender;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 环节处理状态
     */
    private String actStatus;

    /**
     * 流程实例key
     */
    private String procDefKey;

    /**
     * 流程是否通过 true=通过 false=不通过
     */
    private Boolean pass;

    private String actName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
