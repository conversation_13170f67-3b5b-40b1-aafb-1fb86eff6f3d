package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件申请流程关联视图对象 doc_apply_relation
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件申请流程关联视图对象")
@ExcelIgnoreUnannotated
public class ApplyRelationVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String applyId;

    /**
     * 流程类型 修订，作废
     */
	@ExcelProperty(value = "流程类型 修订，作废")
	@ApiModelProperty("流程类型 修订，作废")
	private String applyType;

    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String relationApplyId;

    /**
     * 流程类型 复审
     */
	@ExcelProperty(value = "流程类型 复审")
	@ApiModelProperty("流程类型 复审")
	private String relationApplyType;

    /**
     * 是否删除 1 是
     */
	@ExcelProperty(value = "是否删除 1 是")
	@ApiModelProperty("是否删除 1 是")
	private Long isDeleted;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
}
