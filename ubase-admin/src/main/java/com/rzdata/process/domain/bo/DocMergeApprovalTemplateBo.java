package com.rzdata.process.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/19 17:12
 * @Version 1.0
 * @Description 文件合并-页眉模板实体类
 */
@Data
public class DocMergeApprovalTemplateBo {

    /**
     * 模板Id
     */
    @ApiModelProperty(value = "模板Id")
    private String templateDocId;

    /**
     * 起草信息
     */
    @ApiModelProperty(value = "起草信息")
    private DocMergeApprovalInfoBo draft;

    /**
     * 审批信息
     */
    @ApiModelProperty(value = "审批信息")
    private List<DocMergeApprovalInfoBo> approvals;

    /**
     * 批准信息
     */
    @ApiModelProperty(value = "批准信息")
    private DocMergeApprovalInfoBo allow;
}
