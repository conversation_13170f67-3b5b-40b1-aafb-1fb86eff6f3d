package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.convert.ExcelChangeTypeConvert;
import com.rzdata.process.domain.ReissueApply;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 打印记录条目对象 doc_print_log_item
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Data
@ExcelIgnoreUnannotated
public class PrintLogItemVo {

    private static final long serialVersionUID=1L;
    /**
     * 主键
     */
    private String id;

    /**
     * 分发部门编号
     */

    private String deptId;

    /**
     * 分发部门名称
     */
    @ExcelProperty(value = "分发部门",index = 0)
    private String deptName;

    /**
     * 文件分发表主键
     */
    private String distributeId;

    /**
     * 文件分发明细表主键(分发号)
     */
    @ExcelProperty(value = "分发号",index = 1)
    private String distributeItemId;

    /**
     * 文件打印列表数据主键 doc_print_log主键
     */
    private String printId;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 业务id
     */
    private String applyId;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 文件版本
     */
    private String versionId;

    /**
     * 文档编制部门
     */
    private Long compileDeptId;

    /**
     * 文档编制部门
     */
    private String compileDeptName;

    /**
     * 打印时间
     */
    @ExcelProperty(value = "首次打印时间",index = 4)
    private Date printTime;

    /**
     * 打印次数
     */
    private int nums;

    /**
     * 打印人
     */
    private String printUserName;

    /**
     * 打印人名称
     */
    @ExcelProperty(value = "打印人",index = 3)
    private String printUserNickName;

    /**
     * 打印状态 N:未，Y：已
     */
    private String status;

    /**
     * 备注
     */
    private String remake;

    /**
     * 打印数据来源 新增 修订 增发 补发
     */
    @ExcelProperty(value = "分发类型",converter = ExcelChangeTypeConvert.class,index = 2)
    private String printSource;

    /**
     * 补发详情
     */
    private List<ReissueApply> reissueApplyList;

}
