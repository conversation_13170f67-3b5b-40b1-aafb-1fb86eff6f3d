package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 我的收藏对象 doc_version_favorites
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Data
@Accessors(chain = true)
@TableName("doc_version_favorites")
public class DocVersionFavorites extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 用户id
     */
    private String userId;
    /**
     * 主文件版本ID
     */
    private String versionId;
    /**
     * 租户id
     */
    private String tenantId;

}
