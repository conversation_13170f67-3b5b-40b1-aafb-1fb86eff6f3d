package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件增发申请文件份数业务对象 doc_extra_apply_doc_number
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件增发申请文件份数业务对象")
public class ExtraApplyDocNumberBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量", required = true)
    @NotNull(message = "申请数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long applyNum;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是", required = true)
    @NotNull(message = "是否删除 1 是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isDeleted;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    @ApiModelProperty("版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ExcelProperty(value = "版本ID")
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("是否分发不打印(0 否 1 是)")
    private String notPrint;

}
