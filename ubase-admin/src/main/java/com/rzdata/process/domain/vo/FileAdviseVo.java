package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件建议视图对象 doc_file_advise
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Data
@ApiModel("文件建议视图对象")
@ExcelIgnoreUnannotated
public class FileAdviseVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 文件类型名称
	 */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型名称")
	private String docTypeName;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件id
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件id")
	private String docId;

    /**
     * 文件类型code
     */
	@ApiModelProperty("文件类型code")
	private String docTypeCode;

    /**
     * 版本Id
     */
	@ApiModelProperty("版本Id")
	private String versionId;

    /**
     * 版本值
     */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("版本值")
	private String versionValue;

    /**
     * 编制部门Id
     */
	@ApiModelProperty("编制部门Id")
	private String docDeptId;

    /**
     * 编制部门名称
     */
	@ExcelProperty(value = "编制部门")
	@ApiModelProperty("编制部门名称")
	private String docDeptName;

	@ExcelProperty(value = "提交部门")
	@ApiModelProperty("提交部门")
	private String deptName;

	/**
	 * 提交人
	 */
	@ExcelProperty(value = "提交人")
	@ApiModelProperty("提交人名称")
	private String createName;

	/**
	 * 提交人
	 */
	@ExcelProperty(value = "提交人")
	@ApiModelProperty("提交人")
	private String createUser;

	/**
	 * 提交时间
	 */
	@ExcelProperty(value = "提交时间")
	@ApiModelProperty(value = "提交时间")
	private Date createTime;

    /**
     * 建议内容
     */
	@ExcelProperty(value = "建议内容")
	@ApiModelProperty("建议内容")
	private String summary;

    /**
     * 部门id
     */
	@ApiModelProperty("部门id")
	private String deptId;



}
