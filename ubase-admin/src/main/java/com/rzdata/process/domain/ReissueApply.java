package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件补发申请对象 doc_reissue_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_reissue_apply")
public class ReissueApply {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 补发部门
     */
    private String deptId;
    /**
     * 分发号
     */
    private String distributeItemIds;
    /**
     * 申请人
     */
    private String userName;

    /**
     * 申请人名称
     */
    private String nickName;
    /**
     * 申请原因
     */
    private String applyReason;
    /**
     * 申请状态
     */
    private String status;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
