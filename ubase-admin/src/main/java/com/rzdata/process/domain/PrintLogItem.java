package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rzdata.process.enums.ApplyTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 打印记录条目对象 doc_print_log_item
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Data
@Accessors(chain = true)
@TableName("doc_print_log_item")
public class PrintLogItem {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 分发部门编号
     */
    private String deptId;

    /**
     * 分发部门名称
     */
    private String deptName;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件分发表主键
     */
    private String distributeId;

    /**
     * 文件打印列表数据主键 doc_print_log主键
     */
    private String printId;

    /**
     * 文件分发明细表主键(分发号)
     */
    private String distributeItemId;

    /**
     * 业务id
     */
    private String applyId;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 文件版本
     */
    private String versionId;

    /**
     * 文档编制部门
     */
    private Long compileDeptId;

    /**
     * 文档编制部门
     */
    private String compileDeptName;

    /**
     * 首次打印时间
     */
    private Date printTime;

    /**
     * 打印次数
     */
    private int nums;

    /**
     * 打印人
     */
    private String printUserName;

    /**
     * 打印人名称
     */
    private String printUserNickName;

    /**
     * 打印状态 N:未，Y：已
     */
    private String status;

    /**
     * 备注
     */
    private String remake;

    /**
     * 打印数据来源 新增 修订 增发 补发
     */
    private ApplyTypeEnum printSource;

    @TableField(exist = false)
    /**
     * 补发详情
     */
    private List<ReissueApply> reissueApplyList;

    @TableField(exist = false)
    /**
     * 增发详情
     */
    private ExtraApply extraApply;
}
