package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件阅览记录视图对象 doc_read_log
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Data
@ApiModel("文件阅览记录视图对象")
@ExcelIgnoreUnannotated
public class ReadLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本号")
	@ApiModelProperty("文件版本号")
	private String versionValue;

    /**
     * 阅读人员
     */
	@ExcelProperty(value = "阅读人员")
	@ApiModelProperty("阅读人员")
	private String userName;

    /**
     * 所属部门
     */
	@ExcelProperty(value = "所属部门")
	@ApiModelProperty("所属部门")
	private String deptId;

    /**
     * 下载量
     */
	@ExcelProperty(value = "下载量")
	@ApiModelProperty("下载量")
	private String downLoad;

    /**
     * 阅读量
     */
	@ExcelProperty(value = "阅读量")
	@ApiModelProperty("阅读量")
	private String preview;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String externalFileId;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@TableField(fill = FieldFill.INSERT)
	private Date createTime;

	@ApiModelProperty("文件名称")
	private String docName;

	@ApiModelProperty("部门名称")
	private String deptName;

	@ApiModelProperty("用户名")
	private String nickName;

	private String fileName;

}
