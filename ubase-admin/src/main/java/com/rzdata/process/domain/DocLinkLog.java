package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件关联记录对象 doc_link_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Data
@Accessors(chain = true)
@TableName("doc_link_log")
public class DocLinkLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 主文件主键
     */
    //private String standardId;
    /**
     * 主文件编号
     */
    //private String docId;
    /**
     * 关联记录文件编号
     */
    private String linkCode;
    /**
     * 关联文件名称
     */
    private String fileName;
    /**
     * 关联记录文件id
     */
    private String fileId;
    /**
     * 关联记录版本号
     */
    private String versionValue;
    /**
     * 主文件版本ID
     */
    //private String versionId;
    /**
     * 关联类型: DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
     */
    private String linkType;

    /**
     * 关联记录文件类型
     */
    private String docClass;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String status;
}
