package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.convert.ExcelChangeTypeConvert;
import com.rzdata.framework.convert.ExcelRecoveryStatusConvert;
import com.rzdata.framework.convert.ExcelRecoveryTypeConvert;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@ExcelIgnoreUnannotated
public class RecoveryLogItemVo {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号")
    private String docId;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID")
    private String versionId;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号")
    private String applyId;

    /**
     * 回收部门id(也就是分发部门)
     */
    @ApiModelProperty(value = "回收部门id(也就是分发部门)")
    private String deptId;

    /**
     * 回收部门名称(也就是分发部门)
     */
    @ApiModelProperty(value = "回收部门名称(也就是分发部门)")
    @ExcelProperty(value = "分发部门",index = 1)
    private String deptName;

    /**
     * 回收记录主键 doc_recovery主键
     */
    @ApiModelProperty(value = "回收记录主键 doc_recovery主键")
    private String recoveryId;

    /**
     * 分发号
     */
    @ApiModelProperty(value = "分发号")
    @ExcelProperty(value = "分发号",index = 2)
    private String distributeItemId;

    /**
     * 回收人员
     */
    @ApiModelProperty(value = "回收人员")
    private String userName;

    /**
     * 回收人员名称
     */
    @ApiModelProperty(value = "回收人员名称")
    private String nickName;

    /**
     * 回收状态
     */
    @ApiModelProperty(value = "回收状态")
    @ExcelProperty(value = "回收状态",converter = ExcelRecoveryStatusConvert.class,index = 6)
    private String status;

    /**
     * 打印状态 是否已打印 Y=已打印 N=未打印 未打印的不能回收
     */
    @ApiModelProperty(value = "回收状态")
    private String printStatus;

    /**
     * 签收人
     */
    @ApiModelProperty(value = "签收人")
    private String receiveUserName;

    /**
     * 签收人昵称
     */
    @ApiModelProperty(value = "签收人昵称")
    @ExcelProperty(value = "签收人",index = 4)
    private String receiveNickName;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    @ExcelProperty(value = "签收时间",index = 5)
    private Date receiveTime;

    /**
     * 数据来源 ADD=新增 UPDATE=修订等等
     */
    @ApiModelProperty(value = "数据来源 ADD=新增 UPDATE=修订等等 ",required = true)
    @ExcelProperty(value = "分发类型",converter = ExcelChangeTypeConvert.class,index = 3)
    private String changeType;

    /**
     * 操作类型（recovery：回收，lost：丢失）
     */
    @ApiModelProperty(value = "操作类型（recovery：回收，lost：丢失）",required = true)
    @ExcelProperty(value = "回收情况",converter = ExcelRecoveryTypeConvert.class,index = 7)
    private String type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "回收创建时间",index = 0)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @ExcelProperty(value = "回收时间",index = 8)
    private Date updateTime;

}
