package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件增发申请文件份数对象 doc_extra_apply_doc_number
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_extra_apply_doc_number")
public class ExtraApplyDocNumber   {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件类型
     */
    private String docName;
    /**
     * 申请数量
     */
    private Integer applyNum;
    /**
     * 分发部门
     */
    private String deptId;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String notPrint;
}
