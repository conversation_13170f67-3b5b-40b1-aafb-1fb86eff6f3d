package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/2/9 10:44
 * @Version 1.0
 * @Description
 */
@Data
@ApiModel("文件统计-变更要素视图对象")
@ExcelIgnoreUnannotated
public class DocStatisticsChangeFactorVo {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id", example="103")
    private String deptId;

    /**
     *文件类型
     * REC=记录文件
     * SMP=管理规程
     * SOP=操作规程
     * STP=技术标准
     */
    @ApiModelProperty(value = "文件类型", example="REC")
    private String docClass;

    /**
     * 人
     */
    @ApiModelProperty(value = "人数量", example="2")
    private int personNum;

    /**
     * 机数量
     */
    @ApiModelProperty(value = "机数量", example="5")
    private int machineNum;

    /**
     * 法数量
     */
    @ApiModelProperty(value = "法数量", example="3")
    private int lawNum;

    /**
     * 料数量
     */
    @ApiModelProperty(value = "料数量", example="1")
    private int materialNum;

    /**
     * 环数量
     */
    @ApiModelProperty(value = "环数量", example="1")
    private int linkNum;
    /**
     * 测数量
     */
    @ApiModelProperty(value = "测数量", example="1")
    private int testNum;

    /**
     * 总计
     */
    @ApiModelProperty(value = "总计", example="13")
    private int total;
}
