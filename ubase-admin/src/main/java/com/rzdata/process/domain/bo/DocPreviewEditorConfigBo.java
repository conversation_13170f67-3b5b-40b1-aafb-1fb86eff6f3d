package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.process.enums.OpenModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * onlyOffice定义的文档对象(不可随意修改)
 * @author: wangyang
 * @date: 2022年1月21日15:47:00
 */
@ApiModel("文档预览实体")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocPreviewEditorConfigBo implements Serializable {

    /**
     * 文档打开方式
     * {@link OpenModeEnum}
     * */
    private String mode;
    /**
     * 保存回调地址
     */
    private String callbackUrl;

    /**
     * 当前操作用户
     */
    private LoginUser user;
}
