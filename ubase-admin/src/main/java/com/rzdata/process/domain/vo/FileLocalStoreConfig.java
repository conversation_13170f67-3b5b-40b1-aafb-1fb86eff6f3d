package com.rzdata.process.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/4 下午2:34
 * @Version 1.0
 * @Desc 文件服务器本地存储参数
 */
@Data
public class FileLocalStoreConfig {
    /**
     * 配置的KEY
     */
    public static final String KEY_LOCAL_STORE = "file.local.store";
    /**
     * 上传文件存储在本地的根路径KEY
     */
    public static final String KEY_LOCAL_STORE_PATH = "file.local.store.path";
    /**
     * 域名或本机访问地址KEY
     */
    public static final String KEY_LOCAL_URL_DOMAIN = "file.local.url.domain";
    /**
     * 资源映射路径 前缀KEY
     */
    public static final String KEY_LOCAL_URL_PREFIX = "file.local.url.prefix";
    /**
     * 上传文件存储在本地的根路径
     */
    private String storePath;
    /**
     * 域名或本机访问地址
     */
    private String domain;
    /**
     * 资源映射路径 前缀
     */
    private String prefix;
}
