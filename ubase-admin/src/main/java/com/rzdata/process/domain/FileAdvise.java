package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件建议对象 doc_file_advise
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Data
@Accessors(chain = true)
@TableName("doc_file_advise")
public class FileAdvise {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件id
     */
    private String docId;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件类型code
     */
    private String docTypeCode;
    /**
     * 文件类型名称
     */
    private String docTypeName;
    /**
     * 版本Id
     */
    private String versionId;
    /**
     * 版本值
     */
    private String versionValue;
    /**
     * 编制部门Id
     */
    private String docDeptId;
    /**
     * 编制部门名称
     */
    private String docDeptName;
    /**
     * 建议
     */
    private String summary;
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    private String deptName;
}
