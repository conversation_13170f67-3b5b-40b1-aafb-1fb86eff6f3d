package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件回收记录对象 doc_recovery_log_item
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@Accessors(chain = true)
@TableName("doc_recovery_log_item")
public class RecoveryLogItem {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件版本ID
     */
    private String versionId;

    /**
     * 流程编号
     */
    private String applyId;

    /**
     * 回收部门id(也就是分发部门)
     */
    private String deptId;

    /**
     * 回收部门id(也就是分发部门)
     */
    private String deptName;

    /**
     * 回收记录主键 doc_recovery主键
     */
    private String recoveryId;

    /**
     * 分发号
     */
    private String distributeItemId;

    /**
     * 回收人员
     */
    private String userName;

    /**
     * 回收人员名称
     */
    private String nickName;

    /**
     * 回收状态  Y=已回收 N=未回收
     */
    private String status;

    /**
     * 数据来源 ADD=新增 UPDATE=修订 EXTRA=增发
     */
    private String changeType;

    /**
     * 操作类型（recovery：回收，lost：丢失）
     */
    private String type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
