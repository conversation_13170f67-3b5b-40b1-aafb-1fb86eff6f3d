package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件版本记录对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_version")
public class Version {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 标准文件主键
     */
    private String standardId;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 变更申请流水号
     */
    private String applyId;

    /**
     * 文件版本号
     */
    private String versionValue;
    /**
     * 版本生效时间
     */
    private Date startDate;
    /**
     * 版本生效截止时间
     */
    private Date endDate;
    /**
     * 版本状态 1=有效 2=失效 0=未知
     */
    private String status;
    /**
     * 变更原因
     */
    private String reason;
    /**
     * 上次复审时间
     */
    private Date reviewTime;

    /**
     * 内容概述
     */
    private String content;

    /**
     * 备注
     */
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    private String changeFactor;

    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否永久有效 0=否 1=是
     */
    private String forever;
    /**
     * 培训部门
     */
    private String trainDept;
    private String fileId;
    private String mergeFileId;
    private String encryptFileId;
}
