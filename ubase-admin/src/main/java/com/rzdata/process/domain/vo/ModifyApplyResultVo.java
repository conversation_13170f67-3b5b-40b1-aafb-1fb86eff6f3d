package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/12 下午5:31
 * @Version 1.0
 */
@Data
@ApiModel("文件增发申请保存成功后返回视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyResultVo {
    /**
     * 申请ID
     */
    @ExcelProperty(value = "申请ID")
    @ApiModelProperty("申请ID")
    private String id;

    /**
     * 流程定义ID
     */
    @ExcelProperty(value = "流程定义ID")
    @ApiModelProperty("流程定义ID")
    private String procDefId;

    /**
     * 当前环节ID
     */
    @ExcelProperty(value = "当前环节ID")
    @ApiModelProperty("当前环节ID")
    private String curActInstId;

    private ProcessInstanceModel processInstanceModel;
}
