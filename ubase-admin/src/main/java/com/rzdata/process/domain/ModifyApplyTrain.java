package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件变更操作申请培训记录对象 doc_modify_apply_train
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Data
@Accessors(chain = true)
@TableName("doc_modify_apply_train")
public class ModifyApplyTrain {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 文件ID,多条
     */
    private String fileIds;
    /**
     * 培训人
     */
    private String userName;
    /**
     * 培训人昵称
     */
    @TableField(exist = false)
    private String nickName;
    /**
     * 培训人部门编号
     */
    private String deptId;
    /**
     * 培训人部门名称
     */
    @TableField(exist = false)
    private Integer deptName;
    /**
     * 培训时间
     */
    private Date trainTime;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 文件版本ID
     */
    private String versionId;

    /**
     * 文件编号
     */
    private String docId;
}
