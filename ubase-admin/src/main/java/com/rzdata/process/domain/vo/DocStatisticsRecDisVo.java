package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/2/9 10:44
 * @Version 1.0
 * @Description
 */
@Data
@ApiModel("文件统计-分发回收视图对象")
@ExcelIgnoreUnannotated
public class DocStatisticsRecDisVo {

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", example="湖南分公司生产管理规范")
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", example="SEP-XXX")
    private String docId;

    /**
     * 文件版本
     */
    @ApiModelProperty(value = "文件版本", example="v1.1")
    private String versionValue;

    /**
     *文件类型
     * REC=记录文件
     * SMP=管理规程
     * SOP=操作规程
     * STP=技术标准
     */
    @ApiModelProperty(value = "文件类型", example="REC")
    private String docClass;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id", example="103")
    private String deptId;

    /**
     * 总分发数量
     */
    @ApiModelProperty(value = "总分发数量", example="2")
    private int distributeNum;

    /**
     * 总打印数
     */
    @ApiModelProperty(value = "总打印数", example="5")
    private int PrintNum;

    /**
     * 总回收数
     */
    @ApiModelProperty(value = "总回收数", example="3")
    private int recoveryNum;

    /**
     * 打印状态 0=未 1=已
     */
    @ApiModelProperty(value = "打印状态 0=未打印 1=已打印", example="1")
    private int printStatus;

    /**
     * 文件状态 0=失效 1=生效
     */
    @ApiModelProperty(value = "文件状态", example="1")
    private int fileStatus;

    /**
     * 回收状态
     */
    @ApiModelProperty(value = "回收状态", example="1")
    private int recoveryStatus;
}
