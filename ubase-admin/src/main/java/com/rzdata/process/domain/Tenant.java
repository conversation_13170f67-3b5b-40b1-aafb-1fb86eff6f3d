package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 客户对象 basic_Tenant
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@Data
@Accessors(chain = true)
@TableName("basic_tenant")
public class Tenant  {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 客户名称
     */
    private String tenantName;
    /**
     * 客户地址
     */
    private String address;
    /**
     * 联系人
     */
    private String contact;
    /**
     * 联系人电话
     */
    private String contactTel;

    /**
     * 该租户访问地址
     *
     */
    private String url;
}
