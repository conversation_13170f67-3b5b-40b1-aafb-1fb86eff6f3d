package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件分发记录条目对象 doc_distribute_item
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Getter
@TableName("doc_distribute_item")
public class DistributeItem  {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 文件分发记录ID
     */
    private String distributeId;
    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文档名称
     */
    private String docName;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 分发部门
     */
    private String deptId;

    /**
     * 分发部门名称
     */
    private String deptName;
    /**
     * 分发时间
     */
    private Date distributeTime;
    /**
     * 签收人
     */
    private String receiveUserName;
    /**
     * 签收人
     */
    private String receiveNickName;
    /**
     * 签收时间
     */
    private Date receiveTime;
    /**
     * 打印人
     */
    //private String printUserName;
    /**
     * 签收时间
     */
    //private Date printTime;
    /**
     * 打印次数
     */
    //private Long printTimes;
    /**
     * 回收人
     */
    //private String recoveryUserName;
    /**
     * 回收时间
     */
    //private Date recoveryTime;
    /**
     * 回收情况
     */
    //private String recoveryType;
    /**
     * 是否删除 1 是
     */
    //private Integer isDeleted;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
