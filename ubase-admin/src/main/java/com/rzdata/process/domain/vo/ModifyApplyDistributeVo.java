package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件变更操作申请分发视图对象 doc_modify_apply_distribute
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Data
@ApiModel("文件变更操作申请分发视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyDistributeVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String applyId;

    /**
     * 分发部门编号
     */
	@ExcelProperty(value = "分发部门编号")
	@ApiModelProperty("分发部门编号")
	private String deptId;

	private String deptName;

    /**
     * 分发数量
     */
	@ExcelProperty(value = "分发数量")
	@ApiModelProperty("分发数量")
	private Integer nums;

	/**
	 * 是否删除 1 是
	 */
	@ExcelProperty(value = "是否删除 1 是")
	@ApiModelProperty("是否删除 1 是")
	private Integer isDeleted;

	/**
	 * 是否分发不打印(0 否 1 是)
	 */
	@ApiModelProperty("是否分发不打印(0 否 1 是)")
	private String notPrint;
}
