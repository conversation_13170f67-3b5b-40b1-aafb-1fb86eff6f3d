package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.process.domain.WorkFlowInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 文件复审申请视图对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件复审申请视图对象")
@ExcelIgnoreUnannotated
public class ReviewApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 申请标题
     */
	@ExcelProperty(value = "申请标题")
	@ApiModelProperty("申请标题")
	private String applyTitle;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 复审标准，文件延长有效期，文件保持现状，文件修订，文件作废
     */
	@ExcelProperty(value = "复审标准，文件延长有效期，文件保持现状，文件修订，文件作废")
	@ApiModelProperty("复审标准，文件延长有效期，文件保持现状，文件修订，文件作废")
	private String reviewAction;

    /**
     * 申请部门
     */
	@ExcelProperty(value = "申请部门")
	@ApiModelProperty("申请部门")
	private String deptId;

    /**
     * 申请人
     */
	@ExcelProperty(value = "申请人")
	@ApiModelProperty("申请人")
	private String userName;

	/**
	 * 编制人
	 */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String editUserName;

    /**
     * 申请时间
     */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

    /**
     * 申请原因
     */
	@ExcelProperty(value = "申请原因")
	@ApiModelProperty("申请原因")
	private String reason;

    /**
     * 申请状态
     */
	@ExcelProperty(value = "申请状态")
	@ApiModelProperty("申请状态")
	private String status;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;

	@ExcelProperty(value = "编制文件id")
	@ApiModelProperty("编制文件id")
	private String fileId;

	@ExcelProperty(value = "合稿文件id")
	@ApiModelProperty("合稿文件id")
	private String mergeFileId;

	@ExcelProperty(value = "签章文件id")
	@ApiModelProperty("签章文件id")
	private String encryptFileId;

	@ExcelProperty(value = "有效期结束时间(有效期)")
	@ApiModelProperty("有效期结束时间(有效期)")
	private Date endDate;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("流程信息")
	private WorkflowLogVo workflowLogVo;

	@ApiModelProperty("流程信息")
	private WorkFlowInfo workFlowInfo;

	private String nickName;

	private String tenantId;

	private String docName;

	private String docClass;

	private Long reviewCycle;

	/**
	 * 文件复审清单信息
	 */
	private List<ReviewApplyItemVo> applyItemList;
}
