package com.rzdata.process.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:55
 * @Version 1.0
 * @Description
 */
@Data
public class ReprintLogVo {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 重打人
     */
    @ApiModelProperty("重打人")
    private String reprintUserName;

    /**
     * 重打人名称
     */
    @ApiModelProperty("重打人名称")
    private String reprintNickName;

    /**
     * 重打次数
     */
    @ApiModelProperty("重打次数")
    private int reprintNum;

    /**
     * 重打时间
     */
    @ApiModelProperty("重打时间")
    private Date reprintTime;

    /**
     * 打印记录表主键id
     */
    @ApiModelProperty("分发记录表主键id")
    private String disItemId;
}
