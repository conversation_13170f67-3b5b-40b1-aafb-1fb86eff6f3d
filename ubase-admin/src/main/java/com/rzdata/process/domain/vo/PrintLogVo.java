package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件打印记录视图对象 doc_print_log
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Data
@ApiModel("文件打印记录视图对象")
@ExcelIgnoreUnannotated
public class PrintLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

    /**
     * 文件分发编号
     */
//	@ExcelProperty(value = "文件分发编号")
//	@ApiModelProperty("文件分发编号")
//	private String distributeId;

    /**
     * 文件编号
     */
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 打印人员
     */
//	@ExcelProperty(value = "打印人员")
//	@ApiModelProperty("打印人员")
//	private String printUserName;

    /**
     * 打印时间
     */
//	@ExcelProperty(value = "打印时间")
//	@ApiModelProperty("打印时间")
//	private Date printTime;

//	@ApiModelProperty("打印状态 0：未，1：已")
//	private Integer status;

	@ApiModelProperty("分发部门id")
	private String deptId;

	/**
	 * 变更申请流水号
	 */
	@ApiModelProperty("变更申请流水号")
	private String applyId;

	/**
	 * 分发数量
	 */
//	@ExcelProperty(value = "分发数量")
//	@ApiModelProperty("分发数量")
//	private Long nums;

	/**
	 * 打印数量（第一次分发数+增发数量+补发数量）
	 */
	@ApiModelProperty("打印数量（第一次分发数+增发数量+补发数量）")
	private int printNums;

	/**
	 * 已打印数量
	 */
	@ApiModelProperty("已打印数量")
	private int printedNums;

	/**
	 * 分发份数=第一次分发数+增发数量
	 */
	@ApiModelProperty("分发份数=第一次分发数+增发数量")
	private int distributeNums;

	@ApiModelProperty("分发时间")
	private Date distributeTime;
	/**
	 * 文档编制部门
	 */
	@ApiModelProperty("文档编制部门")
	private Long compileDeptId;

	/**
	 * 文档编制部门名称
	 */
	@ApiModelProperty("文档编制部门名称")
	private String compileDeptName;
	/**
	 * 签收人
	 */
//	@ExcelProperty(value = "签收人")
//	@ApiModelProperty("签收人")
//	private String receiveUserName;


	/**
	 * 签收时间
	 */
//	@ExcelProperty(value = "签收时间")
//	@ApiModelProperty("签收时间")
//	private Date receiveTime;

	/**
	 * 版本号
	 */
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ApiModelProperty("版本ID")
	private String versionId;

	@ApiModelProperty("分发部门名称")
	private String deptName;

	@ApiModelProperty("文件名称")
	private String docName;

	@ApiModelProperty("文件类型")
	private String docClass;

	@ApiModelProperty("生效期")
	private Date startDate;

	@ApiModelProperty("基础信息")
	private VersionVo versionVo;

//	@ApiModelProperty("文件类型")
//	private String typeName;
//
//	@ApiModelProperty("明细")
//	private List<DistributeItem> distributeItemList;
}
