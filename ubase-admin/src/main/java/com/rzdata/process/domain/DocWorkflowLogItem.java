package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 【请填写功能名称】对象 doc_workflow_log_item
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Data
@Accessors(chain = true)
@TableName("doc_workflow_log_item")
public class DocWorkflowLogItem extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 接收人
     */
    private String receiverMan;
    /**
     * 接收人组织
     */
    private String receiverOrg;
    /**
     * doc_workflow_log主键
     */
    private String workflowLogId;

}
