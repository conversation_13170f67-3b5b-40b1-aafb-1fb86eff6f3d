package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件借阅选择借阅人对象 doc_borrow_apply_user
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_borrow_apply_user")
public class BorrowApplyUser {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 借阅人
     */
    private String borrowUser;

    /**
     * 文档编号
     */
    private String docId;

    /**
     * 编制部门id
     */
    private String deptId;

    /**
     * 编制部门名称
     */
    private String deptName;

    /**
     * 文档名称
     */
    private String docName;

    /**
     * 文档名称
     */
    private String docClass;

    /**
     * 版本id
     */
    private String versionId;

    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
