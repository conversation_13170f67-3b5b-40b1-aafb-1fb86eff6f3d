package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.*;


/**
 * 文件变更操作申请分发业务对象 doc_modify_apply_distribute
 *
 * <AUTHOR>
 * @date 2022-01-05
 */

@Data
@ApiModel("文件变更操作申请分发业务对象")
public class ModifyApplyDistributeBo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = false)
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = false)
    private String applyId;

    /**
     * 分发部门编号
     */
    @ApiModelProperty(value = "分发部门编号", required = true)
    @NotNull(message = "分发部门编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 分发数量
     */
    @ApiModelProperty(value = "分发数量", required = true)
    @NotNull(message = "分发数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer nums;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是")
    private Integer isDeleted;

    private String changeId;

    /**
     * 是否分发不打印(0 否 1 是)
     */
    @ApiModelProperty("是否分发不打印(0 否 1 是)")
    private String notPrint;

}
