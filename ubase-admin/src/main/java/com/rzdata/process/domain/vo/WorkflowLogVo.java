package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 流程审批记录视图对象 doc_workflow_log
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("流程审批记录视图对象")
@ExcelIgnoreUnannotated
public class WorkflowLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

	@ApiModelProperty("主键")
	private String flowId;

    /**
     * 流程实例ID
     */
	@ExcelProperty(value = "流程实例ID")
	@ApiModelProperty("流程实例ID")
	private String procInstId;

    /**
     * 业务ID
     */
	@ExcelProperty(value = "业务ID")
	@ApiModelProperty("业务ID")
	private String businessId;

    /**
     * 环节定义名称
     */
	@ExcelProperty(value = "环节定义名称")
	@ApiModelProperty("环节定义名称")
	private String actDefName;

    /**
     * 环节实例ID
     */
	@ExcelProperty(value = "环节实例ID")
	@ApiModelProperty("环节实例ID")
	private String actInstId;

    /**
     * 是否同意
     */
	@ExcelProperty(value = "是否同意")
	@ApiModelProperty("是否同意")
	private String yNApprove;

    /**
     * 意见
     */
	@ExcelProperty(value = "意见")
	@ApiModelProperty("意见")
	private String opinion;

    /**
     * 发送人
     */
	@ExcelProperty(value = "发送人")
	@ApiModelProperty("发送人")
	private String sender;

    /**
     * 接收人
     */
	@ExcelProperty(value = "接收人")
	@ApiModelProperty("接收人")
	private String receiver;

    /**
     * 环节处理状态
     */
	@ExcelProperty(value = "环节处理状态")
	@ApiModelProperty("环节处理状态")
	private String actStatus;

	@ApiModelProperty("流程实例key")
	private String procDefKey;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("审批人名称")
	private String userName;

	@ApiModelProperty("部门名称")
	private String deptName;

	@ApiModelProperty("职位名称")
	private String postName;

	private String nickName;
	/**
	 * 流程是否通过 true=通过 false=不通过
	 */
	private Boolean pass;

	private String actName;

}
