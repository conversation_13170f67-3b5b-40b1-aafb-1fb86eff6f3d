package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件回收记录对象 doc_recovery_log
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@Accessors(chain = true)
@TableName("doc_recovery_log")
public class RecoveryLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件版本ID
     */
    private String versionId;

    /**
     * 文件版本
     */
    private String versionValue;

    /**
     * 流程编号
     */
    private String applyId;

    /**
     * 回收人员所在部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 待回收数
     */
    private Integer noRecyclNum;

    /**
     * 已回收数
     */
    private int recyclNum;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 编制部门id
     */
    private Long compileDeptId;

    /**
     * 编制部门名称
     */
    private String compileDeptName;

    /**
     * 分发时间
     */
    private Date DistributeTime;
}
