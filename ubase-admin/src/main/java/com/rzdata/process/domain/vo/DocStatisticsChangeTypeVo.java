package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/2/9 10:44
 * @Version 1.0
 * @Description
 */
@Data
@ApiModel("文件统计-变更类型视图对象")
@ExcelIgnoreUnannotated
public class DocStatisticsChangeTypeVo {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id", example="103")
    private String deptId;

    /**
     *文件类型
     * REC=记录文件
     * SMP=管理规程
     * SOP=操作规程
     * STP=技术标准
     */
    @ApiModelProperty(value = "文件类型", example="REC")
    private String docClass;

    /**
     * 新增数量
     */
    @ApiModelProperty(value = "新增数量", example="10")
    private int addNum;

    /**
     * 修订数量
     */
    @ApiModelProperty(value = "修订数量", example="1")
    private int updateNum;

    /**
     * 作废数量
     */
    @ApiModelProperty(value = "作废数量", example="2")
    private int disuseNum;

    /**
     * 总计
     */
    @ApiModelProperty(value = "总计", example="13")
    private int total;
}
