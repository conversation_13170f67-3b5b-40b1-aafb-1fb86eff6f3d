package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件变更操作申请培训记录对象 doc_modify_signature_log
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Data
@Accessors(chain = true)
@TableName("doc_modify_signature_log")
public class ModifySignature {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 流程编号
     */
    private String applyId;

    /**
     * 签章地址
     */
    private String signatureUrl;

    /**
     * 创建时间
     */
    private Date createTime;
}
