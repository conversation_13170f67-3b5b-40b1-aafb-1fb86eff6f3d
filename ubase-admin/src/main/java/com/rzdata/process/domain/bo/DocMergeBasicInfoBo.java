package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 文件合稿基础信息对象
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
public class DocMergeBasicInfoBo{

    /**
     * 模板Id
     */
    @ApiModelProperty(value = "模板Id", required = true)
    private String templateDocId;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 文件类型名称
     */
    @ApiModelProperty(value = "文件类型名称", required = true)
    @NotBlank(message = "文件类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClassName;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型", required = true)
    @NotBlank(message = "变更类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeType;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id", required = true)
    private String fileId;

    /**
     * 文件版本
     */
    @ExcelProperty(value = "文件版本")
    @ApiModelProperty("文件版本")
    private String versionValue;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
    private String deptId;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门", required = true)
    private String deptName;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String userName;

    /**
     * 编制时间
     */
    @ApiModelProperty(value = "编制时间", required = true)
    private String dateTime;

    /**
     * 文件生效时间
     */
    @ApiModelProperty(value = "文件生效时间", required = true)
    private String validDateTime;

    /**
     * 文件有效时间
     */
    @ApiModelProperty(value = "文件有效时间", required = true)
    private String reviewDateTime;

    /**
     * 当前变更版本
     */
    @ApiModelProperty(value = "当前变更版本", required = true)
    private String currentChangeVersion;

    /**
     * 当前生效版本
     */
    @ApiModelProperty(value = "当前生效版本", required = true)
    private String currentValidityVersion;

    /**
     * 附件，多个以,隔开
     */
    @ApiModelProperty(value = "附件，多个以,隔开", required = true)
    private String appendixs;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 变更内容
     */
    @ApiModelProperty(value = "变更内容")
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
