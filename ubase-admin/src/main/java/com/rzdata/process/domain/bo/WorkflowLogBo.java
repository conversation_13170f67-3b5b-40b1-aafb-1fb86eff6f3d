package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 流程审批记录业务对象 doc_workflow_log
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("流程审批记录业务对象")
public class WorkflowLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID", required = true)
    private String procInstId;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID", required = true)
    private String businessId;

    /**
     * 环节定义名称
     */
    @ApiModelProperty(value = "环节定义名称", required = true)
    private String actDefName;

    /**
     * 环节实例ID
     */
    @ApiModelProperty(value = "环节实例ID", required = true)
    private String actInstId;

    /**
     * 是否同意
     */
    @ApiModelProperty(value = "是否同意", required = true)
    private String yNApprove;

    /**
     * 意见
     */
    @ApiModelProperty(value = "意见", required = true)
    private String opinion;

    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人", required = true)
    private String sender;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", required = true)
    private String receiver;

    /**
     * 是否通过
     */
    @ApiModelProperty(value = "是否通过 true=通过 false=不通过", required = true)
    private Boolean pass;

    /**
     * 环节处理状态
     */
    @ApiModelProperty(value = "环节处理状态", required = true)
    private String actStatus;

    @ApiModelProperty("流程实例key")
    private String procDefKey;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;


    private String actName;

    /**
     * 接收人
     */
    private String receiverMan;

}
