package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 文件分发记录条目业务对象 doc_distribute_item
 *
 * <AUTHOR>
 * @date 2022-01-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分发记录条目业务对象")
public class DistributeItemBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 文件分发记录ID
     */
    @ApiModelProperty(value = "文件分发记录ID", required = true)
    @NotBlank(message = "文件分发记录ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributeId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 分发部门名称
     */
    @ApiModelProperty(value = "分发部门名称", required = true)
    private String deptName;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    @NotBlank(message = "文件版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 分发部门
     */
    @ApiModelProperty(value = "分发部门", required = true)
    @NotNull(message = "分发部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 分发时间
     */
    @ApiModelProperty(value = "分发时间", required = true)
    @NotNull(message = "分发时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date distributeTime;

    /**
     * 签收人
     */
    @ApiModelProperty(value = "签收人", required = true)
    @NotBlank(message = "签收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveUserName;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间", required = true)
    @NotNull(message = "签收时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date receiveTime;

    /**
     * 打印人
     */
    @ApiModelProperty(value = "打印人", required = true)
    @NotBlank(message = "打印人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printUserName;

    /**
     * 打印时间
     */
    @ApiModelProperty(value = "打印时间", required = true)
    @NotNull(message = "打印时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date printTime;

    /**
     * 打印次数
     */
    @ApiModelProperty(value = "打印次数", required = true)
    @NotNull(message = "打印次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long printTimes;

    /**
     * 回收人
     */
    @ApiModelProperty(value = "回收人", required = true)
    @NotBlank(message = "回收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recoveryUserName;

    /**
     * 回收时间
     */
    @ApiModelProperty(value = "回收时间", required = true)
    @NotNull(message = "回收时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date recoveryTime;

    /**
     * 回收情况
     */
    @ApiModelProperty(value = "回收情况", required = true)
    @NotBlank(message = "回收情况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recoveryType;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是", required = true)
    @NotNull(message = "是否删除 1 是不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isDeleted;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
