package com.rzdata.process.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:00
 * @Version 1.0
 * @Description
 */
@Data
@ApiModel("消息返回实体")
public class DocMessageVo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "消息主键id",example = "xxxx")
    private String id;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id",example = "xxxx")
    private String applyId;

    /**
     * 文档id
     */
    @ApiModelProperty(value = "业务id",example = "SEP-125")
    private String docId;

    /**
     * 文档名称
     */
    @ApiModelProperty(value = "文档名称",example = "测试文档")
    private String docName;

    /**
     * 文档版本号
     */
    @ApiModelProperty(value = "文档版本号",example = "1.0")
    private String versionValue;

    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型",example = "REC")
    private String docClass;

    /**
     * 文档所属部门
     */
    @ApiModelProperty(value = "文档所属部门",example = "103")
    private String deptId;

    /**
     * 文档所属部门名称
     */
    @ApiModelProperty(value = "文档所属部门名称",example = "生产部")
    private String deptName;

    /**
     * 消息内容 用于显示 通过/未通过
     */
    @ApiModelProperty(value = "消息内容",example = "通过/不通过/待签收等等")
    private String msgInfo;

    /**
     * 消息状态 0=未读 1=已读
     */
    @ApiModelProperty(value = "消息状态 0=未读 1=已读",example = "0")
    private int msgStatus;

    /**
     * 消息类型 1=文件复审 2=文件到期 3=文件变更申请 4=文件借阅申请 5=文件补发申请 6=文件增发申请 7=文件复审申请 8=文件代签收 9=文件失效 10=文件生效
     */
    @ApiModelProperty(value = "消息类型 1=文件复审 2=文件到期 3=文件变更申请 4=文件借阅申请 5=文件补发申请 6=文件增发申请 7=文件复审申请 8=文件代签收 9=文件失效 10=文件生效",example = "1")
    private Integer msgClass;

    /**
     * 消息发送时间
     */
    @ApiModelProperty(value = "消息发送时间",example = "2022-02-12 12:00:00")
    private Date createTime;

    /**
     * 消息创建人
     */
    @ApiModelProperty(value = "消息创建人",example = "admin")
    private String createUser;

    /**
     * 消息创建人id
     */
    @ApiModelProperty(value = "消息创建人id",example = "123")
    private String createUserId;

    /**
     * 消息接受(已读)时间
     */
    @ApiModelProperty(value = "消息接受(已读)时间",example = "2022-02-12 12:00:00")
    private Date recoveryTime;

    /**
     * 消息接受人
     */
    @ApiModelProperty(value = "消息接受人",example = "user")
    private String recoveryUser;

    /**
     * 消息接受人id
     */
    @ApiModelProperty(value = "消息接收人id",example = "123")
    private String recoveryUserId;
}
