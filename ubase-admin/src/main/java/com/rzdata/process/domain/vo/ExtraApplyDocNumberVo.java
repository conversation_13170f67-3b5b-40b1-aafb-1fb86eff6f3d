package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件增发申请文件份数视图对象 doc_extra_apply_doc_number
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件增发申请文件份数视图对象")
@ExcelIgnoreUnannotated
public class ExtraApplyDocNumberVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String applyId;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 申请数量
     */
	@ExcelProperty(value = "申请数量")
	@ApiModelProperty("申请数量")
	private Long applyNum;

    /**
     * 是否删除 1 是
     */
	@ExcelProperty(value = "是否删除 1 是")
	@ApiModelProperty("是否删除 1 是")
	private Long isDeleted;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("是否分发不打印(0 否 1 是)")
	private String notPrint;
}
