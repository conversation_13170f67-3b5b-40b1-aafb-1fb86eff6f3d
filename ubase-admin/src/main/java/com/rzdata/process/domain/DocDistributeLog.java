package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.rzdata.process.enums.ApplyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件分发记录对象 doc_distribute_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Data
@Accessors(chain = true)
@TableName("doc_distribute_log")
public class DocDistributeLog {

    private static final long serialVersionUID=1L;

    /**
     * 文件分发编号，根据分发编码规则生成
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 变更申请流水号
     */
    private String applyId;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 文件名称
     */
    private String docName;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 分发部门
     */
    private String deptId;

    /**
     * 分发部门名称
     */
    private String deptName;
    /**
     * 文档编制部门
     */
    private String compileDeptId;

    /**
     * 文档编制部门
     */
    private String compileDeptName;

    /**
     * 分发数量
     */
    private int nums;
    /**
     * 签收人
     */
    private String receiveUserName;

    /**
     * 签收人昵称
     */
    private String receiveNickName;

    /**
     * 分发人
     */
    private String distributeUserName;

    /**
     * 分发人昵称
     */
    private String distributeUserNickName;
    /**
     * 分发时间
     */
    private Date distributeTime;
    /**
     * 签收状态 1=签收 0=未签收
     */
    private String receiveStatus;
    /**
     * 签收时间
     */
    private Date receiveTime;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 变更类型
     */
    private ApplyTypeEnum changeType;

    /**
     * 上一版本 用于修订
     */
    private String previousVersionValue;

    /**
     * 打印状态 0=未打印 1=已打印
     */
    //private Integer printStatus;

    /**
     * 数据库状态 0=未删除 1=已删除
     */
    private Integer dbStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty("是否分发不打印(0 否 1 是)")
    private String notPrint;
}
