package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文件变更操作申请视图对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更操作申请视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyVo implements java.io.Serializable{

	private static final long serialVersionUID = 1L;

    /**
     * 主键，根据编码规则生成
     */
	@ExcelProperty(value = "主键，根据编码规则生成")
	@ApiModelProperty("主键，根据编码规则生成")
	private String id;

    /**
     * 变更类型
     */
	@ExcelProperty(value = "变更类型")
	@ApiModelProperty("变更类型")
	private String changeType;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 编制部门编号
     */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

	private String deptName;

    /**
     * 编制人
     */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

	@TableField(exist = false)
	private String nickName;
    /**
     * 申请时间
     */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

    /**
     * 文件有效期
     */
	@ExcelProperty(value = "文件有效期")
	@ApiModelProperty("文件有效期")
	private Integer expiration;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;
    /**
     * 是否培训
     */
	@ExcelProperty(value = "是否培训")
	@ApiModelProperty("是否培训")
	private String yNTrain;

    /**
     * 培训部门
     */
	@ExcelProperty(value = "培训部门")
	@ApiModelProperty("培训部门")
	private String trainDept;

    /**
     * 是否已合稿
     */
	@ExcelProperty(value = "是否已合稿")
	@ApiModelProperty("是否已合稿")
	private String yNMergeDraft;

    /**
     * 是否已签章
     */
	@ExcelProperty(value = "是否已签章")
	@ApiModelProperty("是否已签章")
	private String yNEncrypt;

    /**
     * 是否已分发
     */
	@ExcelProperty(value = "是否已分发")
	@ApiModelProperty("是否已分发")
	private String yNDistribute;

    /**
     * 编制文件编号
     */
	@ExcelProperty(value = "编制文件编号")
	@ApiModelProperty("编制文件编号")
	private String fileId;

    /**
     * 记录状态
     */
	@ExcelProperty(value = "记录状态")
	@ApiModelProperty("记录状态")
	private String recordStatus;

	/**
	 * 流程状态
	 */
	@ExcelProperty(value = "流程状态")
	@ApiModelProperty("流程状态")
	private String processStatus;

    /**
     * 变更原因
     */
	@ExcelProperty(value = "变更原因")
	@ApiModelProperty("变更原因")
	private String changeReason;

    /**
     * 内容概述
     */
	@ExcelProperty(value = "内容概述")
	@ApiModelProperty("内容概述")
	private String content;

    /**
     * 备注
     */
	@ExcelProperty(value = "备注")
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ExcelProperty(value = "变更要素，多个以,隔开")
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	//@ApiModelProperty(value = "当前生效版本")
	//private StandardVo file;
	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "编制正文(变更版本主文件)")
	private ModifyApplyLinkVo standardDoc;

	/**
	 * 备注附件
	 */
	@ApiModelProperty(value = "备注附件")
	private List<ModifyApplyLinkVo> remarkDoc;

	/**
	 * 编制正文
	 */
	@ApiModelProperty(value = "上个编制正文(当前生效版本主文件)")
	private DocLinkLogVo preStandardDoc;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件文件(变更版本附件)")
	private List<ModifyApplyLinkVo> appendixes;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "上个附件文件(当前生效版本附件)")
	private List<DocLinkLogVo> preAppendixes;

	/**
	 * 分发部门
	 */
	@ApiModelProperty(value = "分发部门", required = true)
	private List<ModifyApplyDistributeVo> distributeDepths;

	/**
	 * 关联文件
	 */
	@ApiModelProperty(value = "关联文件")
	private List<ModifyApplyLinkVo> docLinks;

	/**
	 * 关联或新增记录
	 */
	@ApiModelProperty(value = "关联或新增记录")
	private List<ModifyApplyLinkVo> recordLinks;

	/**
	 * 历史版本
	 */
	@ApiModelProperty(value = "历史版本")
	private List<VersionVo> versions;

	/**
	 * 培训记录
	 */
	@ApiModelProperty(value = "培训记录")
	private List<ModifyApplyTrainVo> trains;

	/**
	 * 签章链接
	 */
	@ApiModelProperty(value = "签章链接")
	private String signatureUrl;

	/**
	 * 是否已签章 Y=已 N=未 默认N
	 */
	@ApiModelProperty(value = "是否已签章 Y=已 N=未 默认N")
	private String isSignature;

	private String changeId;

	/**
	 * 设置生效时间
	 */
	private Date setupTime;
}
