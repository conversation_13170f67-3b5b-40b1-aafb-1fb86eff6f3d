package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 文件变更申请业务对象 doc_change_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件合稿申请业务对象")
public class DocMergeBo extends BaseEntity {

    /**
     * 基础信息
     */
    @ApiModelProperty(value = "基础信息",required = true)
    private DocMergeBasicInfoBo basicInfo;

    /**
     * 审批流程 暂时确定不了节点 用object
     */
    @ApiModelProperty(value = "审批流程")
    private DocMergeApprovalTemplateBo approvalRecord;

    /**
     * 页眉和页码的模板
     */
    @ApiModelProperty(value = "页眉和页码模板")
    private DocMergeHeadAndFootTemplateBo headFootInfo;

    /**
     * 是否使用新页眉模板
     */
    @ApiModelProperty(value = "是否使用新页眉模板")
    private boolean useNewHeadFoot;
    /**
     * 是否合并基础信息
     */
    @ApiModelProperty(value = "是否合并基础信息")
    private boolean mergeBasicInfo;

    /**
     * 是否合并审批流程记录
     */
    @ApiModelProperty(value = "是否合并审批流程记录")
    private boolean mergeApprovalRecord;

    @ApiModelProperty("流程ID")
    private String applyId;
}
