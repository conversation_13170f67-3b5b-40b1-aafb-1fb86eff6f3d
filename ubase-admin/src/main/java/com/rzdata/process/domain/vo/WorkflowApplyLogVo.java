package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 流程申请记录视图对象 doc_workflow_apply_log
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Data
@ApiModel("流程申请记录视图对象")
@ExcelIgnoreUnannotated
public class WorkflowApplyLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 流程类别
     */
	@ExcelProperty(value = "流程类别")
	@ApiModelProperty("流程类别")
	private String applyClass;

    /**
     * 流程处理状态
     */
	@ExcelProperty(value = "流程处理状态")
	@ApiModelProperty("流程处理状态")
	private String applyStatus;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 文件版本ID
     */
	@ExcelProperty(value = "文件版本ID")
	@ApiModelProperty("文件版本ID")
	private String versionId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本号")
	@ApiModelProperty("文件版本号")
	private String versionValue;

    /**
     * 发送人
     */
	@ExcelProperty(value = "发送人")
	@ApiModelProperty("发送人")
	private String sender;

    /**
     * 编制部门编号
     */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

    /**
     * 流程实例ID
     */
	@ExcelProperty(value = "流程实例ID")
	@ApiModelProperty("流程实例ID")
	private String procInstId;

    /**
     * 流程实例key
     */
	@ExcelProperty(value = "流程实例key")
	@ApiModelProperty("流程实例key")
	private String procDefKey;

    /**
     * 申请时间
     */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("当前环节名称")
	private String actDefName;

	private String userName;

	@ApiModelProperty("部门名称")
	private String deptName;

	@ApiModelProperty("变更原因")
	private String changeType;

	//@ApiModelProperty("当前生效版本")
	//private String validityVersion;

	//@ApiModelProperty("生效时间")
	//private Date validityTime;

	/**
	 * 流程状态
	 */
	private String procStatus;

}
