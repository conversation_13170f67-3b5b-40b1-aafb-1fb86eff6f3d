package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionExportVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 变更申请流水号
     */
	@ApiModelProperty("变更申请流水号")
	private String applyId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本号")
	private String versionValue;

	@ExcelProperty(value = "编制部门")
	@ApiModelProperty("部门名称")
	private String deptName;




    /**
     * 版本状态
     */
	@ExcelProperty(value = "文件状态")
	@ApiModelProperty("版本状态")
	private String status;

    /**
     * 变更原因
     */
	@ApiModelProperty("变更原因")
	private String reason;

	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClass;



	/**
	 * 编制部门编号
	 */

	@ApiModelProperty("编制部门编号")
	private String deptId;

	/**
	 * 编制人
	 */
	@ApiModelProperty("编制人")
	private String userName;

	/**
	 * 申请时间
	 */
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 编制文件编号
	 */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	@ApiModelProperty("合稿文件编号")
	private String mergeFileId;

	/**
	 * 加密文件编号
	 */
	@ApiModelProperty("加密文件编号")
	private String encryptFileId;


	/**
	 * 上次复审时间
	 */
	@ApiModelProperty("上次复审时间")
	private Date reviewTime;

	/**
	 * 版本生效时间
	 */
	@ExcelProperty(value = "发布时间")
	@ApiModelProperty("版本生效时间")
	private Date startDate;

	/**
	 * 版本生效截止时间
	 */
	@ExcelProperty(value = "失效时间")
	@ApiModelProperty("版本生效截止时间")
	private Date endDate;
}
