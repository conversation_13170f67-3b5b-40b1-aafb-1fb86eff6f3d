package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.process.domain.vo.VersionVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 文件借阅申请业务对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件借阅申请业务对象")
public class BorrowApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 文件编号,多选
     */
    @ApiModelProperty(value = "文件编号,多选", required = true)
    @NotBlank(message = "文件编号,多选不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docIds;

    /**
     * 补发部门
     */
    @ApiModelProperty(value = "补发部门", required = true)
    private String deptId;

    /**
     * 借阅开始时间
     */
    @ApiModelProperty(value = "借阅开始时间", required = true)
//    @NotNull(message = "借阅开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 借阅结束时间
     */
    @ApiModelProperty(value = "借阅结束时间", required = true)
//    @NotNull(message = "借阅结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    private Integer dayTime;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String applyReason;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;

    @ApiModelProperty("借阅人，多个")
    private List<String> borrowUsers;

    private String borrowUser;

    @ApiModelProperty("文件名称")
    private String docNames;

    @ApiModelProperty("文件类型")
    private String docClass;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private BpmClientInputModelBo bpmClientInputModel;

    /**
     * 变更类型
     */
    private String changeType;

    private List<VersionVo> docInfos;

}
