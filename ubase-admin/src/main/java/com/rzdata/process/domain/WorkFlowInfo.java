package com.rzdata.process.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;

/**
 * @auther xcy
 * @create 2021-12-30 15:41
 */
@Data
public class WorkFlowInfo {

    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("当前活动定义名称")
    private String curActDefName;

    @ApiModelProperty("父流程Id")
    private String parentProcInstId;

    @ApiModelProperty("接收人姓名")
    private String recUserName;

    @ApiModelProperty("前一活动名称")
    private String prevActDefName;

    @ApiModelProperty("流程定义key")
    private String procDefKey;

    @ApiModelProperty("接收人Id")
    private String recUserId;

    @ApiModelProperty("发送人组织名称")
    private String sendOrgName;

    @ApiModelProperty("流程定义Id")
    private String procDefId;

    @ApiModelProperty("发送时间")
    private String sendTime;

    @ApiModelProperty("处理时间")
    private String dealTime;

    @ApiModelProperty("流程定义名称")
    private String procDefName;

    private String dataSource;

    @ApiModelProperty("待办：1 已办：2 办结：3 待阅：4 已阅：5 作废：6 删除：7")
    private String status;

    @ApiModelProperty("桌面端访问协议")
    private String cportalProtocol;

    @ApiModelProperty("发送人组织Id")
    private String sendOrgId;

    private String serviceCode;

    @ApiModelProperty("前一活动定义Id")
    private String prevActDefId;

    @ApiModelProperty("手机端访问协议")
    private String mportalProtocol;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("手机端URL")
    private String mportalUrl;

    @ApiModelProperty("顶级流程Id")
    private String topProcInstId;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("发送人Id")
    private String sendUserId;

    @ApiModelProperty("流程起草人Id")
    private String startUserId;

    @ApiModelProperty("流程起草人姓名")
    private String startUserName;

    @ApiModelProperty("当前活动处理时限")
    private String curActLimitTime;

    @ApiModelProperty("发送人姓名")
    private String sendUserName;

    @ApiModelProperty("当前活动实例Id")
    private String curActInstId;

    @ApiModelProperty("起草时间")
    private String startTime;

    @ApiModelProperty("前一活动实例Id")
    private String prevActInstId;

    @ApiModelProperty("流程实例Id")
    private String procInstId;

    @ApiModelProperty("桌面端URL")
    private String url;

    private String wportalProtocol;

    @ApiModelProperty("接收人组织名称")
    private String recOrgName;

    @ApiModelProperty("接收人组织Id")
    private String recOrgId;

    @ApiModelProperty("当前活动定义Id")
    private String curActDefId;

    @ApiModelProperty("业务数据")
    private Object businessData;

    //是否删除,1:正常 0：删除
    private Integer flag = 1;

    private String docName;
}

