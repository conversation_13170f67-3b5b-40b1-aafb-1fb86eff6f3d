package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件变更申请视图对象 doc_change_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更申请视图对象")
@ExcelIgnoreUnannotated
public class DocChangeApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 变更编号
     */
	@ExcelProperty(value = "变更编号")
	@ApiModelProperty("变更编号")
	private String id;

    /**
     * 申请标题
     */
	@ExcelProperty(value = "申请标题")
	@ApiModelProperty("申请标题")
	private String applyTitle;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 变更类型
     */
	@ExcelProperty(value = "变更类型")
	@ApiModelProperty("变更类型")
	private String changeType;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;

    /**
     * 编制部门编号
     */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

    /**
     * 编制人
     */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

    /**
     * 状态
     */
	@ExcelProperty(value = "状态")
	@ApiModelProperty("状态")
	private String status;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 下一流程的发起人
	 */
	@ExcelProperty(value = "下一流程的发起人")
	@ApiModelProperty("下一流程的发起人")
	private String editUserName;

	/**
	 * 下一流程的发起人部门
	 */
	@ExcelProperty(value = "下一流程的发起人部门")
	@ApiModelProperty("下一流程的发起人部门")
	private String editDeptId;

	/**
	 * 附件，多个以,隔开
	 */
	@ExcelProperty(value = "附件ID，多个以,隔开")
	@ApiModelProperty("附件ID，多个以,隔开")
	private String appendixs;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ExcelProperty(value = "变更要素，多个以,隔开")
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

	/**
	 * 变更原因
	 */
	private String changeReason;
	/**
	 * 内容概述
	 */
	private String content;
	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 编制人
	 */
	private String standUserName;

	/**
	 * 编制部门
	 */
	private String standDeptName;

	/**
	 * 申请人
	 */
	private String applyUserName;

}
