package com.rzdata.process.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 文档响应实体类 不能修改 onlyOffice固定为code = 0/1这种格式
 * @author: wangyang
 * @date: 2022年1月24日10:26:47
 */
@Data
public class DocPreviewEditCallbackResponseBo<T>  implements Serializable {
    private int error;

    private T data;


    public DocPreviewEditCallbackResponseBo(int error) {
        this.error = error;
    }


    private DocPreviewEditCallbackResponseBo<T> data(T data){
        this.data = data;
        return this;
    }

    public static <T>DocPreviewEditCallbackResponseBo<T> success(T data){
        return success().data(data);
    }

    public static DocPreviewEditCallbackResponseBo success(){
        return new DocPreviewEditCallbackResponseBo(0);
    }

    public static DocPreviewEditCallbackResponseBo failue(){
        return new DocPreviewEditCallbackResponseBo(1);
    }
}
