package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 文件建议业务对象 doc_file_advise
 *
 * <AUTHOR>
 * @date 2022-01-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件建议业务对象")
public class FileAdviseBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件id
     */
    @ApiModelProperty(value = "文件id", required = true)
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 文件类型code
     */
    @ApiModelProperty(value = "文件类型code", required = true)
    private String docTypeCode;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 文件类型名称
     */
    @ApiModelProperty(value = "文件类型名称", required = true)
    private String docTypeName;

    /**
     * 版本Id
     */
    @ApiModelProperty(value = "版本Id", required = true)
    private String versionId;

    /**
     * 版本值
     */
    @ApiModelProperty(value = "版本值", required = true)
    private String versionValue;

    /**
     * 编制部门Id
     */
    @ApiModelProperty(value = "编制部门Id", required = true)
    private String docDeptId;

    /**
     * 编制部门名称
     */
    @ApiModelProperty(value = "编制部门名称", required = true)
    private String docDeptName;

    /**
     * 建议
     */
    @ApiModelProperty(value = "建议", required = true)
    private String summary;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id", required = true)
    private String deptId;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称", required = true)
    private String createName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true)
    private String createUser;

    private String searchType;

    private String deptName;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
