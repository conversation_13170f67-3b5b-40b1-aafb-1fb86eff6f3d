package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件借阅申请对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_borrow_apply")
public class BorrowApply  {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 文件编号,多选
     */
    //private String docIds;
    /**
     * 补发部门
     */
    private String deptId;
    /**
     * 借阅开始时间
     */
    private Date startTime;
    /**
     * 借阅结束时间
     */
    private Date endTime;
    /**
     * 申请人
     */
    private String userName;
    /**
     * 申请原因
     */
    private String applyReason;
    /**
     * 申请状态
     */
    private String status;

    /**
     * 流程状态 {@link com.rzdata.process.enums.ApplyStatusEnum}
     */
    private String applyStatus;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Integer dayTime;

}
