package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件补发申请业务对象 doc_reissue_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件补发申请业务对象")
public class ReissueApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 补发部门
     */
    @ApiModelProperty(value = "补发部门", required = true)
    private String deptId;

    /**
     * 分发号,多选
     */
    @ApiModelProperty(value = "分发号,多选", required = true)
    @NotBlank(message = "分发号,多选不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributeItemIds;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 申请人名称
     */
    @ApiModelProperty(value = "申请人名称")
    private String nickName;
    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String applyReason;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    @ApiModelProperty("版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ExcelProperty(value = "版本ID")
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private BpmClientInputModelBo bpmClientInputModel;

    /**
     * 流程状态
     */
    private String procStatus;

    /**
     * 变更类型
     */
    private String changeType;
}
