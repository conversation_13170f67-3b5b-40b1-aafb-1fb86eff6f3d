package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 附件视图对象 basic_file
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Data
@ApiModel("附件视图对象")
@ExcelIgnoreUnannotated
public class BasicFileVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String fileName;

    /**
     * 文件大小
     */
	@ExcelProperty(value = "文件大小")
	@ApiModelProperty("文件大小")
	private Long fileSize;

    /**
     * 文件类型
     */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String fileType;

    /**
     * 文件类别
     */
	@ExcelProperty(value = "文件类别")
	@ApiModelProperty("文件类别")
	private String fileClass;

    /**
     * 文件来源业务主键
     */
	@ExcelProperty(value = "文件来源业务主键")
	@ApiModelProperty("文件来源业务主键")
	private String businessId;

	/**
	 * 文件路径
	 */
	@ExcelProperty(value = "文件路径")
	@ApiModelProperty("文件路径")
	private String filePath;

	/**
	 * 文件外部存储ID
	 */
	@ExcelProperty(value = "文件外部存储ID")
	@ApiModelProperty("文件外部存储ID")
	private String externalFileId;

	private String externalFilePath;

	private String externalRev;

	private Integer status;

	private String linkId;

	private String linkCode;

}
