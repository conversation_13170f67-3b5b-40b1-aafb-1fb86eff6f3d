package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 版本-文件关联记录-关联对象 doc_version_link
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Data
@Accessors(chain = true)
@TableName("doc_version_link")
public class DocVersionLink {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 关联记录主键
     */
    private String linkId;
    /**
     * 主文件版本ID
     */
    private String versionId;
    /**
     * 租户id
     */
    private String tenantId;

}
