package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.process.enums.ApplyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件增发申请视图对象 doc_extra_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件增发申请视图对象")
@ExcelIgnoreUnannotated
public class ExtraApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 申请标题
     */
	@ExcelProperty(value = "申请标题")
	@ApiModelProperty("申请标题")
	private String applyTitle;

	/**
	 * 申请类型
	 */
	@ExcelProperty(value = "申请类型")
	@ApiModelProperty("申请类型")
	private ApplyTypeEnum applyType;

	/**
	 * 文件编号
	 */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;
    /**
     * 申请部门
     */
	@ExcelProperty(value = "申请部门")
	@ApiModelProperty("申请部门")
	private String deptId;
	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;
	/**
	 * 文件版本
	 */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本")
	private String versionValue;

	/**
	 * 文件版本id
	 */
	@ExcelProperty(value = "文件版本id")
	@ApiModelProperty("文件版本id")
	private String versionId;

    /**
     * 申请人
     */
	@ExcelProperty(value = "申请人")
	@ApiModelProperty("申请人")
	private String userName;

	/**
	 * 申请人名称
	 */
	@ExcelProperty(value = "申请人名称")
	@ApiModelProperty("申请人名称")
	private String nickName;

    /**
     * 申请时间
     */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

    /**
     * 申请原因
     */
	@ExcelProperty(value = "申请原因")
	@ApiModelProperty("申请原因")
	private String reason;

    /**
     * 申请状态
     */
	@ExcelProperty(value = "申请状态")
	@ApiModelProperty("申请状态")
	private String status;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptName;

}
