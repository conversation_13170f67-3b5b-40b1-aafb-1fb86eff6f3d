package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件变更操作申请分发对象 doc_modify_apply_distribute
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("doc_modify_apply_distribute")
public class ModifyApplyDistribute {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 分发部门编号
     */
    private String deptId;
    /**
     * 分发数量
     */
    private Integer nums;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否分发不打印(0 否 1 是)
     */
    private String notPrint;
}
