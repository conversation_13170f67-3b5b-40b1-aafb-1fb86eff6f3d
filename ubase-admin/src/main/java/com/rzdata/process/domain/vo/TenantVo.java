package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 客户视图对象 basic_Tenant
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@Data
@ApiModel("客户视图对象")
@ExcelIgnoreUnannotated
public class TenantVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ApiModelProperty("主键")
	private String id;

    /**
     * 客户名称
     */
	@ExcelProperty(value = "客户名称")
	@ApiModelProperty("客户名称")
	private String TenantName;

    /**
     * 客户地址
     */
	@ExcelProperty(value = "客户地址")
	@ApiModelProperty("客户地址")
	private String address;

    /**
     * 联系人
     */
	@ExcelProperty(value = "联系人")
	@ApiModelProperty("联系人")
	private String contact;

    /**
     * 联系人电话
     */
	@ExcelProperty(value = "联系人电话")
	@ApiModelProperty("联系人电话")
	private String contactTel;

	@ExcelProperty(value = "租户访问地址")
	@ApiModelProperty("租户访问地址")
	private String url;

}
