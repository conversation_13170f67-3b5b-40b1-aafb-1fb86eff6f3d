package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("复审清单对象")
public class ReviewApplyItemBo extends BaseEntity {

    /**
     * 复审id集合
     */
    private List<String> ids;

    /**
     * 复审文件名
     */
    private String docName;

    /**
     * 文件类型
     */
    private String docClass;
}
