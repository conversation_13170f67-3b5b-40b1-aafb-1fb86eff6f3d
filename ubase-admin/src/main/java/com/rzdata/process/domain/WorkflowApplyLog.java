package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 流程申请记录对象 doc_workflow_apply_log
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("doc_workflow_apply_log")
public class WorkflowApplyLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 流程类别
     */
    private String applyClass;
    /**
     * 流程处理状态 0=开始 1=已提交 2=驳回 3=成功 4=失败
     * 最新：参考applyStatusEnum
     */
    private String applyStatus;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 文件版本号
     */
    private String versionValue;
    /**
     * 发送人 (申请人)
     */
    private String sender;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 流程实例ID
     */
    private String procInstId;
    /**
     * 流程实例key
     */
    private String procDefKey;
    /**
     * 申请时间
     */
    private Date applyTime;

    @ApiModelProperty("当前环节名称")
    private String actDefName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String changeType;


    /**
     * 流程状态
     */
    private String procStatus;

}
