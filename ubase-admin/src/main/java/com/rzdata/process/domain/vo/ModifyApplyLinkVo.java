package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件变更操作申请引用视图对象 doc_modify_apply_link
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更操作申请引用视图对象")
@ExcelIgnoreUnannotated
public class ModifyApplyLinkVo{

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

	/**
	 * basic_file主键
	 */
	private String fileId;
    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String applyId;

    /**
     * 关联编号
     */
	@ExcelProperty(value = "关联编号")
	@ApiModelProperty("关联编号")
	private String linkId;

    /**
     * 关联类别: doc file
     */
	@ExcelProperty(value = "关联类别: doc file")
	@ApiModelProperty("关联类别: doc file")
	private String linkClass;

    /**
     * 关联类型:正文 附件 关联文件 关联记录
     */
	@ExcelProperty(value = "关联类型:正文 附件 关联文件 关联记录")
	@ApiModelProperty("关联类型:正文 附件 关联文件 关联记录")
	private String linkType;

    /**
     * 文档名称
     */
	@ExcelProperty(value = "文档名称")
	@ApiModelProperty("文档名称")
	private String docName;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String fileName;
	/**
	 * 文件名称
	 */
//	@ExcelProperty(value = "文件名称")
//	@ApiModelProperty("文件名称")
//	private String fileName;

	@ApiModelProperty("文件编号")
	private String docId;
	/**
	 * 文件类型
	 */
	@ApiModelProperty("文件类型")
	private String docClass;
	/**
	 * 版本号
	 */
	@ExcelProperty(value = "版本号")
	@ApiModelProperty("版本号")
	private String versionValue;

	/**
	 * 文件版本ID
	 */
	@ExcelProperty(value = "版本ID")
	@ApiModelProperty("版本ID")
	private String versionId;

    /**
     * 是否删除 1 是
     */
	@ExcelProperty(value = "是否删除 1 是")
	@ApiModelProperty("是否删除 1 是")
	private Integer isDeleted;


	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	private Integer status;

	/**
	 * 1：从主文件生成记录
	 * 0：根据自己的规则生成
	 */
	private String readFromMain;

	/**
	 * 文件大小
	 */
	private String fileSize;
}
