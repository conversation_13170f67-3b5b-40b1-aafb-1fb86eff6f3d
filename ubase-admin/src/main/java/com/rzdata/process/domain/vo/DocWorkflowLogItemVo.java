package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 doc_workflow_log_item
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Data
@ApiModel("【请填写功能名称】视图对象")
@ExcelIgnoreUnannotated
public class DocWorkflowLogItemVo {

	private static final long serialVersionUID = 1L;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private Long id;

    /**
     * 接收人
     */
	@ExcelProperty(value = "接收人")
	@ApiModelProperty("接收人")
	private String receiverMan;

    /**
     * 接收人组织
     */
	@ExcelProperty(value = "接收人组织")
	@ApiModelProperty("接收人组织")
	private String receiverOrg;

    /**
     * doc_workflow_log主键
     */
	@ExcelProperty(value = "doc_workflow_log主键")
	@ApiModelProperty("doc_workflow_log主键")
	private String workflowLogId;


}
