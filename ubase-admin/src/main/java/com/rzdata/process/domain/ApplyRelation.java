package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件申请流程关联对象 doc_apply_relation
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_apply_relation")
public class ApplyRelation {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 流程编号
     */
    private String applyId;
    /**
     * 流程类型 修订，作废
     */
    private String applyType;
    /**
     * 流程编号
     */
    private String relationApplyId;
    /**
     * 流程类型 复审
     */
    private String relationApplyType;
    /**
     * 是否删除 1 是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
