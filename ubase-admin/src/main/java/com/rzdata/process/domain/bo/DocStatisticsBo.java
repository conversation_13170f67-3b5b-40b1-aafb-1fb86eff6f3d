package com.rzdata.process.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:17
 * @Version 1.0
 * @Description 文件统计请求实体类
 */
@ApiModel("文档统计参数实体")
@Data
public class DocStatisticsBo{
    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id", example="103")
    private String deptId;

    /**
     *文件类型
     * REC=记录文件
     * SMP=管理规程
     * SOP=操作规程
     * STP=技术标准
     */
    @ApiModelProperty(value = "文件类型", example="REC")
    private String docClass;

    /**
     * 统计时间 用于统计 更新时间在startDateTime到endDateTime之间的文件
     */

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", example="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDateTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", example="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDateTime;
}
