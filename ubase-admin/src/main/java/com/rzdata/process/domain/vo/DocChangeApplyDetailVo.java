package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 文件变更申请详情视图对象
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件变更申请详情视图对象")
@ExcelIgnoreUnannotated
public class DocChangeApplyDetailVo extends DocChangeApplyVo {
    @ExcelProperty(value = "附件")
    @ApiModelProperty("附件")
    private List<ModifyApplyLinkVo> appendixFiles;

    @ApiModelProperty("备注附件")
    private List<ModifyApplyLinkVo> remarkFiles;

    @ApiModelProperty("生效版本附件")
    private List<DocLinkLogVo> sxAppendixFiles;

    @ExcelProperty(value = "当前文件")
    @ApiModelProperty("主文件")
    private DocLinkLogVo file;
}
