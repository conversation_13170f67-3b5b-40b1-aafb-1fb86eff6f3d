package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件分发记录条目视图对象 doc_distribute_item
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Data
@ApiModel("文件分发记录条目视图对象")
@ExcelIgnoreUnannotated
public class DistributeItemVo {

	private static final long serialVersionUID = 1L;


    /**
     * 流程编号
     */
	@ApiModelProperty("流程编号")
	private String applyId;


	@ExcelProperty(value = "分发部门")
	@ApiModelProperty("分发部门名称")
	private String deptName;

	/**
	 * 主键
	 */
	@ExcelProperty(value = "分发号")
	@ApiModelProperty("主键/分发号")
	private String id;

    /**
     * 文件分发记录ID
     */
	@ApiModelProperty("文件分发记录ID")
	private String distributeId;

	/**
	 * 分发时间
	 */
	//@ExcelProperty(value = "分发日期")
	@ApiModelProperty("分发时间")
	private Date distributeTime;

	/**
	 * 签收人
	 */
	@ApiModelProperty("签收人")
	@ExcelProperty(value = "签收人")
	private String receiveUserName;

	//@ExcelProperty(value = "签收人")
	//private String receiveNickName;


	/**
	 * 签收时间
	 */
	@ExcelProperty(value = "签收时间")
	@ApiModelProperty("签收时间")
	private Date receiveTime;

	/**
	 * 打印人
	 */
	//@ApiModelProperty("打印人")
	//private String printUserName;

	//@ExcelProperty(value = "打印人")
	//private String printNickName;


	/**
	 * 签收时间
	 */
	//@ExcelProperty(value = "签收时间")
	//@ApiModelProperty("签收时间")
	//private Date printTime;

	/**
	 * 回收人
	 */
	//@ApiModelProperty("回收人")
	//private String recoveryUserName;

	//@ExcelProperty(value = "回收人")
	//private String recoveryNickName;

	/**
	 * 回收时间
	 */
	//@ExcelProperty(value = "回收时间")
	//@ApiModelProperty("回收时间")
	//private Date recoveryTime;

	/**
	 * 回收情况
	 */
	//@ExcelProperty(value = "回收情况")
	//@ApiModelProperty("回收情况")
	//private String recoveryType;

    /**
     * 文件编号
     */
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 文件名称
	 */
	@ApiModelProperty("文件名称")
	private String docName;

    /**
     * 文件版本ID
     */
	@ApiModelProperty("文件版本ID")
	private String versionId;

    /**
     * 分发部门
     */
	@ApiModelProperty("分发部门")
	private String deptId;


    /**
     * 打印次数
     */
	//@ApiModelProperty("打印次数")
	//private Long printTimes;


    /**
     * 是否删除 1 是
     */
	//@ApiModelProperty("是否删除 1 是")
	//private Long isDeleted;

	/**
	 * 补发记录
	 */
	//@ApiModelProperty("补发记录")
	//private List<ReissueApplyVo> reissueApplyList;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	//private VersionVo versionVo;

}
