package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 附件对象 basic_file
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Data
@Accessors(chain = true)
@TableName("basic_file")
public class BasicFile {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件类别
     */
    private String fileClass;
    /**
     * 文件来源业务主键
     */
    private String businessId;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件外部存储ID
     */
    private String externalFileId;

    private String externalFilePath;

    private String externalRev;

    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 文件摘要
     */
    private String messageDigest;
}
