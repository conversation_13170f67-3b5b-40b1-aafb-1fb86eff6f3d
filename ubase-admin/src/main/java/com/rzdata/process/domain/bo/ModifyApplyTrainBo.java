package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件变更操作申请培训记录业务对象 doc_modify_apply_train
 *
 * <AUTHOR>
 * @date 2022-01-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件变更操作申请培训记录业务对象")
public class ModifyApplyTrainBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号")
    private String applyId;

    /**
     * 文件ID,多条
     */
    @ApiModelProperty(value = "文件ID,多条")
    private String fileIds;

    /**
     * 培训人
     */
    @ApiModelProperty(value = "培训人")
    private String userName;

    /**
     * 培训人部门编号
     */
    @ApiModelProperty(value = "培训人部门编号")
    private String deptId;

    /**
     * 培训时间
     */
    @ApiModelProperty(value = "培训时间")
    private Date trainTime;

    /**
     * 是否删除 1 是
     */
    @ApiModelProperty(value = "是否删除 1 是")
    private Integer isDeleted;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
