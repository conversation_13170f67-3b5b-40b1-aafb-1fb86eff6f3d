package com.rzdata.process.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:16
 * @Version 1.0
 * @Description
 */
@ApiModel("查询消息实体")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocMessageBo {


    /**
     * 消息主键id集合
     */
    /*@ApiModelProperty(value = "消息主键id集合,用于批量更新消息状态为已读",example = "[\"xxxx\",\"xxxxx\"]")
    private List<String> ids;*/

    /**
     * 文档类型
     */
    @ApiModelProperty(value = "文档类型",example = "REC")
    private String docClass;

    /**
     * 文档名称
     */
    @ApiModelProperty(value = "文档名称",example = "xxxx")
    private String docName;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id",example = "103")
    private String deptId;

    /**
     * 消息类型 1=文件复审 2=文件到期 3=文件变更申请 4=文件借阅申请 5=文件补发申请 6=文件增发申请 7=文件复审申请 8=文件代签收 9=文件失效 10=文件生效
     */
    @ApiModelProperty(value = "消息类型 1=文件复审 2=文件到期 3=文件变更申请 4=文件借阅申请 5=文件补发申请 6=文件增发申请 7=文件复审申请 8=文件待签收 9=文件失效 10=文件生效",example = "1")
    private Integer msgClass;

    /**
     * 消息状态
     */
    @ApiModelProperty(value = "消息状态 1=已读 0=未读",example = "1")
    private Integer msgStatus;

    /**
     * 消息接收人
     */
    @ApiModelProperty(value = "消息接收人",example = "userName",required = true)
    private String recoveryUser;

    /**
     * 消息接收人Id
     */
    @ApiModelProperty(value = "消息接收人Id",example = "userNameId")
    private String recoveryUserId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;
}
