package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 文件阅览记录对象 doc_read_log
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Data
@Accessors(chain = true)
@TableName("doc_read_log")
public class ReadLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 文件版本号
     */
    private String versionValue;
    /**
     * 阅读人员
     */
    private String userName;
    /**
     * 所属部门
     */
    private String deptId;
    /**
     * 下载量
     */
    private String downLoad;
    /**
     * 阅读量
     */
    private String preview;
    /**
     * 
     */
    private String externalFileId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    private String docName;

    private String deptName;

    private String nickName;

    private String fileName;
}
