package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName doc_print_log_item_detail
 */
@TableName(value ="doc_print_log_item_detail")
@Data
public class PrintLogItemDetail implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 打印条目主键id doc_print_log_item主键
     */
    private String itemId;

    /**
     * 打印时间
     */
    private Date printTime;

    /**
     * 打印人
     */
    private String userName;

    /**
     * 打印人昵称
     */
    private String userNickName;

    /**
     * 打印来源 重打=2 打印=1
     */
    private Integer printType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
