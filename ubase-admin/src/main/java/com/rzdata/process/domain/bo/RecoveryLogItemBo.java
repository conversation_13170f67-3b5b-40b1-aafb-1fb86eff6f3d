package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 文件回收记录对象 doc_recovery_log_item
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@Accessors(chain = true)
public class RecoveryLogItemBo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String [] ids;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号")
    private String docId;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID")
    private String versionId;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号")
    private String applyId;

    /**
     * 回收部门id(也就是分发部门)
     */
    @ApiModelProperty(value = "回收部门id(也就是分发部门)")
    private String deptId;

    /**
     * 回收部门名称(也就是分发部门)
     */
    @ApiModelProperty(value = "回收部门名称(也就是分发部门)")
    private String deptName;

    /**
     * 回收记录主键 doc_recovery主键
     */
    @ApiModelProperty(value = "回收记录主键 doc_recovery主键")
    private String recoveryId;

    /**
     * 回收人员
     */
    @ApiModelProperty(value = "回收人员")
    private String userName;

    /**
     * 回收人员名称
     */
    @ApiModelProperty(value = "回收人员名称")
    private String nickName;

    /**
     * 回收状态
     */
    @ApiModelProperty(value = "回收状态")
    private String status;

    /**
     * 数据来源 ADD=新增 UPDATE=修订
     */
    @ApiModelProperty(value = "ADD=新增 UPDATE=修订...",required = true)
    private String changeType;

    /**
     * 操作类型（recovery：回收，lost：丢失）
     */
    @ApiModelProperty(value = "操作类型（recovery：回收，lost：丢失）",required = true)
    private String type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private int pageNum ;

    private int pageSize ;

}
