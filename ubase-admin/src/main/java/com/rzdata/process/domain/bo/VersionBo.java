package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 文件版本记录业务对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件版本记录业务对象")
public class VersionBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 标准文件主键
     */

    @ApiModelProperty(value = "标准文件主键", required = true)
    private String standardId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 变更申请流水号
     */
    @ApiModelProperty(value = "变更申请流水号", required = true)
    @NotBlank(message = "变更申请流水号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 文件版本号
     */
    @ApiModelProperty(value = "文件版本号", required = true)
    @NotBlank(message = "文件版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionValue;

    /**
     * 版本生效时间
     */
    @ApiModelProperty(value = "版本生效时间", required = true)
    @NotNull(message = "版本生效时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startDate;

    /**
     * 版本生效截止时间
     */
    @ApiModelProperty(value = "版本生效截止时间", required = true)
    @NotNull(message = "版本生效截止时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endDate;

    /**
     * 版本状态
     */
    @ApiModelProperty(value = "版本状态", required = true)
    @NotBlank(message = "版本状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    private String dbStatus;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    @NotBlank(message = "变更原因不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reason;


    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty("文件名称")
    private String docName;

    /**
     * 编制部门编号
     */
    @ApiModelProperty("编制部门编号")
    private String deptId;

    /**
     * 培训部门
     */
    @ApiModelProperty("培训部门")
    private String trainDept;

    /**
     * 上次复审时间
     */
    @ApiModelProperty(value = "上次复审时间", required = true)
    private Date reviewTime;

    private String forever;
    /**
     * 编制人
     */
    @ApiModelProperty("编制人")
    private String userName;

    private String linkType;

    /**
     * 文档状态
     */
    @ApiModelProperty(value = "文档状态")
    private String docStatus;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String deptIds;

    private String inside;

    private String outside;

    /**
     * 当前用户所属角色
     */
    private String roleKey;
}
