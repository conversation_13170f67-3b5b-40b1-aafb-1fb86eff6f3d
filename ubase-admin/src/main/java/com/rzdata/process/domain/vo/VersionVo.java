package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件版本记录视图对象 doc_version
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("文件版本记录视图对象")
@ExcelIgnoreUnannotated
public class VersionVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

	/**
	 * 标准文件主键
	 */
	@ExcelProperty(value = "标准文件主键")
	@ApiModelProperty("标准文件主键")
	private String standardId;
    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

	/**
	 * 关联文件编号
	 */
	@ApiModelProperty("关联文件编号")
	private String recordDocId;

    /**
     * 变更申请流水号
     */
	@ExcelProperty(value = "变更申请流水号")
	@ApiModelProperty("变更申请流水号")
	private String applyId;

    /**
     * 文件版本号
     */
	@ExcelProperty(value = "文件版本号")
	@ApiModelProperty("文件版本号")
	private String versionValue;

    /**
     * 版本生效时间
     */
	@ExcelProperty(value = "版本生效时间")
	@ApiModelProperty("版本生效时间")
	private Date startDate;

    /**
     * 版本生效截止时间
     */
	@ExcelProperty(value = "版本生效截止时间")
	@ApiModelProperty("版本生效截止时间")
	private Date endDate;

    /**
     * 版本状态
     */
	@ExcelProperty(value = "版本状态")
	@ApiModelProperty("版本状态")
	private String status;

    /**
     * 变更原因
     */
	@ExcelProperty(value = "变更原因")
	@ApiModelProperty("变更原因")
	private String reason;


	/**
	 * 内容概述
	 */
	@ApiModelProperty("内容概述")
	private String content;

	/**
	 * 备注
	 */
	@ApiModelProperty("备注")
	private String remark;

	/**
	 * 变更要素，多个以,隔开
	 */
	@ApiModelProperty("变更要素，多个以,隔开")
	private String changeFactor;

	/**
	 * 变更原因
	 */
	@ApiModelProperty("变更原因")
	private String changeReason;
	/**
	 * 文件类型
	 */
	@ExcelProperty(value = "文件类型")
	@ApiModelProperty("文件类型")
	private String docClass;

	/**
	 * 文件名称
	 */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String docName;

	/**
	 * 编制部门编号
	 */
	@ExcelProperty(value = "编制部门编号")
	@ApiModelProperty("编制部门编号")
	private String deptId;

	/**
	 * 编制人
	 */
	@ExcelProperty(value = "编制人")
	@ApiModelProperty("编制人")
	private String userName;

	/**
	 * 申请时间
	 */
	@ExcelProperty(value = "申请时间")
	@ApiModelProperty("申请时间")
	private Date applyTime;

	/**
	 * 编制文件编号
	 */
	@ApiModelProperty("编制文件编号")
	private String fileId;

	@ExcelProperty(value = "合稿文件编号")
	@ApiModelProperty("合稿文件编号")
	private String mergeFileId;

	/**
	 * 加密文件编号
	 */
	@ExcelProperty(value = "加密文件编号")
	@ApiModelProperty("加密文件编号")
	private String encryptFileId;

	@ApiModelProperty("部门名称")
	private String deptName;

	private String compileDeptName;

	/**
	 * 上次复审时间
	 */
	@ExcelProperty(value = "上次复审时间")
	@ApiModelProperty("上次复审时间")
	private Date reviewTime;

	private String forever;
	/**
	 * 培训部门
	 */
	@ExcelProperty(value = "培训部门")
	@ApiModelProperty("培训部门")
	private String trainDept;

	private String className;

	private String changeContent;

	private String receiveStatus;
	/**
	 * 培训部门
	 */
	@ExcelProperty(value = "流程实例id")
	@ApiModelProperty("流程实例id")
	private String procInstId;

	private Boolean hasPerms;

	private String versionId;

	private Boolean inFavorites;
}
