package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件回收记录视图对象 doc_recovery_log
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
@ApiModel("文件回收记录视图对象")
@ExcelIgnoreUnannotated
public class RecoveryLogVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 文件版本ID
     */
	@ExcelProperty(value = "文件版本ID")
	@ApiModelProperty("文件版本ID")
	private String versionId;

	/**
	 * 文件版本
	 */
	@ExcelProperty(value = "文件版本")
	@ApiModelProperty("文件版本")
	private String versionValue;

    /**
     * 流程编号
     */
	@ExcelProperty(value = "流程编号")
	@ApiModelProperty("流程编号")
	private String applyId;

    /**
     * 回收人员所在部门id
     */
	@ExcelProperty(value = "回收人员部门id")
	@ApiModelProperty("回收人员所在部门id")
	private String deptId;

    /**
     * 部门名称
     */
	@ExcelProperty(value = "部门名称")
	@ApiModelProperty("回收人员所在部门名称")
	private String deptName;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 待回收数
	 */
	@ApiModelProperty(value = "待回收数")
	private Integer noRecyclNum;

	/**
	 * 已回收数
	 */
	@ApiModelProperty(value = "已回收数")
	private Integer recyclNum;


	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "有效期开始时间")
	private Date startDate;

	@ApiModelProperty(value = "有效期结束时间")
	private Date endDate;

	/**
	 * 文件类型
	 */
	@ApiModelProperty(value = "文件类型")
	private String docClass;

	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private String docName;

	/**
	 * 编制部门id
	 */
	@ApiModelProperty(value = "编制部门id")
	private String compileDeptId;

	/**
	 * 编制部门名称
	 */
	@ApiModelProperty(value = "编制部门名称")
	private String compileDeptName;

	private String classCame;

	private VersionVo versionVo;
}
