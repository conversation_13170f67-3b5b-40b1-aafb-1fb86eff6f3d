package com.rzdata.process.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class PrintLogItemDetailBo implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private String id;

    /**
     * 打印条目主键id doc_print_log_item主键
     */
    @ApiModelProperty("打印条目主键id doc_print_log_item主键")
    private String itemId;

    /**
     * 打印时间
     */
    @ApiModelProperty("打印时间")
    private Date printTime;

    /**
     * 打印人
     */
    @ApiModelProperty("打印人")
    private String userName;

    /**
     * 打印人昵称
     */
    @ApiModelProperty("打印人昵称")
    private String userNickName;

    /**
     * 打印来源 重打=2 打印=1
     */
    @ApiModelProperty("打印来源 重打=2 打印=1")
    private Integer printType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
