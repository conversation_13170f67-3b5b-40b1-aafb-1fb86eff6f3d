package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 文件变更操作申请业务对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("文件变更操作申请业务对象")
public class ModifyApplyBo extends BaseEntity {

    /**
     * 主键，根据编码规则生成
     */
    @ApiModelProperty(value = "主键，根据编码规则生成", required = true)
    private String id;

    /**
     * 变更类型
     */
    @ApiModelProperty(value = "变更类型", required = true)
    private String changeType;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    private String docId;

    /**
     * 编制部门编号
     */
    @ApiModelProperty(value = "编制部门编号", required = true)
    private String deptId;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String userName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    private Date applyTime;

    /**
     * 文件有效期
     */
    @ApiModelProperty(value = "文件有效期", required = true)
    private Long expiration;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionValue;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 编制正文
     */
    @ApiModelProperty(value = "编制正文")
    private ModifyApplyLinkBo standardDoc;

    /**
     * 备注附件
     */
    @ApiModelProperty(value = "备注附件")
    private List<ModifyApplyLinkBo> remarkDoc;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件文件")
    private List<ModifyApplyLinkBo> appendixes;

    /**
     * 是否培训
     */
    @ApiModelProperty(value = "是否培训", required = true)
    private String yNTrain;

    /**
     * 培训部门
     */
    @ApiModelProperty(value = "培训部门", required = true)
    private String trainDept;

    /**
     * 分发部门
     */
    @ApiModelProperty(value = "分发部门", required = true)
    private List<ModifyApplyDistributeBo> distributeDepths;

    /**
     * 关联文件
     */
    @ApiModelProperty(value = "关联文件")
    private List<ModifyApplyLinkBo> docLinks;

    /**
     * 关联或新增记录
     */
    @ApiModelProperty(value = "关联或新增记录")
    private List<ModifyApplyLinkBo> recordLinks;

    /**
     * 培训记录
     */
    @ApiModelProperty(value = "培训记录")
    private List<ModifyApplyTrainBo> trains;

    /**
     * 是否已合稿
     */
    @ApiModelProperty(value = "是否已合稿", required = true)
    private String yNMergeDraft;

    /**
     * 是否已签章
     */
    @ApiModelProperty(value = "是否已签章", required = true)
    private String yNEncrypt;

    /**
     * 是否已分发
     */
    @ApiModelProperty(value = "是否已分发", required = true)
    private String yNDistribute;

    /**
     * 编制文件编号
     */
    @ApiModelProperty(value = "编制文件编号", required = true)
    private String fileId;

    /**
     * 记录状态
     */
    @ApiModelProperty(value = "记录状态", required = true)
    private String recordStatus;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 内容概述
     */
    @ApiModelProperty(value = "内容概述", required = true)
    private String content;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    private String changeId;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    /**
     * 流程状态
     */
    @ApiModelProperty("流程状态")
    private String processStatus;

    private BpmClientInputModelBo bpmClientInputModel;

    /**
     * 设置生效时间
     */
    private Date setupTime;
}
