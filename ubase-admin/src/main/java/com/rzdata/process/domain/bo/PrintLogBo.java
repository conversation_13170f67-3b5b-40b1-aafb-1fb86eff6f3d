package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 文件打印记录业务对象 doc_print_log
 *
 * <AUTHOR>
 * @date 2022-01-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件打印记录业务对象")
public class PrintLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件分发编号
     */
    @ApiModelProperty(value = "文件分发编号", required = true)
    @NotBlank(message = "文件分发编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributeItemId;

    /**
     * 主键
     */
    @ApiModelProperty(value = "文档名称", required = false)
    //@NotBlank(message = "文档名称", groups = { EditGroup.class })
    private String docName;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 打印人员
     */
//    @ApiModelProperty(value = "打印人员", required = true)
//    @NotBlank(message = "打印人员不能为空", groups = { AddGroup.class, EditGroup.class })
//    private String printUserName;

    /**
     * 打印时间
     */
//    @ApiModelProperty(value = "打印时间", required = true)
//    @NotNull(message = "打印时间不能为空", groups = { AddGroup.class, EditGroup.class })
//    private Date printTime;

    @ApiModelProperty("打印状态 0：未，1：已")
    private String status;

    @ApiModelProperty("文件类型")
    private String docClass;

    @ApiModelProperty("分发部门id")
    private String deptId;

    @ApiModelProperty("编制部门id")
    private Long compileDeptId;

    @ApiModelProperty("生效开始日期")
    private String startTime;

    @ApiModelProperty("生效结束日期")
    private String endTime;

    @ApiModelProperty("分发开始日期")
    private String disStartTime;

    @ApiModelProperty("分发结束日期")
    private String disEndTime;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
