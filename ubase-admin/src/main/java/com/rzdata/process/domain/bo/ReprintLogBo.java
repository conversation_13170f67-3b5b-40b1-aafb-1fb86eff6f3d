package com.rzdata.process.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:55
 * @Version 1.0
 * @Description
 */
@Data
public class ReprintLogBo implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 重打人
     */
    @ApiModelProperty("重打人")
    private String reprintUserName;

    /**
     * 打印记录主键id
     */
    @ApiModelProperty("分发表主键id")
    private String disItemId;

    /**
     * 重打时间
     */
    private Date reprintTime;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;
}
