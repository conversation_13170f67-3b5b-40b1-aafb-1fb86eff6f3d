package com.rzdata.process.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 文件变更操作申请培训记录对象 doc_modify_signature_log
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Data
public class ModifySignatureVo {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private String id;

    /**
     * 流程编号
     */
    @ApiModelProperty("流程编号")
    private String applyId;

    /**
     * 签章地址
     */
    @ApiModelProperty("签章地址")
    private String signatureUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
}
