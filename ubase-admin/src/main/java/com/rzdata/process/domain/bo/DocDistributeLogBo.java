package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文件分发记录业务对象 doc_distribute_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分发记录业务对象")
public class DocDistributeLogBo extends BaseEntity {

    /**
     * 文件分发编号，根据分发编码规则生成
     */
    @ApiModelProperty(value = "文件分发编号，根据分发编码规则生成")
    private String id;

    /**
     * 变更申请流水号
     */
    @ApiModelProperty(value = "变更申请流水号")
    private String applyId;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号")
    private String docId;

    /**
     * doc_distribute_log的分发部门
     */
    private String distributeDeptId;

    /**
     * 分发部门
     */
    @ApiModelProperty(value = "分发部门")
    private String deptId;

    /**
     * 分发部门
     */
    @ApiModelProperty(value = "编制部门")
    private String compileDeptId;

    /**
     * 分发数量
     */
    @ApiModelProperty(value = "分发数量")
    private Long nums;

    /**
     * 签收人
     */
    @ApiModelProperty(value = "签收人")
    private String receiveUserName;

    /**
     * 分发时间
     */
    @ApiModelProperty(value = "分发时间")
    private Date distributeTime;

    /**
     * 签收状态 0=未签收 1=已签收
     */
    @ApiModelProperty(value = "签收状态")
    private String receiveStatus;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    private Date receiveTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionValue;

    @ApiModelProperty("类型")
    private String linkType;

    @ApiModelProperty("类型")
    private String docClass;

    @ApiModelProperty("生效日期")
    private Date validDate;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    @ApiModelProperty("查询类型 1： 本部门，2：外部部门")
    private String searchType;

    private String printStatus;

    /**
     * 逻辑删除 1=删除 0=未删除
     */
    private String dbStatus;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("文件名称")
    private String docName;

    @ApiModelProperty("生效开始日期")
    private String startTime;

    @ApiModelProperty("生效结束日期")
    private String endTime;

    @ApiModelProperty("分发开始日期")
    private String disStartTime;

    @ApiModelProperty("分发结束日期")
    private String disEndTime;

    private String source;

    @ApiModelProperty("是否分发不打印(0 否 1 是)")
    private String notPrint;

}
