package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.rzdata.process.enums.ApplyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件增发申请对象 doc_extra_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Accessors(chain = true)
@TableName("doc_extra_apply")
public class ExtraApply  {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 申请类型 新增申请默认就是EXTRA
     */
    private ApplyTypeEnum applyType;
    /**
     * 申请部门
     */
    private String deptId;
    /**
     * 申请人
     */
    private String userName;
    /**
     * 申请人名称
     */
    private String nickName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 申请状态
     */
    private String status;

    /**
     * 申请结果 {@link com.rzdata.process.enums.ApplyStatusEnum}
     */
    private String applyStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
