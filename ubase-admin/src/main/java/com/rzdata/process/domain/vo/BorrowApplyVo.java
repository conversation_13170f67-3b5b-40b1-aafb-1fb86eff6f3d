package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 文件借阅申请视图对象 doc_borrow_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@ApiModel("文件借阅申请视图对象")
@ExcelIgnoreUnannotated
public class BorrowApplyVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 申请标题
     */
	@ExcelProperty(value = "申请标题")
	@ApiModelProperty("申请标题")
	private String applyTitle;

    /**
     * 文件编号
     */
	@ExcelProperty(value = "文件编号")
	@ApiModelProperty("文件编号")
	private String docId;

    /**
     * 补发部门
     */
	@ExcelProperty(value = "补发部门")
	@ApiModelProperty("补发部门")
	private String deptId;

    /**
     * 借阅开始时间
     */
	@ExcelProperty(value = "借阅开始时间")
	@ApiModelProperty("借阅开始时间")
	private Date startTime;

    /**
     * 借阅结束时间
     */
	@ExcelProperty(value = "借阅结束时间")
	@ApiModelProperty("借阅结束时间")
	private Date endTime;

    /**
     * 申请人
     */
	@ExcelProperty(value = "申请人")
	@ApiModelProperty("申请人")
	private String userName;

    /**
     * 申请原因
     */
	@ExcelProperty(value = "申请原因")
	@ApiModelProperty("申请原因")
	private String applyReason;

    /**
     * 申请状态
     */
	@ExcelProperty(value = "申请状态")
	@ApiModelProperty("申请状态")
	private String status;

	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty("借阅人")
	private String borrowUser;

	@ApiModelProperty("借阅部门")
	private String borrowDeptName;

	@ApiModelProperty("借阅时长 分钟")
	private Long borrowTime;

	@ApiModelProperty("文件名称")
	private String docName;

	@ApiModelProperty("文件类型")
	private String docClass;

	@ApiModelProperty("文件版本ID")
	private String versionId;

	@ApiModelProperty("文件版本")
	private String versionValue;

	@ApiModelProperty("编制部门")
	private String makeDeptName;

	@ApiModelProperty("类型名称")
	private String typeName;

	private String borrowId;

	private List<FileVo> fileVoList;

	private Integer dayTime;

	/**
	 * 借阅文件主键id
	 */
	private String fileId;

	/**
	 * 合稿文件id
	 */
	private String mergeFileId;

	/**
	 * 签章文件id
	 */
	private String encryptFileId;

}
