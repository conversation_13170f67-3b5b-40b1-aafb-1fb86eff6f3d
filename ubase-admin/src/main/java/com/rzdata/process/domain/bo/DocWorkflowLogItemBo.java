package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 【请填写功能名称】业务对象 doc_workflow_log_item
 *
 * <AUTHOR>
 * @date 2022-03-21
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("【请填写功能名称】业务对象")
public class DocWorkflowLogItemBo extends BaseEntity {

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", required = true)
    @NotBlank(message = "接收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiverMan;

    /**
     * 接收人组织
     */
    @ApiModelProperty(value = "接收人组织", required = true)
    @NotBlank(message = "接收人组织不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiverOrg;

    /**
     * doc_workflow_log主键
     */
    @ApiModelProperty(value = "doc_workflow_log主键", required = true)
    @NotBlank(message = "doc_workflow_log主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workflowLogId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
