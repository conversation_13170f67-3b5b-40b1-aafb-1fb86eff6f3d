package com.rzdata.process.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件复审申请业务对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件复审申请业务对象")
public class ReviewApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    private String applyTitle;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
//    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 复审标准，文件延长有效期，文件保持现状，文件修订，文件作废
     */
    @ApiModelProperty(value = "复审标准，文件延长有效期，文件保持现状，文件修订，文件作废", required = true)
    private String reviewAction;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门", required = true)
    private String deptId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String editUserName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    private Date applyTime;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String reason;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;

    /**
     * 版本号
     */
//    @ExcelProperty(value = "版本号")
//    @ApiModelProperty("版本号")
//    private String versionValue;

    /**
     * 文件版本ID
     */
//    @ExcelProperty(value = "版本ID")
//    @ApiModelProperty("版本ID")
//    private String versionId;

    @ApiModelProperty("流程定义key")
//    @NotBlank(message = "流程定义key不能为空", groups = { AddGroup.class })
    private String procDefKey;

    @ApiModelProperty("流程状态")
    private Integer procStauts;

    @ApiModelProperty("申请开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyStartTime;

    @ApiModelProperty("申请结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyEndTime;

    @ApiModelProperty(value = "文件编号集合", required = true)
    private List<String> docIds;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private String procStatus;

    private String changeType;
}
