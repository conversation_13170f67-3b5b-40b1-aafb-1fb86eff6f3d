package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 标准文件对象 doc_standard
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_standard")
public class Standard  {

    private static final long serialVersionUID=1L;

    /**
     * 文件编号，根据编号规则自动生成
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件状态
     */
    private String status;
    /**
     * 文件有效期
     */
    private Long expiration;
    /**
     * 当前文件版本
     */
    private String currentVersion;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 编制人
     */
    private String userName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 编制文件basic_file表主键
     */
    private String fileId;
    /**
     * 加密文件编号
     */
    private String encryptFileId;
    /**
     * 合稿文件编号
     */
    private String mergeFileId;
    /**
     * 内容概述
     */
    private String content;
    /**
     * 备注
     */
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    @ApiModelProperty(value = "变更要素，多个以,隔开", required = true)
    private String changeFactor;

    /**
     * 变更原因
     */
    @ApiModelProperty(value = "变更原因", required = true)
    private String changeReason;

    /**
     * 变更类型 ADD=新增 UPDATE=修订 DISUSE = 作废
     */
    private String changeType;

    /**
     * 1=已在流程中 0=未在流程中
     */
    private Integer lockStatus;

    /**
     * 是否初始化文件
     * 否 = 0
     * 是 = 1
     */
    private String initFile;

}
