package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 文件变更操作申请对象 doc_modify_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Getter
@TableName("doc_modify_apply")
public class ModifyApply  {

    private static final long serialVersionUID=1L;

    /**
     * 主键，根据编码规则生成
     */
    @TableId(value = "id")
    private String id;
    /**
     * 变更类型
     */
    private String changeType;
    /**
     * 文件类型
     */
    private String docClass;
    /**
     * 文件名称
     */
    private String docName;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 编制部门编号
     */
    private String deptId;
    /**
     * 编制人
     */
    private String userName;

    /**
     * 编制人
     */
    @TableField(exist = false)
    private String nickName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 文件有效期
     */
    private Integer expiration;
    /**
     * 是否培训
     */
    private String yNTrain;
    /**
     * 培训部门
     */
    private String trainDept;
    /**
     * 是否已合稿
     */
    private String yNMergeDraft;
    /**
     * 是否已签章
     */
    private String yNEncrypt;
    /**
     * 是否已分发
     */
    private String yNDistribute;
    /**
     * 编制文件编号
     */
    private String fileId;
    /**
     * 记录状态
     */
    private String recordStatus;
    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 内容概述
     */
    private String content;
    /**
     * 备注
     */
    private String remark;

    /**
     * 变更要素，多个以,隔开
     */
    private String changeFactor;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String changeId;


    /**
     * 签章链接
     */
    private String signatureUrl;

    /**
     * 是否已签章 Y=已 N=未 默认N
     */
    private String isSignature;

    /**
     * 设置生效时间
     */
    private Date setupTime;
}
