package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件复审申请对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@Accessors(chain = true)
@TableName("doc_review_apply")
public class ReviewApply {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 申请标题
     */
    private String applyTitle;
    /**
     * 文件编号
     */
    private String docId;
    /**
     * 复审标准，EXTENSION=文件延长有效期，KEEP=文件保持现状，UPDATE=文件修订，DISUSE=文件作废
     */
    private String reviewAction;
    /**
     * 申请部门
     */
    private String deptId;
    /**
     * 编制人：是后续流程文件变更的申请人,未填写时在领导审批阶段选择
     */
    private String editUserName;
    /**
     * 申请人
     */
    private String userName;
    /**
     * 申请时间
     */
    private Date applyTime;
    /**
     * 申请原因
     */
    private String reason;
    /**
     * 申请状态
     */
    private String status;
    /**
     * 版本号
     */
    private String versionValue;
    /**
     * 文件版本ID
     */
    private String versionId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
