package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 版本-文件关联记录-关联视图对象 doc_version_link
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Data
@ApiModel("版本-文件关联记录-关联视图对象")
@ExcelIgnoreUnannotated
public class DocVersionLinkVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 关联记录主键
     */
	@ExcelProperty(value = "关联记录主键")
	@ApiModelProperty("关联记录主键")
	private String linkId;

    /**
     * 主文件版本ID
     */
	@ExcelProperty(value = "主文件版本ID")
	@ApiModelProperty("主文件版本ID")
	private String versionId;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;


}
