package com.rzdata.process.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/1/19 17:12
 * @Version 1.0
 * @Description 文件合并-页眉模板实体类
 */
@Data
public class DocMergeHeadAndFootTemplateBo {

    /**
     * 模板Id
     */
    @ApiModelProperty(value = "模板Id")
    private String templateDocId;

    /**
     * 模板值 json字符串
     */
    @ApiModelProperty(value = "模板值 json字符串")
    private String value;

    /**
     * 页眉图片地址
     */
    @ApiModelProperty(value = "页眉图片地址 如果模板使用的话")
    private String picPath;
}
