package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/2/9 10:44
 * @Version 1.0
 * @Description
 */
@Data
@ApiModel("文件统计-变更类型视图对象")
@ExcelIgnoreUnannotated
public class DocStatisticsDistributeVo {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门id", example="103")
    private String deptId;


    /**
     * 文档id
     */
    @ApiModelProperty(value = "文档id", example="SEP-103")
    private String docId;

    /**
     * 部门分发时间总数
     */
    @ApiModelProperty(value = "部门分发时间总数", example="50")
    private int distributeNum;

    /**
     * 业务id
     */
    private String applyId;

}
