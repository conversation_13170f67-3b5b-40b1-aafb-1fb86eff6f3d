package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.process.domain.ExtraApplyDocNumber;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 文件增发申请业务对象 doc_extra_apply
 *
 * <AUTHOR>
 * @date 2021-12-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件增发申请业务对象")
public class ExtraApplyBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 申请标题
     */
    @ApiModelProperty(value = "申请标题", required = true)
    @NotBlank(message = "申请标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyTitle;

    /**
     * 申请部门
     */
    @ApiModelProperty(value = "申请部门", required = true)
    private String deptId;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人", required = true)
    private String userName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", required = true)
    private Date applyTime;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因", required = true)
    private String reason;

    /**
     * 申请状态
     */
    @ApiModelProperty(value = "申请状态", required = true)
    private String status;

    @ApiModelProperty("申请的文件数量集合")
    private List<ExtraApplyDocNumber> applyDocNumberList;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    private BpmClientInputModelBo bpmClientInputModel;

    /**
     * 变更类型
     */
    private String changeType;
}
