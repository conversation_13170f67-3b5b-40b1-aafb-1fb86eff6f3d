package com.rzdata.process.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/3 15:22
 * @Version 1.0
 * @Description
 */
@Data
public class DocPreviewStatisticsBo {
    /**
     * 主键id
     */
    private String id;

    /**
     *文档id
     */
    private String docId;

    /**
     * 文档名称
     */
    private String docName;

    /**
     * 版本id
     */
    private String versionId;

    /**
     * 版本号
     */
    private String versionValue;

    /**
     * 编制人
     */
    private String userName;

    /**
     * 预览人
     */
    private String previewUserName;

    /**
     * 预览人名称
     */
    private String previewNickName;

    /**
     * 文档编制人名称
     */
    private String nickName;

    /**
     * 文档编制部门
     */
    private String deptId;

    /**
     * 文档编制部门名称
     */
    private String deptName;

    /**
     * 文件basic_file表(可能是编制文件/合稿文件/签章文件/上传文件后返回的爱数文件id)的主键id
     */
    private String fileId;

    /**
     * 来源(哪个地方调用了预览:1:本部门文件,2:外部门文件.3:公司文件,4:借阅)
     */
    @ApiModelProperty("来源 1:本部门文件,2:外部门文件.3:公司文件,4:借阅")
    private Integer source;

    /**
     * 预览组件类型 onlyoffice和爱数
     */
    @ApiModelProperty("预览类型 AS=爱数 ONLY_OFFICE=onlyOffice")
    private Integer type;

    /**
     * 预览时间
     */
    private Date previewTime;

}
