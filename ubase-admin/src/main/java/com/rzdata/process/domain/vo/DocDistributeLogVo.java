package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.rzdata.process.domain.DistributeItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 文件分发记录视图对象 doc_distribute_log
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Data
@ApiModel("文件分发记录视图对象")
@ExcelIgnoreUnannotated
public class DocDistributeLogVo {

    private static final long serialVersionUID = 1L;

    /**
     * 文件分发编号，根据分发编码规则生成
     */
    @ApiModelProperty("文件分发编号，根据分发编码规则生成")
    private String id;

    /**
     * 文件名称
     */
    @ExcelProperty(value = "文件名称")
    @ApiModelProperty("文件名称")
    private String docName;

    /**
     * 文件编号
     */
    @ExcelProperty(value = "文件编号")
    @ApiModelProperty("文件编号")
    private String docId;

    /**
     * 关联文件编号
     */
    @ApiModelProperty("关联文件编号")
    private String recordDocId;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    @ApiModelProperty("文件类型")
    private String docClassName;


    /**
     * 版本号
     */
    @ExcelProperty(value = "文件版本")
    @ApiModelProperty("版本号")
    private String versionValue;

    /**
     * 分发部门
     */
    @ApiModelProperty("分发部门")
    private String deptId;

    @ExcelProperty(value = "分发部门名称")
    @ApiModelProperty("分发部门名称")
    private String deptName;
    /**
     * 文档编制部门
     */
    @ExcelProperty(value = "文档编制部门")
    @ApiModelProperty("文档编制部门")
    private Long compileDeptId;

    /**
     * 文档编制部门名称
     */
    @ExcelProperty(value = "文档编制部门名称")
    @ApiModelProperty("文档编制部门名称")
    private String compileDeptName;
    /**
     * 版本生效时间
     */
    @ExcelProperty(value = "生效日期")
    @ApiModelProperty("版本生效时间")
    private Date startDate;

    /**
     * 版本生效截止时间
     */
    @ExcelProperty(value = "失效日期")
    @ApiModelProperty("版本生效截止时间")
    private Date endDate;

    /**
     * 变更申请流水号
     */
    @ApiModelProperty("变更申请流水号")
    private String applyId;


    /**
     * 分发数量
     */
    @ApiModelProperty("分发数量")
    private Long nums;

    /**
     * 签收人
     */
    @ApiModelProperty("签收人")
    private String receiveUserName;

    private String distributeUserName;

    /**
     * 分发人
     */
    private String distributeUserNickName;
    /**
     * 分发时间
     */
    @ApiModelProperty("分发时间")
    private Date distributeTime;

    /**
     * 签收状态
     */
    @ApiModelProperty("签收状态")
    private String receiveStatus;

    /**
     * 签收时间
     */
    @ApiModelProperty("签收时间")
    private Date receiveTime;

    /**
     * 文件版本ID
     */
    @ApiModelProperty("版本ID")
    private String versionId;

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String docClass;

    /**
     * 文件有效期
     */
    @ApiModelProperty("文件有效期")
    private Long expiration;

    /**
     * 编制人
     */
    @ApiModelProperty("编制人")
    private String userName;

    /**
     * 申请时间
     */
    @ApiModelProperty("申请时间")
    private Date applyTime;

    /**
     * 编制文件编号
     */
    @ApiModelProperty("编制文件编号")
    private String fileId;

    /**
     * 版本状态
     */
    @ApiModelProperty("版本状态")
    private String versionStatus;


    private VersionVo versionVo;

    private Integer printStatus;

    private Integer dbStatus;

    private String changeType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    private DistributeItem distributeItem;

    private String nickName;

    private String processStatus;

    private String receiveNickName;

    /**
     * 上一版本 用于修订
     */
    @ApiModelProperty(value = "上一版本")
    private String previousVersionValue;

    @ApiModelProperty(value = "合稿文件id")
    private String mergeFileId;

    @ApiModelProperty(value = "签章文件id")
    private String encryptFileId;

    private String isSign;

    private Date reviewTime;

    /**
     * 分发总数 该文档这个版本这个部门所有分发数(增发+分发)
     */
    private Integer totalNums;

    @ApiModelProperty(value = "流程id")
    private String procInstId;

    //@ApiModelProperty(value = "分发部门")
    //private String distributeDeptName;

//    private String receiveUserNames;

    /**
     * 文件有效期是否永久
     */
    private String forever;

    @ApiModelProperty("是否分发不打印(0 否 1 是)")
    private String notPrint;

    private Boolean inFavorites;
}
