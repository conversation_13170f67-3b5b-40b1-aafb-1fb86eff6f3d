package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:54
 * @Version 1.0
 * @Description
 */
@Data
@TableName("doc_reprint_log")
public class ReprintLog {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 重打人
     */
    private String reprintUserName;

    private String reprintNickName;

    /**
     * 重打次数
     */
    private int reprintNum;

    /**
     * 重打时间
     */
    private Date reprintTime;

    /**
     * 打印记录表主键id
     */
    private String disItemId;
}
