package com.rzdata.process.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件打印记录对象 doc_print_log
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Data
@Accessors(chain = true)
@TableName("doc_print_log")
public class PrintLog {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件编号
     */
    private String docId;

    /**
     * 文件编号
     */
    private String applyId;

    /**
     * 文件类型
     */
    private String docClass;

    /**
     * 文件名称
     */
    private String docName;

    /**
     * 文件版本
     */
    private String versionId;

    /**
     * 文件版本
     */
    private String versionValue;

    /**
     * 分发部门编号
     */
    private String deptId;

    /**
     * 分发部门名称
     */
    private String deptName;

    /**
     * 文档编制部门
     */
    private Long compileDeptId;

    /**
     * 文档编制部门
     */
    private String compileDeptName;

    /**
     * 打印数量（第一次分发数+增发数量+补发数量）
     */
    private int printNums;

    /**
     * 已打印数量
     */
    private int printedNums;

    /**
     * 分发份数=第一次分发数+增发数量
     */
    private int distributeNums;

    /**
     * 分发时间
     */
    private Date distributeTime;

}
