package com.rzdata.process.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.rzdata.process.domain.WorkFlowInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 文件复审申请视图对象 doc_review_apply
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Data
@ApiModel("复审文件清单视图对象")
@ExcelIgnoreUnannotated
public class ReviewApplyItemVo {

	private static final long serialVersionUID=1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private String id;
	/**
	 * 文件复审申请表主键
	 */
	private String applyId;

	/**
	 * 文件名称
	 */
	private String docName;
	/**
	 * 文件编号
	 */
	private String docId;

	/**
	 * 文件类型
	 */
	private String docClass;

	/**
	 * 版本号
	 */
	private String versionValue;
	/**
	 * 文件版本ID
	 */
	private String versionId;

	/**
	 * 文件id
	 */
	private String fileId;

}
