package com.rzdata.process.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.Date;


/**
 * 文件回收记录业务对象 doc_recovery_log
 *
 * <AUTHOR>
 * @date 2022-01-17
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件回收记录业务对象")
public class RecoveryLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 文件编号
     */
    @ApiModelProperty(value = "文件编号", required = true)
    @NotBlank(message = "文件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    @NotBlank(message = "文件名称", groups = { AddGroup.class, EditGroup.class })
    private String docName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    @NotBlank(message = "文件类型", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 回收状态
     */
    @ApiModelProperty(value = "回收状态", required = true)
    @NotBlank(message = "回收状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 回收人员
     */
    @ApiModelProperty(value = "回收人员", required = true)
    @NotBlank(message = "回收人员不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userName;

    /**
     * 文件版本ID
     */
    @ApiModelProperty(value = "文件版本ID", required = true)
    @NotBlank(message = "文件版本ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String versionId;

    /**
     * 流程编号
     */
    @ApiModelProperty(value = "流程编号", required = true)
    @NotBlank(message = "流程编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称", required = true)
    @NotBlank(message = "部门名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptName;

    /**
     * 编制人
     */
    @ApiModelProperty(value = "编制人", required = true)
    private String compileUserName;

    /**
     * 编制部门
     */
    @ApiModelProperty(value = "编制部门", required = true)
    private Long compileDeptId;

    private Integer noRecyclNum;

    private Integer recyclNum;

    @ApiModelProperty(value = "分发开始时间", required = true)
    private Date disStartTime;

    @ApiModelProperty(value = "分发结束时间", required = true)
    private Date disEndTime;

    @ApiModelProperty(value = "生效开始时间", required = true)
    private Date startTime;

    @ApiModelProperty(value = "生效结束时间", required = true)
    private Date endTime;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
