package com.rzdata.process.service;

import com.rzdata.process.domain.ModifyApplyDistribute;
import com.rzdata.process.domain.vo.ModifyApplyDistributeVo;
import com.rzdata.process.domain.bo.ModifyApplyDistributeBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件变更操作申请分发Service接口
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
public interface IModifyApplyDistributeService extends IServicePlus<ModifyApplyDistribute, ModifyApplyDistributeVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ModifyApplyDistributeVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ModifyApplyDistributeVo> queryPageList(ModifyApplyDistributeBo bo);

	/**
	 * 查询列表
	 */
	List<ModifyApplyDistributeVo> queryList(ModifyApplyDistributeBo bo);

	/**
	 * 根据新增业务对象插入文件变更操作申请分发
	 * @param bo 文件变更操作申请分发新增业务对象
	 * @return
	 */
	Boolean insertByBo(ModifyApplyDistributeBo bo);

	/**
	 * 根据编辑业务对象修改文件变更操作申请分发
	 * @param bo 文件变更操作申请分发编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ModifyApplyDistributeBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	Integer queryNumsByMap(Map<String, Object> params);

    List<ModifyApplyDistributeVo> queryListByChangeIdApplyId(ModifyApplyDistributeBo distributeBo);
}
