package com.rzdata.process.service.impl;

import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.process.domain.ModifySignature;
import com.rzdata.process.domain.vo.ModifySignatureVo;
import com.rzdata.process.mapper.ModifySignatureMapper;
import com.rzdata.process.service.IModifySignatureService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/4/12 18:35
 * @Version 1.0
 * @Description
 */
@Service
public class ModifySignatureServiceImpl  extends ServicePlusImpl<ModifySignatureMapper, ModifySignature, ModifySignatureVo> implements IModifySignatureService {
}
