package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.collect.Lists;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.eventbus.StartModifyApplyEvent;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocChangeApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.DocChangeApplyBo;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.DocChangeApplyDetailVo;
import com.rzdata.process.domain.vo.DocChangeApplyVo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.DocChangeApplyMapper;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;
import static com.rzdata.process.enums.ApplyTypeEnum.ADD;
import static com.rzdata.process.enums.LinkTypeEnum.*;

/**
 * 文件变更申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class DocChangeApplyServiceImpl extends ServicePlusImpl<DocChangeApplyMapper, DocChangeApply, DocChangeApplyVo> implements IDocChangeApplyService {
    private static final String ID_SPLIT_KEY = ",";
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;
    @Autowired
    private IGenerateIdService iGenerateIdService;
    @Autowired
    private IDocMessageService docMessageService;
    @Autowired
    private IDocClassService iDocClassService;
    @Autowired
    private IStandardService standardService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    ModifyApplyMapper modifyApplyMapper;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;
    @Autowired
    private IModifyApplyService iModifyApplyService;;

    @Override
    public DocChangeApplyDetailVo queryById(String id) {
        DocChangeApplyVo applyVo = this.baseMapper.selectDetailById(id);
        if (ObjectUtil.isEmpty(applyVo)) {
            return null;
        }
        //List<BasicFileVo> fileVoList = new ArrayList<>();
        DocChangeApplyDetailVo detailVo = BeanUtil.toBean(applyVo, DocChangeApplyDetailVo.class);
        /*if (ObjectUtil.isNotEmpty(detailVo.getAppendixs())) {
            fileVoList = iBasicFileService.listVoByIds(Arrays.asList(detailVo.getAppendixs().split(ID_SPLIT_KEY)));
            for (BasicFileVo basicFileVo : fileVoList) {
                basicFileVo.setLinkCode(basicFileVo.getId());
                basicFileVo.setLinkId(basicFileVo.getId());
            }
        }*/
        //查附件
        List<ModifyApplyLinkVo> appendixList = modifyApplyLinkService.queryDocByApplyIdAndType(id, APPENDIX);
        detailVo.setAppendixFiles(appendixList);


        //备注附件查询开始
        List<ModifyApplyLinkVo> remarkList = modifyApplyLinkService.queryDocByApplyIdAndType(id, APPENDIX_REMARK);
        detailVo.setRemarkFiles(remarkList);
       /* Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("apply_id",id);
        columnMap.put("link_type",LinkTypeEnum.APPENDIX_REMARK);
        List<ModifyApplyLinkVo> modifyApplyLinkVos = modifyApplyLinkService.listVoByMap(columnMap);

        if(modifyApplyLinkVos!=null && modifyApplyLinkVos.size() > 0) {
            List<String> sb = new ArrayList<>();
            for (ModifyApplyLinkVo modifyApplyLinkVo : modifyApplyLinkVos) {
                sb.add(modifyApplyLinkVo.getLinkId());
            }

            List<BasicFileVo> basicFiles = iBasicFileService.listVoByIds(sb);

            for (BasicFileVo basicFileVo : basicFiles) {
                basicFileVo.setLinkCode(basicFileVo.getId());
                basicFileVo.setLinkId(basicFileVo.getId());
            }
            detailVo.setRemarkFiles(basicFiles);
        }*/
        //备注附件查询结束

        /*QueryWrapper<ModifyApply> modifyApplyQueryWrapper = new QueryWrapper<>();
        modifyApplyQueryWrapper.lambda().eq(ModifyApply:: getDocId, applyVo.getDocId());
        modifyApplyQueryWrapper.lambda().orderByDesc(ModifyApply:: getApplyTime);
        modifyApplyQueryWrapper.lambda().eq(ModifyApply:: getChangeType, ADD);
        List<ModifyApply> modifyApply = modifyApplyMapper.selectList(modifyApplyQueryWrapper);
        if (ObjectUtil.isNotEmpty(modifyApply)) {
            List<ModifyApplyLink> linkVoList = new ArrayList<>();
            for (ModifyApply apply : modifyApply) {
                QueryWrapper<ModifyApplyLink> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ModifyApplyLink::getApplyId, apply.getId());
                queryWrapper.lambda().eq(ModifyApplyLink::getIsDeleted, 0);
                queryWrapper.lambda().eq(ModifyApplyLink:: getLinkType, APPENDIX);
                linkVoList.addAll(modifyApplyLinkService.list(queryWrapper));
            }
            if (ObjectUtil.isNotEmpty(linkVoList)&&!ObjectUtil.equal("ADD",applyVo.getChangeType())) {
                List<String> basicFileIdLinks = linkVoList.stream().map(x -> x.getLinkId()).collect(Collectors.toList());
                Map<String, String> idNameMap = new HashMap<>();
                if (CollectionUtil.isNotEmpty(basicFileIdLinks)) {
                    List<BasicFileVo> fileVoList2 = basicFileService.listVoByIds(basicFileIdLinks);
                    if (CollectionUtil.isNotEmpty(fileVoList2)) {
                        for (BasicFileVo fileVo : fileVoList2) {
                            idNameMap.put(fileVo.getId(), fileVo.getFileName());
                        }
                    }
                }
                if (MapUtil.isNotEmpty(idNameMap)) {
                    for (ModifyApplyLink linkVo : linkVoList) {
                        linkVo.setDocName(idNameMap.get(linkVo.getLinkId()));
                    }
                }
                detailVo.setSxAppendixFiles(linkVoList);
            }
        }*/
        //如果不是新增
        if (!ApplyTypeEnum.toType(applyVo.getChangeType()).equals(ADD)) {
            //当前生效附件
            List<DocLinkLogVo> preAppendixList = docLinkLogService.queryDocLinkVo(applyVo.getDocId(), applyVo.getVersionId(), APPENDIX);
            detailVo.setSxAppendixFiles(preAppendixList);
            //当前生效版本
            List<DocLinkLogVo> preDocList = docLinkLogService.queryDocLinkVo(applyVo.getDocId(), applyVo.getVersionId(), DOC);
            if (ObjectUtil.isNotEmpty(preDocList)) {
                detailVo.setFile(preDocList.get(0));
            }
        }
       /* StandardVo file = standardService.getVoById(applyVo.getDocId());
        if (file!=null){
            BasicFile basicFileVo = basicFileService.getById(file.getFileId());
            detailVo.setFile(basicFileVo);
        }*/
        return detailVo;
    }

    @Override
    public TableDataInfo<DocChangeApplyVo> queryPageList(DocChangeApplyBo bo) {
        PagePlus<DocChangeApply, DocChangeApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocChangeApplyVo> queryList(DocChangeApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocChangeApply> buildQueryWrapper(DocChangeApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocChangeApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getId()), DocChangeApply::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getApplyTitle()), DocChangeApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DocChangeApply::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), DocChangeApply::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocChangeApply::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), DocChangeApply::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocChangeApply::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), DocChangeApply::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getDeptId() != null, DocChangeApply::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getUserName()), DocChangeApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), DocChangeApply::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getEditUserName()), DocChangeApply::getEditUserName, bo.getEditUserName());
        lqw.eq(bo.getEditDeptId() != null, DocChangeApply::getEditDeptId, bo.getEditDeptId());
        return lqw;
    }

    /**
     * 创建一条关联记录
     */
    private ModifyApplyLink createModifyApplyLink(ModifyApplyLinkBo linkBo, String applyId, Date now, String versionId, String versionValue,
                                                  LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        ModifyApplyLink linkPo = BeanUtil.toBean(linkBo, ModifyApplyLink.class);
        linkPo.setApplyId(applyId);
        if (ObjectUtil.isEmpty(linkPo.getCreateTime())) {
            linkPo.setCreateTime(now);
        }
        linkPo.setUpdateTime(now);
        linkPo.setLinkType(linkTypeEnum.name());
        linkPo.setDocName(linkBo.getDocName());
        if (ObjectUtil.isNotEmpty(linkBo.getDocId())) {
            linkPo.setDocId(linkBo.getDocId());
        }
        if (ObjectUtil.isNotNull(linkClassEnum)) {
            linkPo.setLinkClass(linkClassEnum.name());
        }
//        if (ObjectUtil.isNotEmpty(linkBo.getDocId())) {
//            String type = this.baseMapper.getDocTypeByDocId(linkBo.getDocId());
//            if (ObjectUtil.isNotEmpty(type)) {
//                linkPo.setLinkClass(type);
//            }
//        }
        if (ObjectUtil.isEmpty(linkBo.getVersionId())) {
            linkPo.setVersionId(versionId);
        }
        if (ObjectUtil.isEmpty(linkBo.getVersionValue())) {
            linkPo.setVersionValue(versionValue);
        }
        return linkPo;
    }

    /**
     * 生成多条记录
     */
    private void buildModifyApplyLink(List<ModifyApplyLink> linkList, List<ModifyApplyLinkBo> linkBoList, String applyId,
                                      Date now, String versionId, String versionValue, LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        if (ObjectUtil.isNotEmpty(linkBoList)) {
            List<ModifyApplyLink> appendixList = linkBoList.stream().map(appendixBo -> createModifyApplyLink(
                    appendixBo, applyId, now, versionId, versionValue, linkTypeEnum, linkClassEnum)).collect(Collectors.toList());
            linkList.addAll(appendixList);
        }
    }

    /**
     * 组装关联记录
     */
    private List<ModifyApplyLink> buildModifyApplyLink(DocChangeApplyBo bo) {
        List<ModifyApplyLink> linkList = Lists.newArrayList();

        //备注的附件
        buildModifyApplyLink(linkList, bo.getRemarkDoc(), bo.getId(), bo.getCreateTime(), bo.getVersionId(), bo.getVersionValue(), LinkTypeEnum.APPENDIX_REMARK, LinkClassEnum.FILE);

        return linkList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessInstanceModel insertByBo(DocChangeApplyBo bo) {
        DocChangeApply add = BeanUtil.toBean(bo, DocChangeApply.class);
        bo.setChangeType(ApplyTypeEnum.CHANGE.toString());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        validEntityBeforeSave(add);
        String applyId = generateApplyId(bo.getEditUserName());

        List<ModifyApplyLink> linkList = buildModifyApplyLink(bo);
        if (ObjectUtil.isNotEmpty(linkList)) {
            for (ModifyApplyLink modifyApplyLink : linkList) {
                modifyApplyLink.setApplyId(applyId);
                modifyApplyLink.setId(null);
            }
            modifyApplyLinkService.saveAll(linkList);
        }

        add.setCreateTime(new Date());
        add.setUserName(loginUser.getUsername());
        add.setDeptId(loginUser.getDeptId());
//        add.setStatus(ApplyStatusEnum.APPLIED.getStatus());
        add.setId(applyId);
        boolean flag = save(add);
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            // start doc change apply
            try {
//                ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
//                processInstanceModel = workflowService.startProcess(bo.getApplyTitle(),
//                        processConfig.getProcDefKeyBGSQ(), loginUser.getUsername(), loginUser.getDeptId() + "",
//                        add.getId());
                bo.getBpmClientInputModel().getModel().setWf_sendUserId(loginUser.getUsername());
                bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(loginUser.getDeptId().toString());
                processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                saveWorkFlowLog(add, processInstanceModel);
                if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                    Standard standard = standardService.getById(bo.getDocId());
                    if (ObjectUtil.isNotEmpty(standard)) {
                        standard.setLockStatus(NumberConstants.ONE);
                        standardService.updateById(standard);
                    }
                }
            } catch (Exception e) {
                // TODO: handle exception here
                log.error("start doc change apply fail:", e);
            }
        }
        // for test
//        startModifyApply(add);
        return processInstanceModel;
    }

    private String generateApplyId(String userName) {
        return iGenerateIdService.generateApplyId(ApplyTypeEnum.CHANGE, userName);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        if(log.isDebugEnabled()){
            log.debug("onProcessEvent====1==="+event);
        }
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key -> key.equals(processConfig.getProcDefKeyBGSQ())).isPresent()) {
            return;
        }
        DocChangeApply changeApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(changeApply)) {
            return;
        }
        changeApply.setStatus(event.getStatus());
        changeApply.setUpdateTime(new Date());
        this.baseMapper.updateById(changeApply);
        String status = event.getStatus();

        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
//            if (ObjectUtil.isNotEmpty(changeApply.getDocId())) {
//                Standard standard = standardService.getById(changeApply.getDocId());
//                if (ObjectUtil.isNotEmpty(standard)) {
//                    standard.setLockStatus(NumberConstants.ZERO);
//                    standardService.updateById(standard);
//                }
//            }
//            status = NumberConstants.ONE + "";
        }
        workflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, event.getModel().getWf_nextActDefName(),event.getApplyStatus());
        //流程结束开启一个新流程
        if(log.isDebugEnabled()){
            log.debug("onProcessEvent====2==="+event);
        }
        if (ProcessStatusConstants.TO_DONE.equals(event.getStatus())) {
            //文件变更申请结束后写入日志
            event.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
            docMessageService.insertMessage(event, MsgTypeEnum.CHANGE);
            //需要根据结论判断是否开启20220411
            if(StringUtils.equals(event.getApplyStatus(),ApplyStatusEnum.PASS.getCode())) {
                startModifyApply(changeApply);
            }
        }
    }

    /**
     * 开启新增流程
     */
    private void startModifyApply(DocChangeApply changeApply) {

        ModifyApplyBo modifyApplyBo = BeanUtil.toBean(changeApply, ModifyApplyBo.class);
        modifyApplyBo.setUserName(changeApply.getEditUserName());
        modifyApplyBo.setDeptId(changeApply.getEditDeptId());
        modifyApplyBo.setCreateTime(new Date());
        modifyApplyBo.setId(null);
        modifyApplyBo.setChangeId(changeApply.getId());
        modifyApplyBo.setExpiration(getDocExpiration(changeApply.getDocClass()));
        iModifyApplyService.insertBoNoBpm(modifyApplyBo);
        if(log.isDebugEnabled()){
            log.debug("startModifyApply====1==="+modifyApplyBo);
        }
        ProcessEventBus.post(new StartModifyApplyEvent(changeApply.getId(), ApplyTypeEnum.CHANGE.name(), modifyApplyBo));
    }
    /**
     * TODO: 根据文档类型获取有效年限
     *
     * @param docClass
     * @return
     */
    private Long getDocExpiration(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        if (ObjectUtil.isNotEmpty(voById)){
            return voById.getExpiration();
        }else {
            return 1L;
        }
    }
    @Override
    public Boolean updateByBo(DocChangeApplyBo bo) {
        DocChangeApply update = BeanUtil.toBean(bo, DocChangeApply.class);
        bo.setChangeType(ApplyTypeEnum.CHANGE.toString());
        validEntityBeforeSave(update);
        try {
            bo.getBpmClientInputModel().getModel().setWf_sendUserId(update.getUserName());
            bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(update.getDeptId().toString());
            ProcessInstanceModel processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), update.getId());
            saveWorkFlowLog(update, processInstanceModel);
        } catch (Exception e) {
            // TODO: handle exception here
            log.error("start doc change apply fail:", e);
        }
        return updateById(update);
    }

    /**
     * 保存申请记录
     *
     * @param po
     * @param processInstanceModel
     */
    private void saveWorkFlowLog(DocChangeApply po, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLog workflowApplyLog = BeanUtil.toBean(po, WorkflowApplyLog.class);
        workflowApplyLog.setProcDefKey(processInstanceModel.getProcDefId()).setProcInstId(processInstanceModel.getProcInstId())
                .setApplyClass(ApplyTypeEnum.CHANGE.name()).setId(po.getId()).setApplyStatus(ApplyStatusEnum.PROCESSING.getCode()).setApplyTime(po.getCreateTime())
                .setSender(po.getUserName()).setVersionValue(po.getVersionValue()).setVersionId(po.getVersionId());
        workflowApplyLog.setChangeType(po.getChangeType());
        workflowApplyLog.setProcStatus(ProcessStatusConstants.TO_DO);
        workflowApplyLogService.save(workflowApplyLog);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocChangeApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

}
