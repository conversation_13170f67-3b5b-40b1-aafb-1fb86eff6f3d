package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DocMessageBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.BorrowApplyUserVo;
import com.rzdata.process.domain.vo.DocMessageVo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.enums.ReviewResultEnum;
import com.rzdata.process.mapper.DocMessageMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysRoleService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:04
 * @Version 1.0
 * @Description 消息服务实现类
 */
@Service
public class DocMessageServiceImpl extends ServicePlusImpl<DocMessageMapper, DocMessage, DocMessageVo> implements IDocMessageService {

    @Autowired
    IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    IBorrowApplyUserService borrowApplyUserService;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    ISysRoleService sysRoleService;

    @Autowired
    IStandardService standardService;

    @Autowired
    IDistributeItemService distributeItemService;

    @Autowired
    IVersionService versionService;

    @Autowired
    ISysDeptService deptService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    private IReviewApplyItemService applyItemService;

    @Override
    public TableDataInfo<DocMessageVo> queryListByPage(DocMessageBo bo) {
        PagePlus page = new PagePlus<>(bo.getPageNum(), bo.getPageSize());
        PagePlus<DocMessage, DocMessageVo> result = pageVo(page, buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public int getUnreadNum(DocMessageBo bo) {
        LambdaQueryWrapper<DocMessage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DocMessage::getRecoveryUser, SecurityUtils.getLoginUser().getUsername());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getRecoveryUserId()), DocMessage::getRecoveryUserId, bo.getRecoveryUserId());
        queryWrapper.eq(DocMessage::getMsgStatus, 0);
        List<DocMessageVo> docMessageVos = listVo(queryWrapper);
        return docMessageVos.size();
    }

    @Override
    public void insertMessage(ProcessResultEvent event,MsgTypeEnum msgType) {
        DocMessage docMessage = new DocMessage();
        try {
            insertMessage(event,docMessage,msgType);
        } catch (ServerException exception) {
            exception.printStackTrace();
        }
    }


    @Override
    public void insertMessageByBorrow(ProcessResultEvent event, MsgTypeEnum msgType) {
        try {
            DocMessage docMessage = new DocMessage();
            insertMessage(event,docMessage,msgType);
            //如果为借阅申请 且申请结果为通过时
            if (ApplyStatusEnum.PASS.getCode().equals(event.getApplyStatus())) {
                DocMessage newDocMessage = docMessage;
//                newDocMessage.setId(null);
                LambdaQueryWrapper<BorrowApplyUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BorrowApplyUser::getApplyId, event.getApplyId());
                List<BorrowApplyUserVo> list = borrowApplyUserService.listVo(queryWrapper);
                list = list.stream().filter(i -> !StringUtils.equals(i.getBorrowUser(), docMessage.getRecoveryUser())).collect(Collectors.toList());
                list.forEach(borrowApplyUserVo -> {
                    //下一流程 也就是借阅人也需要生成一条消息
                    newDocMessage.setRecoveryUser(borrowApplyUserVo.getBorrowUser());
                    newDocMessage.setId(null);
                    this.baseMapper.insert(newDocMessage);
                });
            }
        }catch (Exception exception){
            exception.printStackTrace();
        }
    }

    private void insertMessage(ProcessResultEvent event,DocMessage docMessage,MsgTypeEnum msgType) throws ServerException {
        String applyId = event.getApplyId();
        WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(applyId);
        if (workflowApplyLog==null){
            throw new ServerException("未找到流程记录");
        }
        docMessage.setApplyId(applyId);
        docMessage.setDocId(workflowApplyLog.getDocId());
        docMessage.setDocName(workflowApplyLog.getDocName());
        docMessage.setVersionValue(workflowApplyLog.getVersionValue());
        docMessage.setDocClass(workflowApplyLog.getDocClass());
        SysDept dept = deptService.getById(workflowApplyLog.getDeptId());
        if (ObjectUtils.isNotEmpty(dept)){
            docMessage.setDeptId(workflowApplyLog.getDeptId());
            docMessage.setDeptName(dept.getDeptName());
        }
        //只有在修订和作废的变更申请的时候 WorkflowApplyLog的versionId 才会有值 文件新增的变更申请都没有生成文件 没有值
        if (ObjectUtil.isNotEmpty(workflowApplyLog.getVersionId())) {
            Version version = this.versionService.getById(workflowApplyLog.getVersionId());
            Standard standardDept = this.standardService.getById(version.getStandardId());
            if (ObjectUtils.isNotEmpty(standardDept)){
                SysDept sysDept = this.deptService.getById(standardDept.getDeptId());
                if(ObjectUtil.isNotEmpty(sysDept)) {
                    docMessage.setDeptId(sysDept.getDeptId());
                    docMessage.setDeptName(sysDept.getDeptName());
                }
            }
        }
        docMessage.setMsgStatus(0);
        docMessage.setMsgClass(msgType.getType());
        docMessage.setCreateUser(SecurityUtils.getUsername());
        docMessage.setCreateTime(new Date());
        docMessage.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
        //接收人(也就是这条消息需要提醒谁)是这个申请的发起者
        docMessage.setRecoveryUser(workflowApplyLog.getSender());
        //这里要判断
        if (msgType.equals(MsgTypeEnum.REVIEW)){
            ReviewApplyItemBo bo = new ReviewApplyItemBo();
            bo.setIds(Collections.singletonList(applyId));
            List<ReviewApplyItemVo> reviewApplyItemVos = applyItemService.queryItemList(bo);
            if(CollectionUtil.isNotEmpty(reviewApplyItemVos)){
                List<DocMessage> docMessageList = new ArrayList<>();
                for(ReviewApplyItemVo vo: reviewApplyItemVos) {
                    DocMessage doc = BeanUtil.toBean(docMessage, DocMessage.class);
                    doc.setDocId(vo.getDocId());
                    doc.setDocName(vo.getDocName());
                    doc.setVersionValue(vo.getVersionValue());
                    doc.setDocClass(vo.getDocClass());
                    docMessageList.add(doc);
                    String msgInfo = event.getMsgInfo();
                    if (ApplyStatusEnum.EXTENSION.getCode().equals(event.getApplyStatus())) {
                        /*Long reviewCycle = iDocClassService.getById(vo.getDocClass()).getReviewCycle();
                        Date date = DateUtil.offset(DateUtil.date(), DateField.YEAR, reviewCycle.intValue());
                        msgInfo += "至" + DateUtil.formatDate(date);*/
                        msgInfo = "有效期延期";
                    }
                    doc.setMsgInfo(msgInfo);
                }
                this.saveBatch(docMessageList);
                return;
            }
        }
        //docMessage.setRecoveryUserId("");
        this.baseMapper.insert(docMessage);
    }

    /**
     * 获取消息显示msgInfo
     * @param actionType
     * @return
     */
    private String getReviewMsgInfo(String actionType){
        switch (actionType){
            case "KEEP":
                return "保持现状";
            case "EXTENSION":
                return "延长有效期";
            case "UPDATE":
                return "文件修订";
            case "DISUSE":
                return "文件作废";
            default:
                return "未知操作";
        }
    }

    @Override
    public void insertMessageByDistribute(DocDistributeLog distributeLog) {
        //准备生成消息
        String deptId = distributeLog.getDeptId();
        List<SysUser> sysUserList = sysUserService.selectDeptFileManagerByDeptId(deptId);
        sysUserList.forEach(sysUser -> {
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(distributeLog.getApplyId());
            docMessage.setDocId(distributeLog.getDocId());
            docMessage.setVersionValue(distributeLog.getVersionValue());
            docMessage.setDocClass(distributeLog.getDocClass());
            docMessage.setDocName(distributeLog.getDocName());
            docMessage.setDeptId(distributeLog.getCompileDeptId());
            docMessage.setDeptName(distributeLog.getCompileDeptName());
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("待签收");
            docMessage.setMsgClass(MsgTypeEnum.SIGN.getType());
            docMessage.setCreateTime(new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            //docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            docMessage.setRecoveryUserId(sysUser.getUserId());
            this.baseMapper.insert(docMessage);
        });
    }

    @Override
    public void insertMessageByReceive(ModifyApply applyPo) {
        //准备生成消息
        String deptId = applyPo.getDeptId();
        //查出部门文件管理员
        List<SysUser> sysUserList = sysUserService.selectDeptFileManagerByDeptId(deptId);
        //发送消息给部门文件管理员
        sysUserList.forEach(sysUser -> {
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(applyPo.getId());
            docMessage.setDocId(applyPo.getDocId());
            docMessage.setVersionValue(applyPo.getVersionValue());
            LambdaQueryWrapper <Standard>lambdaQueryWrapper = new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(Standard::getDeptId,applyPo.getDeptId());
            lambdaQueryWrapper.eq(Standard::getId,applyPo.getDocId());
            docMessage.setDocClass(applyPo.getDocClass());
            docMessage.setDocName(applyPo.getDocName());
            SysDept sysDept = deptService.getById(applyPo.getDeptId());
            if (ObjectUtils.isNotEmpty(sysDept)){
                docMessage.setDeptId(sysDept.getDeptId());
                docMessage.setDeptName(sysDept.getDeptName());
            }
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("待回收");
            docMessage.setMsgClass(MsgTypeEnum.RECEIVE.getType());
            docMessage.setCreateTime(new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            //docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            docMessage.setRecoveryUserId(sysUser.getUserId());
            this.baseMapper.insert(docMessage);
        });
    }

    @Override
    public void insertMessageByValidity(DocDistributeLog distributeLog) {
        //准备生成消息
        String deptId = distributeLog.getDeptId();
        LambdaQueryWrapper <SysUser> queryUser = new LambdaQueryWrapper();
        queryUser.eq(SysUser::getDeptId,deptId);
        List<SysUser> sysUserList = sysUserService.list(queryUser);
        sysUserList.forEach(sysUser -> {
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(distributeLog.getApplyId());
            docMessage.setDocId(distributeLog.getDocId());
            docMessage.setDocName(distributeLog.getDocName());
            docMessage.setVersionValue(distributeLog.getVersionValue());
            docMessage.setDocClass(distributeLog.getDocClass());
            docMessage.setDocName(distributeLog.getDocName());
            docMessage.setDeptId(distributeLog.getCompileDeptId());
            docMessage.setDeptName(distributeLog.getCompileDeptName());
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("生效");
            docMessage.setMsgClass(MsgTypeEnum.VALIDITY.getType());
            docMessage.setCreateTime(new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            //docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            //docMessage.setRecoveryUserId(sysUser.getUserId());
            this.baseMapper.insert(docMessage);
        });
    }

    @Override
    public void insertMessageByValidity(ModifyApply applyPo) {
        //查出生产负责人和质量负责人
        List<SysUser> users = sysUserService.selectQaAndSc();
        //发送消息给部门所有人和生产负责人还有质量负责人
        users.forEach(sysUser -> {
            DocMessage docMessage = new DocMessage();
            docMessage.setApplyId(applyPo.getId());
            docMessage.setDocId(applyPo.getDocId());
            String value = versionService.queryById(applyPo.getVersionId()).getVersionValue();
            docMessage.setVersionValue(value);
            //LambdaQueryWrapper <Standard>lambdaQueryWrapper = new LambdaQueryWrapper();
            //lambdaQueryWrapper.eq(Standard::getDeptId,applyPo.getDeptId());
            //lambdaQueryWrapper.eq(Standard::getId,applyPo.getDocId());
            //Standard one = standardService.getOne(lambdaQueryWrapper);
            docMessage.setDocClass(applyPo.getDocClass());
            docMessage.setDocName(applyPo.getDocName());
            SysDept sysDept = deptService.getById(applyPo.getDeptId());
            if (ObjectUtils.isNotEmpty(sysDept)){
                docMessage.setDeptName(sysDept.getDeptName());
            }
            docMessage.setMsgStatus(0);
            docMessage.setMsgInfo("生效");
            docMessage.setMsgClass(MsgTypeEnum.VALIDITY.getType());
            docMessage.setCreateTime(ObjectUtil.isNotEmpty(applyPo.getSetupTime())?applyPo.getSetupTime():new Date());
            docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
            docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
            //docMessage.setCreateUserId("");
            //docMessage.setRecoveryTime(new Date());
            docMessage.setRecoveryUser(sysUser.getUserName());
            docMessage.setRecoveryUserId(sysUser.getUserId());
            this.baseMapper.insert(docMessage);
        });
    }

    @Override
    public void insertMessageByInvalidation(List<DistributeItem> distributeItemList) {
        //准备生成消息
        distributeItemList.forEach(distributeItem -> {
            //发送已失效消息给所有分发部门的人
            String deptId = distributeItem.getDeptId();
            //查出部门所有人
            LambdaQueryWrapper <SysUser> queryAllUser = new LambdaQueryWrapper();
            queryAllUser.eq(SysUser::getDeptId,deptId);
            List<SysUser> allDeptUserList = sysUserService.list(queryAllUser);
            //查出生产负责人和质量负责人
            List<SysUser> users = sysUserService.selectQaAndSc();
            //加进来一起遍历
            allDeptUserList.addAll(users);
            //发送消息给部门所有人和生产负责人还有质量负责人
            allDeptUserList.forEach(sysUser -> {
                DocMessage docMessage = new DocMessage();
                docMessage.setApplyId(distributeItem.getApplyId());
                docMessage.setDocId(distributeItem.getDocId());
                String value = versionService.queryById(distributeItem.getVersionId()).getVersionValue();
                docMessage.setVersionValue(value);
                //lambdaQueryWrapper.eq(Standard::getDeptId,distributeItem.getDeptId());
                Version version = versionService.getById(distributeItem.getVersionId());
                Standard one = standardService.getById(version.getStandardId());
                docMessage.setDocClass(one.getDocClass());
                docMessage.setDocName(one.getDocName());
                SysDept sysDept = deptService.getById(one.getDeptId());
                if (ObjectUtils.isNotEmpty(sysDept)){
                    docMessage.setDeptId(sysDept.getDeptId());
                    docMessage.setDeptName(sysDept.getDeptName());
                }
                docMessage.setMsgStatus(0);
                docMessage.setMsgInfo("失效");
                docMessage.setMsgClass(MsgTypeEnum.INVALIDATION.getType());
                docMessage.setCreateTime(new Date());
                docMessage.setCreateUser(SecurityUtils.getLoginUser().getUsername());
                docMessage.setCreateUserId(SecurityUtils.getLoginUser().getUserId());
                //docMessage.setCreateUserId("");
                //docMessage.setRecoveryTime(new Date());
                docMessage.setRecoveryUser(sysUser.getUserName());
                docMessage.setRecoveryUserId(sysUser.getUserId());
                this.baseMapper.insert(docMessage);
            });
        });


    }

    @Override
    public void insertMessageEndArchiving(String applyId) throws ServerException {
        WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(applyId);
        if (workflowApplyLog==null){
            throw new ServerException("未找到流程记录");
        }
        DocMessage docMessage = new DocMessage();
        docMessage.setApplyId(applyId);
        docMessage.setDocId(workflowApplyLog.getDocId());
        docMessage.setDocName(workflowApplyLog.getDocName());
        docMessage.setVersionValue(workflowApplyLog.getVersionValue());
        docMessage.setDocClass(workflowApplyLog.getDocClass());
        SysDept sysDept = this.deptService.getById(workflowApplyLog.getDeptId());
        if(ObjectUtil.isNotEmpty(sysDept)) {
            docMessage.setDeptId(sysDept.getDeptId());
            docMessage.setDeptName(sysDept.getDeptName());
        }
        docMessage.setMsgStatus(0);
        docMessage.setMsgClass(MsgTypeEnum.END_ARCHIVING.getType());
        docMessage.setCreateUser(SecurityUtils.getUsername());
        docMessage.setCreateTime(new Date());
        docMessage.setMsgInfo("终止");
        //接收人(也就是这条消息需要提醒谁)是这个申请的发起者
        docMessage.setRecoveryUser(workflowApplyLog.getSender());
        this.baseMapper.insert(docMessage);
        List<SysUser> userList = iSysUserService.selectListAllByRoleKey(Constants.ONE);
        for (SysUser user:userList) {
            docMessage.setRecoveryUser(user.getUserName());
            docMessage.setId(null);
            this.baseMapper.insert(docMessage);
        }
    }

    @Override
    public List<DocMessageVo> queryMessageDelivery() {
        QueryWrapper<DocMessage> queryWrapper = Wrappers.query();
        //queryWrapper.select("distinct doc_id,doc_name,version_value,dept_name,create_time,msg_info");
        //queryWrapper.eq(DocMessage::getRecoveryUser, SecurityUtils.getLoginUser().getUsername());
        queryWrapper.lambda().groupBy(DocMessage::getDocName);
        queryWrapper.lambda().groupBy(DocMessage::getVersionValue);
        queryWrapper.lambda().eq(DocMessage::getMsgClass,MsgTypeEnum.VALIDITY.getType()).or().eq(DocMessage::getMsgClass,MsgTypeEnum.INVALIDATION.getType());
        queryWrapper.lambda().orderByDesc(DocMessage::getCreateTime);
        return listVo(queryWrapper);
    }

    private LambdaQueryWrapper<DocMessage> buildQueryWrapper(DocMessageBo bo) {
        LambdaQueryWrapper<DocMessage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(bo.getDocClass()), DocMessage::getDocClass, bo.getDocClass());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDocName()), DocMessage::getDocName, bo.getDocName());
        queryWrapper.eq(StringUtils.isNotNull(bo.getDeptId()), DocMessage::getDeptId, bo.getDeptId());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgClass()), DocMessage::getMsgClass, bo.getMsgClass());
        queryWrapper.eq(DocMessage::getRecoveryUser, SecurityUtils.getLoginUser().getUsername());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getRecoveryUserId()), DocMessage::getRecoveryUserId, bo.getRecoveryUserId());
        queryWrapper.eq(StringUtils.isNotNull(bo.getMsgStatus()), DocMessage::getMsgStatus, bo.getMsgStatus());
        queryWrapper.le(DocMessage::getCreateTime, DateUtil.date());
        queryWrapper.orderByDesc(DocMessage::getCreateTime);
        return queryWrapper;
    }

    @Override
    public Boolean insert(DocMessage docMessage) {
        return save(docMessage);
    }
}
