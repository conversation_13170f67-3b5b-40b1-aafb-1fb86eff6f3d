package com.rzdata.process.service;

import com.rzdata.process.domain.ModifyApplyTrain;
import com.rzdata.process.domain.vo.ModifyApplyTrainVo;
import com.rzdata.process.domain.bo.ModifyApplyTrainBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件变更操作申请培训记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
public interface IModifyApplyTrainService extends IServicePlus<ModifyApplyTrain, ModifyApplyTrainVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ModifyApplyTrainVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ModifyApplyTrainVo> queryPageList(ModifyApplyTrainBo bo);

	/**
	 * 查询列表
	 */
	List<ModifyApplyTrainVo> queryList(ModifyApplyTrainBo bo);

	/**
	 * 根据新增业务对象插入文件变更操作申请培训记录
	 * @param bo 文件变更操作申请培训记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(ModifyApplyTrainBo bo);

	/**
	 * 根据编辑业务对象修改文件变更操作申请培训记录
	 * @param bo 文件变更操作申请培训记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ModifyApplyTrainBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
