package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ReissueApplyBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.ReissueApplyVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.mapper.ReissueApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件补发申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class ReissueApplyServiceImpl extends ServicePlusImpl<ReissueApplyMapper, ReissueApply, ReissueApplyVo> implements IReissueApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    ICodeRuleService iCodeRuleService;

    @Autowired
    private IDocDistributeLogService docDistributeLogService;

    //@Autowired
    //private IDistributeItemService distributeItemService;
    @Autowired
    private IPrintLogItemService printLogItemService;

    @Autowired
    private IPrintLogService printLogService;

    @Autowired
    private IDocMessageService docMessageService;

    @Autowired
    private  IGenerateIdService iGenerateIdService;

    @Override
    public ReissueApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ReissueApplyVo> queryPageList(ReissueApplyBo bo) {
        PagePlus<ReissueApply, ReissueApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ReissueApplyVo> queryList(ReissueApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ReissueApply> buildQueryWrapper(ReissueApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReissueApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReissueApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ReissueApply::getDocId, bo.getDocId());
        lqw.eq(bo.getDeptId() != null, ReissueApply::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getDistributeItemIds()), ReissueApply::getDistributeItemIds, bo.getDistributeItemIds());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ReissueApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyReason()), ReissueApply::getApplyReason, bo.getApplyReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ReissueApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceModel insertByBo(ReissueApplyBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ReissueApply add = BeanUtil.toBean(bo, ReissueApply.class);
        bo.setChangeType(ApplyTypeEnum.REISSUE.toString());
        add.setId(iGenerateIdService.generateApplyId(ApplyTypeEnum.REISSUE,loginUser.getUsername()));
        add.setCreateTime(new Date());
        add.setUserName(loginUser.getUsername());
        add.setNickName(loginUser.getNickName());
        add.setStatus(NumberConstants.ONE + "");
        add.setDeptId(loginUser.getDeptId());
        add.setApplyTitle("补发申请");
        boolean flag = save(add);
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().getModel().setWf_sendUserId(loginUser.getUsername());
                bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(loginUser.getDeptId().toString());
                processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), add.getId());
                saveWorkFlowLog(bo, processInstanceModel);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    /**
     * 保存申请记录
     * @param bo
     * @param processInstanceModel
     */
    private void saveWorkFlowLog(ReissueApplyBo bo, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLogBo applyLogBo = new WorkflowApplyLogBo();
        applyLogBo.setId(bo.getId());
        applyLogBo.setApplyClass(ApplyTypeEnum.REISSUE.name());
        applyLogBo.setDocId(bo.getDocId());
        applyLogBo.setDocName(bo.getDocName());
        applyLogBo.setDocClass(bo.getDocClass());
        applyLogBo.setProcDefKey(processInstanceModel.getProcDefId());
        applyLogBo.setProcInstId(processInstanceModel.getProcInstId());
        applyLogBo.setApplyStatus(bo.getBpmClientInputModel().getApplyStatus());
        applyLogBo.setProcStatus(ProcessStatusConstants.TO_DO);
        iWorkflowApplyLogService.insertByBo(applyLogBo);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key -> key.equals(processConfig.getProcDefKeyBFSQ())).isPresent()) {
            return;
        }
        ReissueApply reissueApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(reissueApply)) {
            return;
        }
        reissueApply.setStatus(event.getStatus());
        reissueApply.setUpdateTime(new Date());
        this.baseMapper.updateById(reissueApply);
        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            //先生成补发消息
            docMessageService.insertMessage(event, MsgTypeEnum.REISSUE);
            String [] strings = reissueApply.getDistributeItemIds().split(",");

            LambdaQueryWrapper<PrintLogItem> itemWrapper = new LambdaQueryWrapper<>();
            Arrays.stream(strings).forEach(
                    id -> {
                        itemWrapper.clear();
                        itemWrapper.eq(PrintLogItem::getDistributeItemId,id);
                        //DocDistributeLog docDistributeLog = docDistributeLogService.getById(id);
                        //查出这条分发号的打印记录
                        PrintLogItem printLogItem = printLogItemService.getOne(itemWrapper);
                        //printLogItem.setNums(printLogItem.getNums()+1);
                        //状态设为未打印
                        printLogItem.setStatus("N");
                        //增加备注为补发
                        printLogItem.setRemake("补发");
                        //把流程id也更新成补发的流程id
                        printLogItem.setApplyId(reissueApply.getId());
                        printLogItemService.updateById(printLogItem);
                        //找到这个打印数据的列表数据 把待打印数量+1
                        PrintLog printLog = printLogService.getById(printLogItem.getPrintId());
                        printLog.setPrintNums(printLog.getPrintNums()+1);
                        printLogService.updateById(printLog);
                    }
            );
            status = ProcessStatusConstants.TO_DONE;
            actDefName = "结束";
            //DocDistributeLog distributeLog = new DocDistributeLog();
            //CreateNoBo createNoBo = new CreateNoBo();
            //createNoBo.setRuleId("1480802024535072769");
            //createNoBo.setType(NumberConstants.TWO);
            //createNoBo.setStr(ProcessNoRuleEnum.ZF.name());
            //createNoBo.setUserName(SecurityUtils.getUsername());
            //String id = iCodeRuleService.createNoByRule(createNoBo);
            //distributeLog.setId(id);
            //distributeLog.setApplyId(reissueApply.getId());
            //distributeLog.setDeptId(reissueApply.getDeptId());
            //distributeLog.setDbStatus(NumberConstants.ZERO);
            //distributeLog.setCreateTime(new Date());
            //distributeLog.setDocId(reissueApply.getDocId());
            //Standard doc = standardService.getById(reissueApply.getDocId());
            //if (ObjectUtil.isNotEmpty(doc)){
            //    distributeLog.setDocName(doc.getDocName());
            //    distributeLog.setDocClass(doc.getDocClass());
            //}
            //distributeLog.setNums(Long.valueOf(NumberConstants.ONE));
            //distributeLog.setVersionId(reissueApply.getVersionId());
            //distributeLog.setVersionValue(reissueApply.getVersionValue());
            //docDistributeLogService.save(distributeLog);
            ////再生成待签收消息
            //docMessageService.insertMessageByDistribute(distributeLog);

        }
        iWorkflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, actDefName,event.getApplyStatus());
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    @Override
    public Boolean updateByBo(ReissueApplyBo bo) {
        ReissueApply update = BeanUtil.toBean(bo, ReissueApply.class);
        bo.setChangeType(ApplyTypeEnum.REISSUE.toString());
        validEntityBeforeSave(update);
        try {
            bo.getBpmClientInputModel().getModel().setWf_sendUserId(update.getUserName());
            bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(update.getDeptId().toString());
            ProcessInstanceModel processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(), update.getId());
            bo.setProcStatus(ProcessStatusConstants.TO_DO);
            saveWorkFlowLog(bo, processInstanceModel);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReissueApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<ReissueApply> listByDisItemId(String disItemId) {
        return this.baseMapper.listByDisItemId(disItemId);
    }
}
