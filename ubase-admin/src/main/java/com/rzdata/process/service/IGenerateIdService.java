package com.rzdata.process.service;

import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.setting.domain.bo.CreateNewNoBo;

/**
 * <AUTHOR>
 * @Date 2022/1/12 上午10:25
 * @Version 1.0
 * @Desc 生成ID
 */
public interface IGenerateIdService {
    /**
     * 生成申请ID
     * @param applyTypeEnum 申请类型
     * @return
     */
    String generateApplyId(ApplyTypeEnum applyTypeEnum, String userName) ;

    /**
     * 生成文档ID
     * @return
     */
    DocNoVo generateDocId(ModifyApply modifyApply) throws Exception;

    /**
     * 生成记录文件ID
     * @param applyPo
     * @param applyLink
     * @return
     */
    DocNoVo generateRecordDocId(ModifyApply applyPo, ModifyApplyLink applyLink) throws Exception;

    /**
     * 生成分发号
     * @param bo
     * @param docClass
     * @return
     */
    String generateDistributeId(DistributeItem bo,String docClass) throws Exception;

    /**
     * 保存历史文件编号
     * @param buinessId 业务主键
     * @param docClass 文件类型
     * @param docId 文件编号
     * @return
     */
    boolean saveOldDocId(String buinessId,String docClass,String docId);

    /**
     * 判断文件编号是否存在
     * @param docId 文件编号
     * @return
     */
    boolean checkDocIdExist(String docId);

    /**
     * 更新文件编号流水号
     * @param bo
     * @return
     * @throws Exception
     */
    Boolean updateDocSerialNumber(CreateNewNoBo bo) throws Exception;

    /**
     * 更新记录文件流水号
     * @param bo
     * @return
     * @throws Exception
     */
    Boolean updateRecordSerialNumber(CreateNewNoBo bo) throws Exception;
}
