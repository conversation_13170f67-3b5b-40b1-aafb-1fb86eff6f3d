package com.rzdata.process.service;

import com.rzdata.process.domain.ExtraApplyDocNumber;
import com.rzdata.process.domain.vo.ExtraApplyDocNumberVo;
import com.rzdata.process.domain.bo.ExtraApplyDocNumberBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件增发申请文件份数Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IExtraApplyDocNumberService extends IServicePlus<ExtraApplyDocNumber, ExtraApplyDocNumberVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ExtraApplyDocNumberVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ExtraApplyDocNumberVo> queryPageList(ExtraApplyDocNumberBo bo);

	/**
	 * 查询列表
	 */
	List<ExtraApplyDocNumberVo> queryList(ExtraApplyDocNumberBo bo);

	/**
	 * 根据新增业务对象插入文件增发申请文件份数
	 * @param bo 文件增发申请文件份数新增业务对象
	 * @return
	 */
	Boolean insertByBo(ExtraApplyDocNumberBo bo);

	/**
	 * 根据编辑业务对象修改文件增发申请文件份数
	 * @param bo 文件增发申请文件份数编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ExtraApplyDocNumberBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
