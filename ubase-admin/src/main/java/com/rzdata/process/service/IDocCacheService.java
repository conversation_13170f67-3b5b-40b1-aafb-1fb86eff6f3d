package com.rzdata.process.service;


import com.rzdata.process.domain.bo.DocPreviewBo;

/**
 * 文件缓存接口
 * @author: wangyang
 * @date: 2022年1月21日18:11:47
 */
public interface IDocCacheService {

    /**
     * 将文档信息放入缓存
     * @param documentKey
     * @param doc
     * @return
     */
    boolean put(String documentKey, DocPreviewBo doc);

    /**
     * 根据 documentKey 从缓存中获取文档
     * @param documentKey
     * @return
     */
    DocPreviewBo get(String documentKey);

    /**
     * 删除缓存
     * @param documentKey
     */
    void remove(String documentKey);
}
