package com.rzdata.process.service;


import com.rzdata.process.domain.bo.DocPreviewBo;
import com.rzdata.process.domain.bo.DocPreviewEditBo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 文件预览服务
 * @author: wangyang
 * @date: 2019/8/7 16:30
 */
public interface IDocPreviewService {
    /**
     * 构建文档对象
     * @param filePath
     * @param fileName
     * @param mode 类型 view=预览 edit=编辑
     * @return documentKey 文档key
     */
    String buildDocument(String filePath, String fileName,String mode);

    /**
     * 从缓存中获取文档信息
     * @param documentKey
     * @return
     */
    DocPreviewBo getDocument(String documentKey);

    /**
     * 下载文档实体文件
     * @param documentKey
     * @param request
     * @param response
     * @throws IOException
     */
    void downloadDocumentFile(String documentKey, HttpServletRequest request, HttpServletResponse response) throws IOException;

    /**
     * 构建文档编辑参数 对象
     * @param userId
     * @param userName
     * @return
     */
    DocPreviewEditBo buildDocumentEditParam(String userId, String userName);

    /**
     * 编辑后保存文档实体文件
     * @param documentKey
     * @param downloadUrl
     * @throws IOException
     */
    boolean saveDocumentFile(String documentKey, String downloadUrl) throws IOException;

    /**
     * 获取服务暴露的host(包含端口)
     * @return
     */
    Object getServerHost();

    /**
     * 文档是否支持编辑
     * @param document
     * @return
     */
    boolean canEdit(DocPreviewBo document);
}
