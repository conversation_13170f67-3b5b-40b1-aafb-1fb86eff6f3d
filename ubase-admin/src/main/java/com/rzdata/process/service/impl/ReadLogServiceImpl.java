package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ReadLogBo;
import com.rzdata.process.domain.vo.ReadLogVo;
import com.rzdata.process.domain.ReadLog;
import com.rzdata.process.mapper.ReadLogMapper;
import com.rzdata.process.service.IReadLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件阅览记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
@Service
public class ReadLogServiceImpl extends ServicePlusImpl<ReadLogMapper, ReadLog, ReadLogVo> implements IReadLogService {

    @Override
    public ReadLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ReadLogVo> queryPageList(ReadLogBo bo) {
        PagePlus<ReadLog, ReadLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ReadLogVo> queryList(ReadLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ReadLog> buildQueryWrapper(ReadLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReadLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ReadLog::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ReadLog::getVersionValue, bo.getVersionValue());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ReadLog::getUserName, bo.getUserName());
        lqw.eq(bo.getDeptId() != null, ReadLog::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getDownLoad()), ReadLog::getDownLoad, bo.getDownLoad());
        lqw.eq(StringUtils.isNotBlank(bo.getPreview()), ReadLog::getPreview, bo.getPreview());
        lqw.eq(StringUtils.isNotBlank(bo.getExternalFileId()), ReadLog::getExternalFileId, bo.getExternalFileId());
        lqw.orderByDesc(ReadLog:: getCreateTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(ReadLogBo bo) {
        ReadLog add = BeanUtil.toBean(bo, ReadLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ReadLogBo bo) {
        ReadLog update = BeanUtil.toBean(bo, ReadLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReadLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<ReadLogVo> selectReadListTop5(ReadLogBo bo) {
        List<ReadLogVo> list = listVo(buildQueryWrapper(bo));
        return CollectionUtil.page(0, 5, list);
    }
}
