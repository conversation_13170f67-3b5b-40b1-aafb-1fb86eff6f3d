package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件分发记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
public interface IDocDistributeLogService extends IServicePlus<DocDistributeLog, DocDistributeLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocDistributeLogVo queryById(String id);

	/**
	 * 查询列表
	 */
	TableDataInfo<DocDistributeLogVo> queryPageList(DocDistributeLogBo bo);

	/**
	 * 签收查询列表
	 */
	TableDataInfo<DocDistributeLogVo> queryPageListForSign(DocDistributeLogBo bo);

	/**
	 * 查询列表
	 */
	TableDataInfo<DocDistributeLogVo> queryNoProcessList(DocDistributeLogBo bo);

	/**
	 * 查询列表
	 */
	List<DocDistributeLogVo> queryList(DocDistributeLogBo bo);

	/**
	 * 根据新增业务对象插入文件分发记录
	 * @param bo 文件分发记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocDistributeLogBo bo);

	/**
	 * 根据编辑业务对象修改文件分发记录
	 * @param bo 文件分发记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocDistributeLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 签收文件
	 * @param id
	 */
	void signeFile(String id);

	void updateByDocId(String docId);

	List<DistributeItemVo> queryDocDistributeLogVoByDisId(String id);

	List<DocDistributeLogVo> queryDocDistributeLogListVoByAppId(Map<String, Object> params);

	/**
	 *  查出文件分发到哪些部门
	 *  定时任务专用
	 * @return 部门Id列表
	 */
	List<String> selectDistributeDept(String docId);

	/**
	 * 通过文档id和文档版本号 查出改版本的所有的分发数据并统计分发数(相加)
	 * @param docId
	 * @param versionId
	 * @return
	 */
	List<DocDistributeLogVo> getDocDistributeNumsByDocId(String docId,String versionId);

	/**
	 * 本部门查询接口
	 * @param bo
	 * @return
	 */
	TableDataInfo<DocDistributeLogVo> queryPageByThisDept(DocDistributeLogBo bo);

	/**
	 * 外部门查询接口
	 * @param bo
	 * @return
	 */
	TableDataInfo<DocDistributeLogVo> queryPageByOtherDept(DocDistributeLogBo bo);


}
