package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo;
import com.rzdata.process.domain.vo.DocStatisticsChangeTypeVo;
import com.rzdata.process.domain.vo.DocStatisticsRecDisVo;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:27
 * @Version 1.0
 * @Description
 */
public interface IDocStatisticsService {

    /**
     * 获取变更类型统计
     * @param docStatisticsBo 文件统计实体类
     * @return
     */
    AjaxResult<List<DocStatisticsChangeTypeVo>> changeType(DocStatisticsBo docStatisticsBo);

    /**
     * 获取变更因素统计
     * @param docStatisticsBo 文件统计实体类
     * @return
     */
    AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(DocStatisticsBo docStatisticsBo);

    /**
     * 获取变更类型统计
     * @param bo 文件统计实体类
     * @return
     */
    AjaxResult<List<DocStatisticsRecDisVo>> recovery(StandardBo bo);

    AjaxResult<List<HashMap>> distribute(DocDisStatisticsBo bo);
}
