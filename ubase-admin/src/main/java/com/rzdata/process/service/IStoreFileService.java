package com.rzdata.process.service;

import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.UploadFileVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2022/1/4 上午11:15
 * @Version 1.0
 */
public interface IStoreFileService {

    /**
     * 上传附件
     * @param file 附件文件
     * @return
     */
    UploadFileVo uploadFile(MultipartFile file) throws IOException;

    /**
     * 获取存储地址
     * @return
     */
    String getStorePath();

    /**
     * 下载文件
     * @param fileVo 文件对象
     * @param response
     * @return
     */
    boolean downloadFile(BasicFileVo fileVo, HttpServletResponse response);

    /**
     * 文件读取统计
     * @param docIds
     * @throws Exception
     */
    void opstatiStics() throws Exception;
}
