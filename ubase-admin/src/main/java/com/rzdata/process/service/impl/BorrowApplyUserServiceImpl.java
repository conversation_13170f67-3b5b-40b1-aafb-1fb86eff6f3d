package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.BorrowApplyUserBo;
import com.rzdata.process.domain.vo.BorrowApplyUserVo;
import com.rzdata.process.domain.BorrowApplyUser;
import com.rzdata.process.mapper.BorrowApplyUserMapper;
import com.rzdata.process.service.IBorrowApplyUserService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件借阅选择借阅人Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class BorrowApplyUserServiceImpl extends ServicePlusImpl<BorrowApplyUserMapper, BorrowApplyUser, BorrowApplyUserVo> implements IBorrowApplyUserService {

    @Override
    public BorrowApplyUserVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BorrowApplyUserVo> queryPageList(BorrowApplyUserBo bo) {
        PagePlus<BorrowApplyUser, BorrowApplyUserVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BorrowApplyUserVo> queryList(BorrowApplyUserBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BorrowApplyUser> buildQueryWrapper(BorrowApplyUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BorrowApplyUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), BorrowApplyUser::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getBorrowUser()), BorrowApplyUser::getBorrowUser, bo.getBorrowUser());
        lqw.eq(bo.getIsDeleted() != null, BorrowApplyUser::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(BorrowApplyUserBo bo) {
        BorrowApplyUser add = BeanUtil.toBean(bo, BorrowApplyUser.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(BorrowApplyUserBo bo) {
        BorrowApplyUser update = BeanUtil.toBean(bo, BorrowApplyUser.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BorrowApplyUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
