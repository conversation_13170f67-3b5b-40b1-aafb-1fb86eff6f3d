package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysRole;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.IDocVersionFavoritesService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysRoleService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件版本记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class VersionServiceImpl extends ServicePlusImpl<VersionMapper, Version, VersionVo> implements IVersionService {

    @Autowired
    ISysDeptService iSysDeptService;

    @Resource
    ISysRoleService sysRoleService;

    @Resource
    ISysUserService sysUserService;

    @Autowired
    IDocVersionFavoritesService iDocVersionFavoritesService;

    @Override
    public VersionVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public Version queryOneByStandardId(String standardId) {
        return getOne(new LambdaQueryWrapper<Version>().eq(Version::getStandardId,standardId).eq(Version::getStatus, Constants.ONE));
    }

    @Override
    public Version queryByDocIdAndVersion(String docId, String versionValue) {
        return getOne(new LambdaQueryWrapper<Version>().eq(Version::getDocId,docId).eq(Version::getVersionValue,versionValue));
    }

    @Override
    @DataScope(deptAlias = "dl")
    public TableDataInfo<VersionVo> queryPageList(VersionBo bo) {
        bo.setDeptIds(SecurityUtils.getDeptId());
        Page<VersionVo> page = PageUtils.buildPage();
        List<SysRole> sysRoles = sysRoleService.queryRolesByUserId(SecurityUtils.getUserId());
        List<String> roleKeyList = sysRoles.stream().map(role -> role.getRoleKey()).collect(Collectors.toList());
        String roleKeys = String.join(",",roleKeyList);
        bo.setRoleKey(roleKeys);
        Page<VersionVo> iPage = this.baseMapper.selectVersionPage(page, bo);
        Date now = new Date();
        String deptId = SecurityUtils.getDeptId();
        //判断当前登录用户是不是公司文件管理员
        boolean isQa = sysUserService.isQa(SecurityUtils.getUsername());
        //不是公司文件管理员 则给字段赋值 查出这个哪些文件有权限查看
        if (!isQa) {
            iPage.getRecords().forEach(versionVo -> {
                int borrowCount = this.baseMapper.checkAuthByBorrow(versionVo.getDocId(), versionVo.getId(), now);
                boolean hasBorrowPerms = borrowCount != 0;
                if (hasBorrowPerms) {
                    versionVo.setHasPerms(true);
                    return;
                }
                int disCount = this.baseMapper.checkAuthByDis(versionVo.getDocId(), versionVo.getId(), deptId);
                versionVo.setHasPerms(disCount != 0);
                return;
            });
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<VersionVo> queryList(VersionBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Version> buildQueryWrapper(VersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Version> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), Version::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), Version::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), Version::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getStartDate() != null, Version::getStartDate, bo.getStartDate());
        lqw.eq(bo.getEndDate() != null, Version::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Version::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), Version::getReason, bo.getReason());
        return lqw;
    }

    @Override
    public Boolean insertByBo(VersionBo bo) {
        Version add = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(VersionBo bo) {
        Version update = BeanUtil.toBean(bo, Version.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Version entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<VersionVo> selectVersionListByAppId(String applyId) {
        return this.baseMapper.selectVersionListByAppId(applyId);
    }

    @Override
    public List<VersionVo> selectVersionList(List<String> applyId) {
        return this.baseMapper.selectVersionList(applyId);
    }

    @Override
    public List<VersionVo> selectVersionListByDocId(String docId) {
        return this.baseMapper.selectVersionListByDocId(docId);
    }

    @Override
    public VersionVo selectVersionByDocId(String docId){
        return this.baseMapper.selectVersionByDocId(docId);
    }

    @Override
    public List<VersionVo> selectVersionListByDocIdAndVersionId(String docId, String versionId) {
        return this.baseMapper.selectVersionListByDocIdAndVersionId(docId,versionId);
    }


    @Override
    public TableDataInfo<VersionVo> selectRecordFile(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFile(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setCompileDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
            vVo.setInFavorites(iDocVersionFavoritesService.inFavorites(vVo.getId()));
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectRecordFileCompany(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.selectRecordFileCompany(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
        }
        Date now = new Date();
        String deptId = SecurityUtils.getDeptId();
        boolean isQa = sysUserService.isQa(SecurityUtils.getUsername());
        //不是公司文件管理员 则给字段赋值 查出这个哪些文件有权限查看
        if (!isQa) {
            iPage.getRecords().forEach(versionVo -> {
                int borrowCount = this.baseMapper.checkAuthByBorrow(versionVo.getDocId(), versionVo.getId(), now);
                boolean hasBorrowPerms = borrowCount != 0;
                if (hasBorrowPerms) {
                    versionVo.setHasPerms(true);
                    return;
                }
                int disCount = this.baseMapper.checkAuthByDis(versionVo.getDocId(), versionVo.getId(), deptId);
                versionVo.setHasPerms(disCount != 0);
                return;
            });
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public TableDataInfo<VersionVo> selectDeptFile(VersionBo bo) {
        Page<VersionVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<VersionVo> iPage = this.baseMapper.selectDeptFile(page, bo);
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
        }
        return PageUtils.buildDataInfo(iPage);
    }
}
