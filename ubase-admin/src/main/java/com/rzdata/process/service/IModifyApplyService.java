package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.domain.vo.ModifyApplyResultVo;
import com.rzdata.process.domain.vo.ModifyApplyVo;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.bo.CreateNewNoBo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 文件变更操作申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IModifyApplyService extends IServicePlus<ModifyApply, ModifyApplyVo> {
	/**
	 * 生效分发
	 * @param id
	 * @return
	 */
	boolean distribute(String id);

	Long getDocExpiration(String docClass);
	/**
	 * 查询单个
	 * @return
	 */
	ModifyApplyVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ModifyApplyVo> queryPageList(ModifyApplyBo bo);

	/**
	 * 查询列表
	 */
	List<ModifyApplyVo> queryList(ModifyApplyBo bo);

	/**
	 * 根据新增业务对象插入文件变更操作申请
	 * @param bo 文件变更操作申请新增业务对象
	 * @return
	 */
	Optional<ModifyApplyResultVo> insertByBo(ModifyApplyBo bo);

	/**
	 * 插入实体，不提交流程
	 * @param bo
	 */
	ModifyApplyResultVo insertBoNoBpm(ModifyApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件变更操作申请
	 * @param bo 文件变更操作申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ModifyApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 根据新增业务对象插入文件变更操作申请的所有数据，包括附件，关联文件，新增或关联记录文件
	 * @param bo 文件变更操作申请新增业务对象
	 * @return 主键
	 */
	Optional<String> insertAllByBo(ModifyApplyBo bo);

	DocNoVo getDocNoByApplyId(String applyId);

	List<DocNoVo> getRecordDocNoByLinkId(Map<String,String> requestMap);

	/**
	 * 判断历史文件编号是否存在
	 * @param docId
	 * @return
	 */
	boolean checkDocIdExist(String docId);
	/**
	 * 更新文件编号流水号
	 * @param bo
	 * @return
	 * @throws Exception
	 */
	Boolean updateDocSerialNumber(CreateNewNoBo bo) throws Exception;

	/**
	 * 更新记录文件流水号
	 * @param listBo
	 * @return
	 * @throws Exception
	 */
	Boolean updateRecordSerialNumber(List<CreateNewNoBo> listBo) throws Exception;
}
