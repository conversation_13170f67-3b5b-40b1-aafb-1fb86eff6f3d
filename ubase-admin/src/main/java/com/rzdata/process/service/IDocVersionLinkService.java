package com.rzdata.process.service;

import com.rzdata.process.domain.DocVersionLink;
import com.rzdata.process.domain.vo.DocVersionLinkVo;
import com.rzdata.process.domain.bo.DocVersionLinkBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 版本-文件关联记录-关联Service接口
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
public interface IDocVersionLinkService extends IServicePlus<DocVersionLink, DocVersionLinkVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocVersionLinkVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocVersionLinkVo> queryPageList(DocVersionLinkBo bo);

	/**
	 * 查询列表
	 */
	List<DocVersionLinkVo> queryList(DocVersionLinkBo bo);

	/**
	 * 根据新增业务对象插入版本-文件关联记录-关联
	 * @param bo 版本-文件关联记录-关联新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocVersionLinkBo bo);

	/**
	 * 根据编辑业务对象修改版本-文件关联记录-关联
	 * @param bo 版本-文件关联记录-关联编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocVersionLinkBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
