package com.rzdata.process.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.asas7.model.OpstatiStics;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.service.ConfigService;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.domain.ReadLog;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.vo.DocInfoVo;
import com.rzdata.process.domain.vo.FileLocalStoreConfig;
import com.rzdata.process.domain.vo.UploadFileVo;
import com.rzdata.process.mapper.BasicFileMapper;
import com.rzdata.process.mapper.ReadLogMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.process.service.IStoreFileService;
import com.rzdata.process.utils.FileStoreUtils;
import com.rzdata.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/1/4 上午11:16
 * @Version 1.0
 * @Desc 本地文件上传
 */
@Slf4j
@Service
public class LocalStoreFileServiceImpl implements IStoreFileService {

    @Autowired
    private ConfigService configService;

    @Autowired
    FileUploadDownloadService fileUploadService;

    @Autowired
    BasicFileMapper basicFileMapper;

    @Autowired
    ReadLogMapper readLogMapper;

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    StandardMapper standardMapper;

    @Autowired
    private IBasicFileService basicFileService;

    @Override
    public UploadFileVo uploadFile(MultipartFile file) throws IOException {
        String localFilePath = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH);
        String localFilePrefix = configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_URL_PREFIX);
        UploadFileVo fileVo = FileStoreUtils.upload(localFilePath, file);
        fileVo.setUrl(localFilePrefix + fileVo.getUrl());
        return fileVo;
    }

    @Override
    public String getStorePath() {
        return configService.getConfigValue(FileLocalStoreConfig.KEY_LOCAL_STORE_PATH);
    }

    @Override
    public boolean downloadFile(BasicFileVo fileVo, HttpServletResponse response) {
        if (ObjectUtils.isEmpty(fileVo)) {
            log.error("download file error: fileId not exists");
            return false;
        }
        String absoluteFilePath = getStorePath() + File.separator + fileVo.getFilePath();
        File file = new File(absoluteFilePath);
        if (file.exists()) {
            response.setContentType("application/octet-stream");
            response.setHeader("content-type", "application/octet-stream");
            String fileName = fileVo.getFileName();
            try {
                fileName = URLEncoder.encode(fileVo.getFileName(), "utf8");
            } catch (Exception e) {
            }
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            byte[] buffer = new byte[1024];
            //输出流
            OutputStream os = null;
            try (FileInputStream fis = new FileInputStream(file);
                 BufferedInputStream bis = new BufferedInputStream(fis);) {
                os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer);
                    i = bis.read(buffer);
                }
                return true;
            } catch (Exception e) {
                log.error("download file error:", e);
            }
        } else {
            log.error("download file path is empty");
        }
        return false;
    }

    @Override
    public void opstatiStics() throws Exception {
        List<BasicFile> fileList = basicFileMapper.selectBasicFileList();
        if (ObjectUtil.isNotEmpty(fileList)) {
            List<String> docIds = fileList.stream().map(x -> x.getExternalFileId()).distinct().collect(Collectors.toList());
            Map<String, BasicFile> map = fileList.stream().collect(Collectors.toMap(x -> x.getExternalFileId(), x -> x, (next, last) -> next));
//            docIds.add("gns://5EF26633E8E740D3AFBB14022CC575CA/6311065E719D46A79EC7AE5EDE7567B9");
//            docIds.add("gns://5EF26633E8E740D3AFBB14022CC575CA/D52315D16C5E4AE28C967A1B7FD5B65A");
            List<OpstatiStics> list = fileUploadService.fileOpstatiStics(docIds);
            if (ObjectUtil.isNotEmpty(list)) {
                for (OpstatiStics opstatiStics : list) {
                    ReadLog readLog = new ReadLog();
                    readLog.setCreateTime(new Date());
                    SysUser user = iSysUserService.selectUserByUserName(SecurityUtils.getUsername());
                    readLog.setDeptId(SecurityUtils.getDeptId());
                    readLog.setDeptName(user.getDept().getDeptName());
                    readLog.setUserName(SecurityUtils.getUsername());
                    readLog.setNickName(user.getNickName());
                    if (ObjectUtil.isNotEmpty(map.get(opstatiStics.getDocId()))) {
                        readLog.setFileName(map.get(opstatiStics.getDocId()).getFileName());
                        DocInfoVo docInfoVo = standardMapper.selectDocInfoByFileId(map.get(opstatiStics.getDocId()).getId());
                        if (ObjectUtil.isNotEmpty(docInfoVo)) {
                            readLog.setDocId(docInfoVo.getDocId());
                            readLog.setDocName(docInfoVo.getDocName());
                        }
//                        readLog.setDeptId();
//                        readLog.setDeptName();
//                        readLog.setUserName();
//                        readLog.setNickName();
                    }
                    readLog.setExternalFileId(opstatiStics.getDocId());
                    readLog.setDownLoad(opstatiStics.getDownLoad());
                    readLog.setPreview(opstatiStics.getPreview());
                    readLogMapper.insert(readLog);
                }
            }
        }
    }
}
