package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.DocVersionLinkBo;
import com.rzdata.process.domain.vo.DocVersionLinkVo;
import com.rzdata.process.domain.DocVersionLink;
import com.rzdata.process.mapper.DocVersionLinkMapper;
import com.rzdata.process.service.IDocVersionLinkService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 版本-文件关联记录-关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-12
 */
@Service
public class DocVersionLinkServiceImpl extends ServicePlusImpl<DocVersionLinkMapper, DocVersionLink, DocVersionLinkVo> implements IDocVersionLinkService {

    @Override
    public DocVersionLinkVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocVersionLinkVo> queryPageList(DocVersionLinkBo bo) {
        PagePlus<DocVersionLink, DocVersionLinkVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocVersionLinkVo> queryList(DocVersionLinkBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocVersionLink> buildQueryWrapper(DocVersionLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocVersionLink> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getLinkId()), DocVersionLink::getLinkId, bo.getLinkId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocVersionLink::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocVersionLink::getTenantId, bo.getTenantId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocVersionLinkBo bo) {
        DocVersionLink add = BeanUtil.toBean(bo, DocVersionLink.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocVersionLinkBo bo) {
        DocVersionLink update = BeanUtil.toBean(bo, DocVersionLink.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocVersionLink entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
