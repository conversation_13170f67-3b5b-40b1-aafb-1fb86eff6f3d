package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ExtraApplyDocNumberBo;
import com.rzdata.process.domain.vo.ExtraApplyDocNumberVo;
import com.rzdata.process.domain.ExtraApplyDocNumber;
import com.rzdata.process.mapper.ExtraApplyDocNumberMapper;
import com.rzdata.process.service.IExtraApplyDocNumberService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件增发申请文件份数Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class ExtraApplyDocNumberServiceImpl extends ServicePlusImpl<ExtraApplyDocNumberMapper, ExtraApplyDocNumber, ExtraApplyDocNumberVo> implements IExtraApplyDocNumberService {

    @Override
    public ExtraApplyDocNumberVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ExtraApplyDocNumberVo> queryPageList(ExtraApplyDocNumberBo bo) {
        PagePlus<ExtraApplyDocNumber, ExtraApplyDocNumberVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ExtraApplyDocNumberVo> queryList(ExtraApplyDocNumberBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ExtraApplyDocNumber> buildQueryWrapper(ExtraApplyDocNumberBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ExtraApplyDocNumber> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ExtraApplyDocNumber::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ExtraApplyDocNumber::getDocId, bo.getDocId());
        lqw.eq(bo.getApplyNum() != null, ExtraApplyDocNumber::getApplyNum, bo.getApplyNum());
        lqw.eq(bo.getIsDeleted() != null, ExtraApplyDocNumber::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ExtraApplyDocNumberBo bo) {
        ExtraApplyDocNumber add = BeanUtil.toBean(bo, ExtraApplyDocNumber.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ExtraApplyDocNumberBo bo) {
        ExtraApplyDocNumber update = BeanUtil.toBean(bo, ExtraApplyDocNumber.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ExtraApplyDocNumber entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
