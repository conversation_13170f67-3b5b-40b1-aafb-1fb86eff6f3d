package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.PrintLog;
import com.rzdata.process.domain.PrintLogItem;
import com.rzdata.process.domain.bo.PrintLogItemBo;
import com.rzdata.process.domain.vo.PrintLogItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件打印记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
public interface IPrintLogItemService extends IServicePlus<PrintLogItem, PrintLogItemVo> {
	/**
	 * 查询单个
	 * @return
	 */
	PrintLogItemVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<PrintLogItem> queryPageList(PrintLogItemBo bo);

	/**
	 * 查询列表
	 */
	List<PrintLogItemVo> queryList(PrintLogItemBo bo);

	/**
	 * 根据新增业务对象插入文件打印记录
	 * @param bo 文件打印记录新增业务对象
	 * @return
	 */
	String insertByBo(PrintLogItemBo bo);

	/**
	 * 根据编辑业务对象修改文件打印记录
	 * @param bo 文件打印记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(PrintLogItemBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	PrintLog selectPrintByDistId(String id);

	/**
	 * 通过id 修改文件为已打印状态
	 * @param ids
	 * @return
	 */
	AjaxResult updatePrintStatusById(String[] ids);

	/**
	 * 文件重打
	 * @param ids
	 * @return
	 */
	AjaxResult reprint(String[]ids);
}
