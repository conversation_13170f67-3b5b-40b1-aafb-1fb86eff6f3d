package com.rzdata.process.service.impl;

import com.alibaba.fastjson.JSON;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.process.domain.bo.DocPreviewBo;
import com.rzdata.process.service.IDocCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 基于Redis的文档缓存业务实现
 * <p>成功配置了Redis时，注入该Bean</p>
 *
 * TODO 超时时间是否考虑？
 *
 * @author: wangyang
 * @date: 2022年1月21日18:12:31
 */
@Slf4j
@Service
@ConditionalOnProperty(prefix = "document.cache.redis", name = "enabled", matchIfMissing = true)
public class RedisDocCacheServiceImpl implements IDocCacheService {
    @Resource
    private RedisTemplate<Object, Object> redisTemplate;

    @Resource(name="redisTemplate")
    private ValueOperations<Object, Object> valOps;

    @Override
    public boolean put(String documentKey, DocPreviewBo doc) {
        if (doc == null) {
            return false;
        }
        valOps.setIfAbsent(cacheKey(documentKey), JSON.toJSONString(doc), DocConstants.CACHE_DURATION);
        //hashOps.put(DocumentConstants.DOCUMENT_REDIS_KEY_PREFIX_FORMAT, documentKey, JSON.toJSONString(doc));
        return true;
    }

    @Override
    public DocPreviewBo get(String documentKey) {
        /*return JSON.parseObject((String) hashOps.get(DocumentConstants.DOCUMENT_REDIS_KEY_PREFIX_FORMAT, documentKey), Document.class);*/
        return JSON.parseObject((String) valOps.get(cacheKey(documentKey)), DocPreviewBo.class);
    }

    @Override
    public void remove(String documentKey) {
        /*hashOps.delete(DocumentConstants.DOCUMENT_REDIS_KEY_PREFIX_FORMAT, documentKey);*/
        redisTemplate.delete(cacheKey(documentKey));
    }

    private String cacheKey(String documentKey) {
        return String.format(DocConstants.DOCUMENT_REDIS_KEY_PREFIX_FORMAT, documentKey);
    }
}
