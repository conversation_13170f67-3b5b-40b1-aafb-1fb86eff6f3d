package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ModifyApplyDistributeBo;
import com.rzdata.process.domain.vo.ModifyApplyDistributeVo;
import com.rzdata.process.domain.ModifyApplyDistribute;
import com.rzdata.process.mapper.ModifyApplyDistributeMapper;
import com.rzdata.process.service.IModifyApplyDistributeService;

import java.util.List;
import java.util.Collection;
import java.util.Map;

/**
 * 文件变更操作申请分发Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Service
public class ModifyApplyDistributeServiceImpl extends ServicePlusImpl<ModifyApplyDistributeMapper, ModifyApplyDistribute, ModifyApplyDistributeVo> implements IModifyApplyDistributeService {

    @Override
    public ModifyApplyDistributeVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ModifyApplyDistributeVo> queryPageList(ModifyApplyDistributeBo bo) {
        PagePlus<ModifyApplyDistribute, ModifyApplyDistributeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ModifyApplyDistributeVo> queryList(ModifyApplyDistributeBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ModifyApplyDistribute> buildQueryWrapper(ModifyApplyDistributeBo bo) {
        LambdaQueryWrapper<ModifyApplyDistribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ModifyApplyDistribute::getApplyId, bo.getApplyId());
        lqw.eq(bo.getDeptId() != null, ModifyApplyDistribute::getDeptId, bo.getDeptId());
        lqw.eq(bo.getNums() != null, ModifyApplyDistribute::getNums, bo.getNums());
        lqw.eq(bo.getIsDeleted() != null, ModifyApplyDistribute::getIsDeleted, bo.getIsDeleted());
        lqw.groupBy(ModifyApplyDistribute:: getDeptId);
        return lqw;
    }

    @Override
    public Boolean insertByBo(ModifyApplyDistributeBo bo) {
        ModifyApplyDistribute add = BeanUtil.toBean(bo, ModifyApplyDistribute.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ModifyApplyDistributeBo bo) {
        ModifyApplyDistribute update = BeanUtil.toBean(bo, ModifyApplyDistribute.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApplyDistribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public Integer queryNumsByMap(Map<String, Object> params) {
        return this.baseMapper.queryNumsByMap(params);
    }

    @Override
    public List<ModifyApplyDistributeVo> queryListByChangeIdApplyId(ModifyApplyDistributeBo distributeBo) {
        return this.baseMapper.queryListByChangeIdApplyId(distributeBo);
    }
}
