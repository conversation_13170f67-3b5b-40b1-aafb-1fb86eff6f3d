package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.QysCallbackBo;

/**
 * <AUTHOR>
 * @Date 2022/1/26 17:33
 * @Version 1.0
 * @Description
 */
public interface IDocSignatureService {

    /**
     * 文件签章
     * @param id 文档主键id
     * @return 签章地址
     */
    AjaxResult signature(String id,String docId,String applyId);


    /**
     * 文件签章
     * @param qysCallback 契约锁回调实体类
     * @return 成功/失败
     */
    AjaxResult callBack(QysCallbackBo qysCallback);
}
