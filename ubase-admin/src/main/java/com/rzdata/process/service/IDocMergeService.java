package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.bo.DocMergeBo;
import com.rzdata.process.domain.vo.BasicFileVo;

/**
 * <AUTHOR>
 * @Date 2022/1/19 10:54
 * @Version 1.0
 * @Description 文件合并服务接口
 */
public interface IDocMergeService{

    /**
     * 文件合稿
     * @param docMergeBo 文件合并实体类
     * @return
     */
    AjaxResult merge(DocMergeBo docMergeBo);

    AjaxResult mergeForRecord(DocMergeBo bo);
}
