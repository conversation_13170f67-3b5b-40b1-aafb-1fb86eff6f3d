package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ExtraApplyBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.ExtraApplyVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.mapper.ExtraApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件增发申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class ExtraApplyServiceImpl extends ServicePlusImpl<ExtraApplyMapper, ExtraApply, ExtraApplyVo> implements IExtraApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    IExtraApplyDocNumberService iExtraApplyDocNumberService;

    @Autowired
    ICodeRuleService iCodeRuleService;

    @Autowired
    IDocDistributeLogService docDistributeLogService;

    @Autowired
    IDistributeItemService distributeItemService;
    @Autowired
    private  IGenerateIdService iGenerateIdService;
    @Autowired
    ISysRoleService sysRoleService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    IStandardService standardService;

    @Autowired
    IVersionService versionService;

    @Autowired
    IDocMessageService docMessageService;
    @Override
    public ExtraApplyVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ExtraApplyVo> queryPageList(ExtraApplyBo bo) {
        PagePlus<ExtraApply, ExtraApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ExtraApplyVo> queryList(ExtraApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ExtraApply> buildQueryWrapper(ExtraApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ExtraApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ExtraApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(bo.getDeptId() != null, ExtraApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ExtraApply::getUserName, bo.getUserName());
        lqw.eq(bo.getApplyTime() != null, ExtraApply::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), ExtraApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ExtraApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProcessInstanceModel insertByBo(ExtraApplyBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ExtraApply add = BeanUtil.toBean(bo, ExtraApply.class);
        bo.setChangeType(ApplyTypeEnum.EXTRA.toString());
        add.setId(iGenerateIdService.generateApplyId(ApplyTypeEnum.EXTRA,loginUser.getUsername()));
        add.setApplyType(ApplyTypeEnum.EXTRA);
        add.setApplyTime(new Date());
        add.setStatus(NumberConstants.ONE+"");
        add.setDeptId(loginUser.getDeptId());
        add.setUserName(loginUser.getUsername());
        add.setNickName(loginUser.getNickName());
        boolean flag = save(add);
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            // 保存关系数据
            saveApplyDocNumber(bo);
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().getModel().setWf_sendUserId(loginUser.getUsername());
                bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(loginUser.getDeptId().toString());
                processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(),add.getId());
                saveWorkFlowLog(bo, processInstanceModel);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    /**
     * 保存申请记录
     * @param bo
     * @param processInstanceModel
     */
    private void saveWorkFlowLog(ExtraApplyBo bo, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLogBo applyLogBo = new WorkflowApplyLogBo();
        applyLogBo.setId(bo.getId());
        applyLogBo.setApplyClass(ApplyTypeEnum.EXTRA.name());

        //选择了多个文件的话 doc_workflow_apply表的docId和docName只存第一个文件的 然后如果在我的待办等地方点详情 再返回具体的多条文件数据
        applyLogBo.setDocId(bo.getApplyDocNumberList().get(0).getDocId());
        applyLogBo.setDocName(bo.getApplyDocNumberList().get(0).getDocName());
        applyLogBo.setDocClass(bo.getApplyDocNumberList().get(0).getDocClass());
        if (ObjectUtil.isNotEmpty(bo.getApplyDocNumberList())) {
            String docIds = String.join(",", bo.getApplyDocNumberList().stream().map(x -> x.getDocId()).collect(Collectors.toList()));
            applyLogBo.setDocIds(docIds);
        }
        applyLogBo.setProcDefKey(processInstanceModel.getProcDefId());
        applyLogBo.setProcInstId(processInstanceModel.getProcInstId());
        applyLogBo.setApplyStatus(ApplyStatusEnum.PROCESSING.getCode());
        applyLogBo.setProcStatus(ProcessStatusConstants.TO_DO);
        iWorkflowApplyLogService.insertByBo(applyLogBo);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key -> key.equals(processConfig.getProcDefKeyZFSQ())).isPresent()) {
            return;
        }
        ExtraApply extraApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(extraApply)) {
            return;
        }
        extraApply.setStatus(event.getStatus());
        extraApply.setApplyStatus(event.getApplyStatus());
        extraApply.setUpdateTime(new Date());
        this.baseMapper.updateById(extraApply);
        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            actDefName = "结束";
            QueryWrapper<ExtraApplyDocNumber> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ExtraApplyDocNumber:: getApplyId, extraApply.getId());
            queryWrapper.lambda().eq(ExtraApplyDocNumber:: getIsDeleted, NumberConstants.ZERO);
            List<ExtraApplyDocNumber> docNumberList = iExtraApplyDocNumberService.list(queryWrapper);
            if(ObjectUtil.isNotEmpty(docNumberList)) {
                for (ExtraApplyDocNumber extraApplyDocNumber : docNumberList) {
                    DocDistributeLog distributeLog = new DocDistributeLog();
                    distributeLog.setId(null);
                    distributeLog.setApplyId(extraApply.getId());
                    distributeLog.setNotPrint(extraApplyDocNumber.getNotPrint());
                    distributeLog.setDeptId(extraApplyDocNumber.getDeptId());
                    distributeLog.setDeptName(sysDeptService.getById(extraApplyDocNumber.getDeptId()).getDeptName());
                    distributeLog.setDbStatus(NumberConstants.ZERO);
                    distributeLog.setDistributeTime(new Date());
                    distributeLog.setCreateTime(new Date());
                    distributeLog.setDocId(extraApplyDocNumber.getDocId());
                    //Version version = versionService.getById(extraApplyDocNumber.getVersionId());
                    StandardVo doc = standardService.getStandardVoByVersionId(extraApplyDocNumber.getVersionId());
                    if (ObjectUtil.isNotEmpty(doc)){
                        distributeLog.setDocName(doc.getDocName());
                        distributeLog.setDocClass(doc.getDocClass());
                        distributeLog.setCompileDeptId(doc.getDeptId());
                        distributeLog.setCompileDeptName(sysDeptService.getById(doc.getDeptId()).getDeptName());
                    }
                    distributeLog.setNums(extraApplyDocNumber.getApplyNum());
                    distributeLog.setVersionId(extraApplyDocNumber.getVersionId());
                    distributeLog.setVersionValue(extraApplyDocNumber.getVersionValue());
                    distributeLog.setDistributeUserNickName(SecurityUtils.getNickname());
                    distributeLog.setPreviousVersionValue(extraApplyDocNumber.getVersionValue());
                    distributeLog.setChangeType(ApplyTypeEnum.EXTRA);
                    //生成新的签收记录 不需要在这里写增加分发打印数据的代码 因为在签收的方法中已经做了处理(数量增加)iDocDistributeLogService.signeFile(id);
                    docDistributeLogService.save(distributeLog);


                    //分发详情表也需要增加数据
                    //DistributeItemBo item = new DistributeItemBo();
                    //item.setId();
                    //item.setApplyId(extraApplyDocNumber.getApplyId());
                    //不知道为什么要用applyId 做DistributeId的值 但是不敢改
                    //item.setDistributeId(extraApply.getId());
                    //item.setDocId(extraApplyDocNumber.getDocId());
                    //item.setVersionId(extraApplyDocNumber.getVersionId());
                    //item.setDistributeTime(new Date());
                    //item.setPrintTimes(0L);
                    //item.setDeptId(extraApplyDocNumber.getDeptId());
                    //申请人作为创建人
                    //item.setCreateBy(extraApply.getUserName());
                    //item.setCreateTime(new Date());
                    //生成分发记录
                    //distributeItemService.insertByBo(item);
                    docMessageService.insertMessageByDistribute(distributeLog);
                }
            }
            docMessageService.insertMessage(event, MsgTypeEnum.EXTRA);
        }
        iWorkflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, actDefName,event.getApplyStatus());
    }



    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存文件申请数量
     * @param bo
     */
    private void saveApplyDocNumber(ExtraApplyBo bo) {
        QueryWrapper<ExtraApplyDocNumber> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExtraApplyDocNumber:: getApplyId, bo.getId());
        queryWrapper.lambda().eq(ExtraApplyDocNumber:: getIsDeleted, NumberConstants.ZERO);
        iExtraApplyDocNumberService.remove(queryWrapper);
        if (ObjectUtil.isNotEmpty(bo.getApplyDocNumberList())) {
            for (ExtraApplyDocNumber extraApplyDocNumber : bo.getApplyDocNumberList()) {
                extraApplyDocNumber.setId(null);
                extraApplyDocNumber.setCreateTime(new Date());
                //extraApplyDocNumber.setDeptId(extraApplyDocNumber.getDeptId());
                extraApplyDocNumber.setApplyId(bo.getId());
                iExtraApplyDocNumberService.save(extraApplyDocNumber);
            }
        }
    }

    @Override
    public Boolean updateByBo(ExtraApplyBo bo) {
        ExtraApply update = BeanUtil.toBean(bo, ExtraApply.class);
        bo.setChangeType(ApplyTypeEnum.EXTRA.toString());
        validEntityBeforeSave(update);
        saveApplyDocNumber(bo);
        // 开启流程 调用工作流相关接口
        try {
            bo.getBpmClientInputModel().getModel().setWf_sendUserId(update.getUserName());
            bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(update.getDeptId().toString());
            ProcessInstanceModel processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(),update.getId());
            saveWorkFlowLog(bo, processInstanceModel);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ExtraApply entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
