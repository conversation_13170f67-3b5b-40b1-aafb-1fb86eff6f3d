package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.PrintLog;
import com.rzdata.process.domain.bo.PrintLogBo;
import com.rzdata.process.domain.vo.PrintLogVo;
import com.rzdata.process.mapper.PrintLogMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.IDistributeItemService;
import com.rzdata.process.service.IPrintLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件打印记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Service
public class PrintLogServiceImpl extends ServicePlusImpl<PrintLogMapper, PrintLog, PrintLogVo> implements IPrintLogService {

    @Autowired
    IDistributeItemService iDistributeItemService;

    @Autowired
    VersionMapper versionMapper;

    @Override
    public PrintLogVo queryById(String id){
        PrintLogVo vo = this.baseMapper.selectDetailById(id);
        /*if (ObjectUtil.isNotEmpty(vo)) {
            QueryWrapper<DistributeItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(DistributeItem:: getDistributeId, id);
            vo.setDistributeItemList(iDistributeItemService.list(queryWrapper));
        }*/
        return vo;
    }

    @Override
    public TableDataInfo<PrintLogVo> queryPageList(PrintLogBo bo) {
//        PagePlus<PrintLog, PrintLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        Page<PrintLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<PrintLogVo> iPage = this.baseMapper.queryPageList(page, bo);
        //获取详情
        iPage.getRecords().forEach(vo->{
            vo.setVersionVo(versionMapper.selectDetailById(vo.getVersionId()));
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<PrintLogVo> queryList(PrintLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<PrintLog> buildQueryWrapper(PrintLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrintLog> lqw = Wrappers.lambdaQuery();
        //lqw.eq(StringUtils.isNotBlank(bo.getDistributeId()), PrintLog::getDistributeId, bo.getDistributeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), PrintLog::getDocId, bo.getDocId());
        //lqw.like(StringUtils.isNotBlank(bo.getPrintUserName()), PrintLog::getPrintUserName, bo.getPrintUserName());
        //lqw.eq(bo.getPrintTime() != null, PrintLog::getPrintTime, bo.getPrintTime());
        return lqw;
    }

    @Override
    public String insertByBo(PrintLogBo bo) {
        PrintLog add = BeanUtil.toBean(bo, PrintLog.class);
        validEntityBeforeSave(add);
        //add.setStatus(NumberConstants.ZERO);
        add.setDeptId(SecurityUtils.getDeptId()+"");
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return add.getId();
    }

    @Override
    public Boolean updateByBo(PrintLogBo bo) {
        PrintLog update = BeanUtil.toBean(bo, PrintLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(PrintLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

   /* @Override
    public PrintLog selectPrintByDistId(String id){
        QueryWrapper<PrintLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PrintLog:: getDistributeId, id);
        return this.baseMapper.selectOne(queryWrapper);
    }*/
}
