package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.process.domain.ReissueApply;
import com.rzdata.process.domain.bo.DistributeItemBo;
import com.rzdata.process.domain.bo.ReprintLogBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import com.rzdata.process.mapper.DistributeItemMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.system.service.ISysRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 文件分发记录条目Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
@Slf4j
@Service
public class DistributeItemServiceImpl extends ServicePlusImpl<DistributeItemMapper, DistributeItem, DistributeItemVo> implements IDistributeItemService {


    @Autowired
    ICodeRuleService iCodeRuleService;

    @Autowired
    IPrintLogService iPrintLogService;

    @Autowired
    IReprintLogService reprintLogService;

    @Autowired
    IRecoveryLogService iRecoveryLogService;
    @Autowired
    private  IGenerateIdService iGenerateIdService;
    @Autowired
    IDocDistributeLogService iDocDistributeLogService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    ISysRoleService iSysRoleService;

    @Autowired
    IReissueApplyService reissueApplyService;

    @Autowired
    IVersionService versionService;

    @Override
    public DistributeItemVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DistributeItemVo> queryPageList(DistributeItemBo bo) {
        Page<DistributeItemVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        bo.setDeptId(SecurityUtils.getDeptId());
//        if (!iSysRoleService.checkRoleByUserIdAndKey(SecurityUtils.getUserId(), RoleConstants.WJGLY)) {
//           bo.setDeptId(SecurityUtils.getDeptId());
//        }
        Page<DistributeItemVo> iPage = this.baseMapper.selectDistributeItemPage(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    @DataScope(deptAlias = "d")
    public TableDataInfo<DistributeItemVo> listByAppid(DistributeItemBo bo) {
        Page<DistributeItemVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());

        Page<DistributeItemVo> iPage = this.baseMapper.listByAppid(page, bo);
        LambdaQueryWrapper<ReissueApply> query = new LambdaQueryWrapper<>();
//        iPage.getRecords().forEach(distributeItemVo -> {
//            query.eq(ReissueApply::getDistributeIds,distributeItemVo.getId());
//            query.orderByDesc(ReissueApply::getCreateTime);
//            List<ReissueApplyVo> list = reissueApplyService.listVo(query);
//            distributeItemVo.setReissueApplyList(list);
//        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    //@DataScope(deptAlias = "d")
    public TableDataInfo<DistributeItemVo> listByDisId(DistributeItemBo bo) {
        //Page<DistributeItemVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        //PagePlus<DistributeItemVo,DistributeItem> page = new PagePlus<>(bo.getPageNum(), bo.getPageSize());
        //Page<DistributeItemVo> iPage = this.baseMapper.listByDisId(page, bo);
        LambdaQueryWrapper <DistributeItem>query = Wrappers.lambdaQuery();
        query.eq(DistributeItem::getDistributeId,bo.getDistributeId());
        PagePlus<DistributeItem, DistributeItemVo> result = pageVo(PageUtils.buildPagePlus(), query);
//        LambdaQueryWrapper<ReissueApply> query = new LambdaQueryWrapper<>();
//        iPage.getRecords().forEach(distributeItemVo -> {
//            query.eq(ReissueApply::getDistributeIds,distributeItemVo.getId());
//            query.orderByDesc(ReissueApply::getCreateTime);
//            List<ReissueApplyVo> list = reissueApplyService.listVo(query);
//            distributeItemVo.setReissueApplyList(list);
//        });
//        PageUtils.buildDataInfo(result).getRows().forEach(vo->{
//            vo.setVersionVo(versionService.getVoById(vo.getVersionId()));
//        });
        return PageUtils.buildDataInfo(result);
    }


    @Override
    @DataScope(deptAlias = "d")
    public TableDataInfo<DistributeItemVo> listRecoveryByAppid(DistributeItemBo bo) {
        Page<DistributeItemVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());

        Page<DistributeItemVo> iPage = this.baseMapper.listRecoveryByAppid(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }



    @Override
    public List<DistributeItemVo> queryList(DistributeItemBo bo) {
        return this.baseMapper.selectDistributeItemList(bo);
    }

    private LambdaQueryWrapper<DistributeItem> buildQueryWrapper(DistributeItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DistributeItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), DistributeItem::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getDistributeId()), DistributeItem::getDistributeId, bo.getDistributeId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DistributeItem::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DistributeItem::getVersionId, bo.getVersionId());
        lqw.eq(bo.getDeptId() != null, DistributeItem::getDeptId, bo.getDeptId());
        lqw.eq(bo.getDistributeTime() != null, DistributeItem::getDistributeTime, bo.getDistributeTime());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveUserName()), DistributeItem::getReceiveUserName, bo.getReceiveUserName());
        lqw.eq(bo.getReceiveTime() != null, DistributeItem::getReceiveTime, bo.getReceiveTime());
        //lqw.like(StringUtils.isNotBlank(bo.getPrintUserName()), DistributeItem::getPrintUserName, bo.getPrintUserName());
        //lqw.eq(bo.getPrintTime() != null, DistributeItem::getPrintTime, bo.getPrintTime());
        //lqw.eq(bo.getPrintTimes() != null, DistributeItem::getPrintTimes, bo.getPrintTimes());
        //lqw.like(StringUtils.isNotBlank(bo.getRecoveryUserName()), DistributeItem::getRecoveryUserName, bo.getRecoveryUserName());
        //lqw.eq(bo.getRecoveryTime() != null, DistributeItem::getRecoveryTime, bo.getRecoveryTime());
        //lqw.eq(StringUtils.isNotBlank(bo.getRecoveryType()), DistributeItem::getRecoveryType, bo.getRecoveryType());
        //lqw.eq(bo.getIsDeleted() != null, DistributeItem::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DistributeItemBo bo, String docClass) {
        try{
            DistributeItem add = BeanUtil.toBean(bo, DistributeItem.class);
            add.setId(iGenerateIdService.generateDistributeId(add,docClass));
            add.setCreateTime(new Date());
            if (bo.getDeptId()==null){
                add.setDeptId(SecurityUtils.getDeptId());
            }
            add.setReceiveTime(new Date());
            add.setReceiveUserName(SecurityUtils.getUsername());
            add.setReceiveNickName(SecurityUtils.getNickname());
            //add.setDistributeTime(new Date());
            //add.setIsDeleted(NumberConstants.ZERO);
            validEntityBeforeSave(add);
            boolean flag = save(add);
            if (flag) {
                bo.setId(add.getId());
            }
            return flag;
        }
        catch(Exception ex){
            log.error("insertByBo===",ex);
            return false;
        }


    }

    @Override
    public Boolean updateByBo(DistributeItemBo bo) {
        DistributeItem update = BeanUtil.toBean(bo, DistributeItem.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DistributeItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 更新数据
     *
     * @param ids  id集合
     * @param type 1: 打印，2：回收
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByIds(List<String> ids, Integer type, String status, String id) {
        if (ObjectUtil.isNotEmpty(ids)) {
            List<DistributeItem> list = this.baseMapper.selectBatchIds(ids);
            //为2表示回收
            if (Objects.equals(status, "2")) {
                //生成文件失效消息
                docMessageService.insertMessageByInvalidation(list);
            }
            if (ObjectUtil.isNotEmpty(list)) {
                LoginUser loginUser = SecurityUtils.getLoginUser();
                //Long count = this.baseMapper.selectPrintNumsById(list.get(0).getDistributeId());
                for (DistributeItem distributeItem : list) {
                    distributeItem.setUpdateTime(new Date());
                   // Long num = ObjectUtil.isNotEmpty(distributeItem.getPrintTimes()) ? distributeItem.getPrintTimes() + 1 : 1;
                    if (Objects.equals(type, NumberConstants.ONE)) {
                        DocDistributeLog distributeLog = iDocDistributeLogService.getById(distributeItem.getDistributeId());
                        if (ObjectUtil.isNotEmpty(distributeLog) /*&& Objects.equals(distributeLog.getPrintStatus(), NumberConstants.ZERO)*/) {
//                            if (count >= distributeLog.getNums()) {
//
//                            }
                            //distributeLog.setPrintStatus(NumberConstants.ONE);
                            iDocDistributeLogService.updateById(distributeLog);
                        }
                        //distributeItem.setPrintTime(new Date());
                        //distributeItem.setPrintUserName(loginUser.getUsername());
                        //distributeItem.setPrintTimes(num);
                        //PrintLog printLog = new PrintLog();
                        //printLog.setDistributeId(distributeItem.getDistributeId());
                        //printLog.setDocId(distributeItem.getDocId());
                        //printLog.setDeptId(loginUser.getDeptId() + "");
                        //printLog.setPrintTime(new Date());
                        //printLog.setPrintUserName(loginUser.getUsername());
                        //printLog.setStatus(NumberConstants.ONE);
                        //iPrintLogService.save(printLog);
                        //给重打表也生成一条记录
                        ReprintLogBo reprintLogBo = new ReprintLogBo();
                        reprintLogBo.setDisItemId(distributeItem.getId());
                        reprintLogService.insertByBo(reprintLogBo);
                    } else {
                        //distributeItem.setRecoveryTime(new Date());
                        //distributeItem.setRecoveryType(status);
                        //distributeItem.setRecoveryUserName(loginUser.getUsername());
                        QueryWrapper<DistributeItem> queryWrapper = new QueryWrapper<>();
                        queryWrapper.lambda().eq(DistributeItem:: getDocId, distributeItem.getDocId());
                        //List<DistributeItem> list1 = this.baseMapper.selectList(queryWrapper);

                        RecoveryLog recoveryLog = iRecoveryLogService.selectRecoveryByApplyIdAndDeptId(distributeItem.getApplyId(),distributeItem.getDeptId());
                        if (ObjectUtil.isNotEmpty(recoveryLog)) {
                            recoveryLog.setRecyclNum(recoveryLog.getRecyclNum()+1);
                            recoveryLog.setUpdateTime(new Date());
                            if (recoveryLog.getRecyclNum()>= recoveryLog.getNoRecyclNum()) {
                                //recoveryLog.setStatus(NumberConstants.ONE + "");
                            }
                            iRecoveryLogService.updateById(recoveryLog);
                        }
                    }
                    this.baseMapper.updateById(distributeItem);
                }
            }
        }
    }
}
