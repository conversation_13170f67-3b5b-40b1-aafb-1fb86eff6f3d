package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DocConstants;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.file.FileUtils;
import com.rzdata.process.service.IStoreFileService;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.BasicFileBo;
import com.rzdata.process.domain.vo.BasicFileVo;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.mapper.BasicFileMapper;
import com.rzdata.process.service.IBasicFileService;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class BasicFileServiceImpl extends ServicePlusImpl<BasicFileMapper, BasicFile, BasicFileVo> implements IBasicFileService {

    @Autowired
    private IStoreFileService iStoreFileService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private FileUploadDownloadService fileUploadService;

    @Override
    public Map<String, BasicFileVo> queryByIdList(List<String> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return new HashMap<>();
        }
        return Optional.ofNullable(this.listVoByIds(fileIds)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(BasicFileVo::getId, Function.identity()));
    }

    @Override
    public BasicFileVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BasicFileVo> queryPageList(BasicFileBo bo) {
        PagePlus<BasicFile, BasicFileVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<BasicFileVo> queryList(BasicFileBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BasicFile> buildQueryWrapper(BasicFileBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BasicFile> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), BasicFile::getFileName, bo.getFileName());
        lqw.eq(bo.getFileSize() != null, BasicFile::getFileSize, bo.getFileSize());
        lqw.eq(StringUtils.isNotBlank(bo.getFileType()), BasicFile::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getFileClass()), BasicFile::getFileClass, bo.getFileClass());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), BasicFile::getBusinessId, bo.getBusinessId());
        lqw.eq(StringUtils.isNotBlank(bo.getFilePath()), BasicFile::getFilePath, bo.getFilePath());
        lqw.eq(StringUtils.isNotBlank(bo.getExternalFileId()), BasicFile::getExternalFileId, bo.getExternalFileId());
        return lqw;
    }

    @Override
    public String insertByBo(BasicFileBo bo) {
        BasicFile add = BeanUtil.toBean(bo, BasicFile.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return bo.getId();
    }

    @Override
    public Map<String, String> uploading(MultipartFile file) throws Exception {
        String storeRootPath = iStoreFileService.getStorePath();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String str = sdf.format(new Date());
        String filename = file.getOriginalFilename();
        if(StringUtils.contains(filename,Constants.SLASH)) {
            filename = filename.substring(filename.indexOf(Constants.SLASH) + 1);
        }
        String tempPath = str + File.separator + filename;
        String filepath = storeRootPath + File.separator + str + File.separator;
        File targetFile = new File(filepath);
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        try (FileOutputStream out = new FileOutputStream(filepath + filename);) {
            out.write(file.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
        }
//        fileUploadService.fileSearch();
        BasicFileBo bo = new BasicFileBo();
        Boolean asPlatform = Boolean.valueOf(configService.selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            // 调用爱数上传附件接口
            FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(filepath + File.separator + filename);
            bo.setExternalFilePath(fileOsbeginuploadRes.getName());
            bo.setExternalFileId(fileOsbeginuploadRes.getDocid());
            bo.setExternalRev(fileOsbeginuploadRes.getRev());
        }
        bo.setFileSize(file.getSize());
        bo.setFileType(filename.substring(filename.lastIndexOf(".")+1));
        bo.setFileName(filename);
        bo.setFilePath(tempPath);
        bo.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        bo.setCreateTime(new Date());
        bo.setMessageDigest(FileUtils.extractChecksum(file.getBytes()));

        LambdaQueryWrapper<BasicFile> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(BasicFile::getMessageDigest,bo.getMessageDigest());

        String id = insertByBo(bo);

        Map<String, String> map = new HashMap<>(3);
        map.put(DocConstants.KEY_FILE_ID, id);
        map.put(DocConstants.KEY_PATH, tempPath);
        map.put(DocConstants.KEY_FILE_NAME, filename);
        return map;
    }


    @Override
    public Boolean updateByBo(BasicFileBo bo) {
        BasicFile update = BeanUtil.toBean(bo, BasicFile.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BasicFile entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
