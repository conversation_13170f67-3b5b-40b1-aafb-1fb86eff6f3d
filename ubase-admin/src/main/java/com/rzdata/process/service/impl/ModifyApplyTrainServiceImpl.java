package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.ModifyApplyTrain;
import com.rzdata.process.domain.bo.ModifyApplyTrainBo;
import com.rzdata.process.domain.vo.ModifyApplyTrainVo;
import com.rzdata.process.mapper.ModifyApplyTrainMapper;
import com.rzdata.process.service.IModifyApplyTrainService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文件变更操作申请培训记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-08
 */
@Service
public class ModifyApplyTrainServiceImpl extends ServicePlusImpl<ModifyApplyTrainMapper, ModifyApplyTrain, ModifyApplyTrainVo> implements IModifyApplyTrainService {

    @Override
    public ModifyApplyTrainVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ModifyApplyTrainVo> queryPageList(ModifyApplyTrainBo bo) {
        PagePlus<ModifyApplyTrain, ModifyApplyTrainVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ModifyApplyTrainVo> queryList(ModifyApplyTrainBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ModifyApplyTrain> buildQueryWrapper(ModifyApplyTrainBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ModifyApplyTrain> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ModifyApplyTrain::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ModifyApplyTrain::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ModifyApplyTrain::getDocId, bo.getDocId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ModifyApplyTrainBo bo) {
        ModifyApplyTrain add = BeanUtil.toBean(bo, ModifyApplyTrain.class);
        add.setUserName(bo.getUserName());
        add.setDeptId(bo.getDeptId());
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ModifyApplyTrainBo bo) {
        ModifyApplyTrain update = BeanUtil.toBean(bo, ModifyApplyTrain.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApplyTrain entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
