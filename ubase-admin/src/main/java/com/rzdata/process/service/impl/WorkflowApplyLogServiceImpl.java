package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.*;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程申请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-05
 */
@Service
public class WorkflowApplyLogServiceImpl extends ServicePlusImpl<WorkflowApplyLogMapper, WorkflowApplyLog, WorkflowApplyLogVo> implements IWorkflowApplyLogService {

    @Autowired
    IStandardService iStandardService;

    @Autowired
    IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    IVersionService iVersionService;

    @Autowired
    ReviewApplyMapper reviewApplyMapper;

    @Autowired
    ExtraApplyMapper extraApplyMapper;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    BorrowApplyMapper borrowApplyMapper;

    @Autowired
    IReissueApplyService reissueApplyService;

    @Autowired
    IBasicFileService iBasicFileService;

    @Autowired
    IExtraApplyDocNumberService iExtraApplyDocNumberService;

    @Autowired
    IModifyApplyService iModifyApplyService;

    @Autowired
    IDocChangeApplyService iDocChangeApplyService;

    @Autowired
    BasicFileMapper basicFileMapper;

    @Autowired
    IDocDistributeLogService iDocDistributeLogService;

    @Autowired
    private IWorkflowLogService iWorkflowLogService;

    @Autowired
    private IReviewApplyItemService applyItemService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Override
    public WorkflowApplyLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<WorkflowApplyLogVo> queryPageList(WorkflowApplyLogBo bo) {
        bo.setSender(SecurityUtils.getUsername());
        Page<WorkflowApplyLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        // 不开启分页自动优化
        page.setOptimizeCountSql(false);
        Page<WorkflowApplyLogVo> iPage = this.baseMapper.selectWorkflowApplyLogPage(page, bo);
//        List<WorkflowApplyLogVo> records = iPage.getRecords();
//        List<WorkflowApplyLogVo> newList = new ArrayList<>();
//        if(ObjectUtil.isNotEmpty(records)){
//            Map<String, List<WorkflowApplyLogVo>> map = records.stream().collect(Collectors.groupingBy(WorkflowApplyLogVo::getId));
//             for (Map.Entry<String,List<WorkflowApplyLogVo>> entry : map.entrySet()){
//                 List<WorkflowApplyLogVo> vos = entry.getValue();
//                 List<WorkflowApplyLogVo> sizeList = new ArrayList<>();
//                 if(vos.size()>1){
//                     for (WorkflowApplyLogVo vo : vos) {
//                         if(StringUtils.isNotBlank(bo.getDocName()) && StringUtils.isNotBlank(bo.getDocClass())){
//                             if(StringUtils.equals(bo.getDocClass(), vo.getDocClass()) && StringUtils.equals(bo.getDocName(), vo.getDocName())){
//                                 vo.setId(entry.getKey());
//                                 sizeList.add(vo);
//                                 break;
//                             }
//                         }else if (StringUtils.isNotBlank(bo.getDocClass()) && StringUtils.equals(bo.getDocClass(), vo.getDocClass())) {
//                            vo.setId(entry.getKey());
//                             sizeList.add(vo);
//                            break;
//                         }else if (StringUtils.isNotBlank(bo.getDocName()) && StringUtils.equals(bo.getDocName(), vo.getDocName())) {
//                             vo.setId(entry.getKey());
//                             sizeList.add(vo);
//                             break;
//                         }
//                     }
//                     if(ObjectUtil.isNotEmpty(sizeList)){
//                         newList.addAll(sizeList);
//                     }
//                     List<String> docNames = vos.stream().map(WorkflowApplyLogVo::getDocName).collect(Collectors.toList());
//                     vos.get(0).setDocName(StringUtils.join(docNames,","));
//                 }
//                 newList.add(vos.get(0));
//             }
//            return PageUtils.buildDataInfo(newList);
//        }
//        else {
        if(ObjectUtil.isNotEmpty(iPage.getRecords())) {
            for (WorkflowApplyLogVo record : iPage.getRecords()) {
                // 为复审流程时，获取复审文件清单，任取一个返回
                if (ApplyTypeEnum.REVIEW.toString().equals(record.getApplyClass())) {
                    ReviewApplyItemBo bo1 = new ReviewApplyItemBo();
                    bo1.setIds(Collections.singletonList(record.getId()));
                    if(StringUtils.isNotBlank(bo.getDocClass())){
                        bo1.setDocClass(bo.getDocClass());
                    }
                    if(StringUtils.isNotBlank(bo.getDocName())){
                        bo1.setDocName(bo.getDocName());
                    }
                    List<ReviewApplyItemVo> itemVos = applyItemService.queryItemList(bo1);
                    if (!CollectionUtils.isEmpty(itemVos)) {
                        for (ReviewApplyItemVo item : itemVos) {
//                            ReviewApplyItemVo item = itemVos.get(0);
                            record.setDocId(item.getDocId());
//                    record.setDocName(item.getDocName());
                            record.setVersionId(item.getVersionId());
                            record.setVersionValue(item.getVersionValue());
                            record.setDocClass(item.getDocClass());
                            if (StringUtils.equals(bo.getDocClass(), item.getDocClass())) {
                                record.setDocClass(item.getDocClass());
                                record.setDocId(item.getDocId());
                                record.setVersionId(item.getVersionId());
                                record.setVersionValue(item.getVersionValue());
                                break;
                            }else if (StringUtils.isNotBlank(bo.getDocClass()) && StringUtils.isNotBlank(bo.getDocName()) && StringUtils.containsIgnoreCase(item.getDocName(),bo.getDocName()) && StringUtils.equals(bo.getDocClass(), item.getDocClass())) {
                                record.setDocId(item.getDocId());
                                record.setDocClass(item.getDocClass());
                                record.setVersionId(item.getVersionId());
                                record.setVersionValue(item.getVersionValue());
                                break;
                            }else if (StringUtils.isNotBlank(bo.getDocName()) && StringUtils.containsIgnoreCase(item.getDocName(),bo.getDocName())) {
                                record.setDocId(item.getDocId());
                                record.setDocClass(item.getDocClass());
                                record.setVersionId(item.getVersionId());
                                record.setVersionValue(item.getVersionValue());
                                break;
                            }

                        }
                    }
                    if(StringUtils.isNotBlank(bo.getDocClass())){
                        itemVos = itemVos.stream().filter(i->StringUtils.equals(bo.getDocClass(),i.getDocClass())).collect(Collectors.toList());
                    }
                    List<String> docNames = itemVos.stream().map(ReviewApplyItemVo::getDocName).collect(Collectors.toList());
                    record.setDocName(StringUtils.join(docNames,","));
                    if(StringUtils.isNotBlank(bo.getDocName())) {
                        docNames = docNames.stream().filter(i->StringUtils.containsIgnoreCase(i,bo.getDocName())).collect(Collectors.toList());
                        record.setDocName(StringUtils.join(docNames, ","));
                    }
                }
            }
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<WorkflowApplyLogVo> queryList(WorkflowApplyLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    @Override
    public Map<String, Object> selectDetail(String type, String busId) {
        Map<String, Object> result = new HashMap<>();
        if (Objects.equals(type, ApplyTypeEnum.CHANGE.name())) {
            DocChangeApplyDetailVo docChangeApply = iDocChangeApplyService.queryById(busId);
            if(ObjectUtil.isNotEmpty(docChangeApply)){
//                result.put("file", basicFileMapper.selectFileByDocId(docChangeApply.getDocId()));
                //查询备注附件
                List<ModifyApplyLinkVo> remarks = modifyApplyLinkService.queryDocByApplyIdAndType(docChangeApply.getId(), LinkTypeEnum.APPENDIX_REMARK);
                docChangeApply.setRemarkFiles(remarks);
               /* Standard standard = iStandardService.getById(docChangeApply.getDocId());
                if (standard!=null){
                    BasicFile basicFile = basicFileMapper.selectById(standard.getFileId());
                    if (basicFile!=null){
                        //被逼无奈 没时间改了 把docstandard对象的文档名改成文件名吧
                        standard.setDocName(basicFile.getFileName());
                    }
                }
                result.put("file",standard);*/
                result.put("result", docChangeApply);
            }
        } else if (Objects.equals(type, ApplyTypeEnum.REVIEW.name())) {
            ReviewApplyVo reviewApplyVo = reviewApplyMapper.selectDetailById(busId);

            result.put("result", reviewApplyVo);
            if (ObjectUtil.isNotEmpty(reviewApplyVo)) {
                ReviewApplyItemBo bo = new ReviewApplyItemBo();
                bo.setIds(Collections.singletonList(reviewApplyVo.getId()));
                List<ReviewApplyItemVo> applyItemVos = applyItemService.queryItemList(bo);
                reviewApplyVo.setApplyItemList(applyItemVos);
                QueryWrapper<DocDistributeLog> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(DocDistributeLog:: getDocId, reviewApplyVo.getDocId());
                queryWrapper.lambda().eq(DocDistributeLog:: getDbStatus, NumberConstants.ZERO);
                List<DocDistributeLog> list = iDocDistributeLogService.list(queryWrapper);
                if (ObjectUtil.isNotEmpty(list)) {
                    result.put("deptIds", list.stream().map(x -> x.getDeptId()).distinct().collect(Collectors.toList()));
                }
            }
        } else if (Objects.equals(type, ApplyTypeEnum.REISSUE.name())) {
            ReissueApplyVo reissueApply = reissueApplyService.getVoById(busId);
            VersionVo vo = iVersionService.getVoById(reissueApply.getVersionId());
            if (vo!=null){
                reissueApply.setFileId(vo.getFileId());
                reissueApply.setMergeFileId(vo.getMergeFileId());
                reissueApply.setEncryptFileId(vo.getEncryptFileId());
                reissueApply.setDeptName(sysDeptService.getById(reissueApply.getDeptId()).getDeptName());
            }
            result.put("result", reissueApply);
            if (ObjectUtil.isNotEmpty(reissueApply)) {
                Version version = iVersionService.getById(reissueApply.getVersionId());
                //Date date = this.baseMapper.selectEffectDate(reissueApply.getDocId());
                if (ObjectUtil.isNotEmpty(version)) {
                    result.put("effectDate",version.getStartDate());
                }
            }
        } else if (Objects.equals(type, ApplyTypeEnum.EXTRA.name())) {
            ExtraApplyVo extraApply = extraApplyMapper.selectDetail(busId);
            result.put("result", extraApply);
            if (ObjectUtil.isNotEmpty(extraApply)) {
                result.put("extraList", this.baseMapper.selectExtraList(extraApply.getId()));
            }
        } else if (Objects.equals(type, ApplyTypeEnum.BORROW.name())) {
            BorrowApply borrowApply = borrowApplyMapper.selectById(busId);

            result.put("result", borrowApply);
            if (ObjectUtil.isNotEmpty(borrowApply)) {
                result.put("borrowUser", this.baseMapper.selectBorrowUser(borrowApply.getId()));
                result.put("docInfos", this.baseMapper.getDocInfoByApplyId(borrowApply.getId()));
                //Date date = this.baseMapper.selectEffectDate(borrowApply.getDocIds());
                //if (ObjectUtil.isNotEmpty(date)) {
                //    result.put("effectDate", DateUtil.formatDate(date));
                //}
            }
        } else if(Objects.equals(type, ApplyTypeEnum.ADD.name())
                || Objects.equals(type, ApplyTypeEnum.UPDATE.name())
                ||Objects.equals(type, ApplyTypeEnum.DISUSE.name())){
            result.put("result",  iModifyApplyService.queryById(busId));
        }
        return result;
    }

    private LambdaQueryWrapper<WorkflowApplyLog> buildQueryWrapper(WorkflowApplyLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkflowApplyLog> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getApplyClass()), WorkflowApplyLog::getApplyClass, bo.getApplyClass());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyStatus()), WorkflowApplyLog::getApplyStatus, bo.getApplyStatus());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), WorkflowApplyLog::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), WorkflowApplyLog::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), WorkflowApplyLog::getDocClass, bo.getDocClass());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), WorkflowApplyLog::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), WorkflowApplyLog::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getDeptId() != null, WorkflowApplyLog::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcInstId()), WorkflowApplyLog::getProcInstId, bo.getProcInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getProcDefKey()), WorkflowApplyLog::getProcDefKey, bo.getProcDefKey());
        lqw.eq(bo.getApplyTime() != null, WorkflowApplyLog::getApplyTime, bo.getApplyTime());
        lqw.eq(WorkflowApplyLog::getSender, SecurityUtils.getUsername());
        lqw.orderByDesc(WorkflowApplyLog:: getApplyTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(WorkflowApplyLogBo bo) {
        WorkflowApplyLog add = BeanUtil.toBean(bo, WorkflowApplyLog.class);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        add.setApplyTime(new Date());
//        add.setApplyStatus(NumberConstants.ZERO+"");
        add.setSender(loginUser.getUsername());
        add.setDeptId(loginUser.getDeptId());
        if (ObjectUtil.isEmpty(add.getDocId())) {
            add.setDocId(bo.getDocIds());
        }
        if (ObjectUtil.isNotEmpty(bo.getDocId()) || ObjectUtil.isNotEmpty(bo.getDocIds())) {
            QueryWrapper<Standard> standardQueryWrapper = new QueryWrapper<>();
            if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                standardQueryWrapper.lambda().eq(Standard:: getId, bo.getDocId());
            }
            if (ObjectUtil.isNotEmpty(bo.getDocIds())) {
                standardQueryWrapper.lambda().in(Standard:: getId, bo.getDocIds().split(","));
            }
            standardQueryWrapper.lambda().eq(Standard:: getStatus, NumberConstants.ONE);
            List<Standard> standardList = iStandardService.list(standardQueryWrapper);
            if (ObjectUtil.isNotEmpty(standardList)) {
                String docClass = String.join(",", standardList.stream().map(x -> x.getDocClass()).distinct().collect(Collectors.toList()));
                String docName = String.join(",", standardList.stream().map(x -> x.getDocName()).distinct().collect(Collectors.toList()));
                add.setDocClass(docClass);
                add.setDocName(docName);
            }
            QueryWrapper<Version> versionQueryWrapper = new QueryWrapper<>();
            if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                versionQueryWrapper.lambda().eq(Version:: getDocId, bo.getDocId());
            }
            if (ObjectUtil.isNotEmpty(bo.getDocIds())) {
                versionQueryWrapper.lambda().in(Version:: getDocId, bo.getDocIds().split(","));
            }
            versionQueryWrapper.lambda().eq(Version:: getStatus, NumberConstants.ONE);
            List<Version> versionList = iVersionService.list(versionQueryWrapper);
            if (ObjectUtil.isNotEmpty(versionList)) {
                String versionId = String.join(",", versionList.stream().map(x -> x.getId()).distinct().collect(Collectors.toList()));
                String versionValue = String.join(",", versionList.stream().map(x -> x.getVersionValue()).distinct().collect(Collectors.toList()));
                add.setVersionId(versionId);
                add.setVersionValue(versionValue);
            }
        }
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(WorkflowApplyLogBo bo) {
        WorkflowApplyLog update = BeanUtil.toBean(bo, WorkflowApplyLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WorkflowApplyLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public void updateStatusByBusId(String busId, String status, String name,String applyStatus) {
        WorkflowApplyLog workflowApplyLog = this.baseMapper.selectById(busId);
        if (ObjectUtil.isNotEmpty(workflowApplyLog)) {
//            workflowApplyLog.setApplyStatus(status);
            if(StringUtils.isNotBlank(applyStatus)){
                workflowApplyLog.setApplyStatus(applyStatus);
            }
            workflowApplyLog.setProcStatus(status);
            workflowApplyLog.setActDefName(name);
            workflowApplyLog.setUpdateTime(new Date());
            this.baseMapper.updateById(workflowApplyLog);
        }
    }

    @Override
    public AjaxResult<Integer> selectStatusByDocId(String docId,String versionId) {
        QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WorkflowApplyLog:: getDocId, docId);
        queryWrapper.lambda().eq(WorkflowApplyLog:: getVersionId, versionId);
        queryWrapper.lambda().ne(WorkflowApplyLog:: getProcStatus, ProcessStatusConstants.TO_DONE);
        Long count = this.baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            return AjaxResult.success("该文件已在流程中",1);
        } else {
            //修订的时候查询其他部门是否已经打印
            //为0表示所有部门都已经打印了
            Long allPrintCount = this.baseMapper.isAllPrint(docId,versionId);
            if(allPrintCount==0) {
                return AjaxResult.success("可以通过", 0);
            }else {
                //否则说明有部门没有打印
                return AjaxResult.success("该文件有部门没有打印", 2);
            }
        }
    }

    @Override
    public String queryProcessStatus(String docId) {
        return this.baseMapper.queryProcessStatus(docId);
    }
}
