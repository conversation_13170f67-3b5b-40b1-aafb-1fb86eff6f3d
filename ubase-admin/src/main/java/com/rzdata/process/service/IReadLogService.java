package com.rzdata.process.service;

import com.rzdata.process.domain.ReadLog;
import com.rzdata.process.domain.vo.ReadLogVo;
import com.rzdata.process.domain.bo.ReadLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件阅览记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-25
 */
public interface IReadLogService extends IServicePlus<ReadLog, ReadLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ReadLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ReadLogVo> queryPageList(ReadLogBo bo);

	/**
	 * 查询列表
	 */
	List<ReadLogVo> queryList(ReadLogBo bo);

	/**
	 * 根据新增业务对象插入文件阅览记录
	 * @param bo 文件阅览记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(ReadLogBo bo);

	/**
	 * 根据编辑业务对象修改文件阅览记录
	 * @param bo 文件阅览记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ReadLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<ReadLogVo> selectReadListTop5(ReadLogBo bo);
}
