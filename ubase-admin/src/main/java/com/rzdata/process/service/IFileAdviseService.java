package com.rzdata.process.service;

import com.rzdata.process.domain.FileAdvise;
import com.rzdata.process.domain.vo.FileAdviseVo;
import com.rzdata.process.domain.bo.FileAdviseBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件建议Service接口
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
public interface IFileAdviseService extends IServicePlus<FileAdvise, FileAdviseVo> {
	/**
	 * 查询单个
	 * @return
	 */
	FileAdviseVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<FileAdviseVo> queryPageList(FileAdviseBo bo);

	/**
	 * 查询列表
	 */
	List<FileAdviseVo> queryList(FileAdviseBo bo);

	/**
	 * 根据新增业务对象插入文件建议
	 * @param bo 文件建议新增业务对象
	 * @return
	 */
	Boolean insertByBo(FileAdviseBo bo);

	/**
	 * 根据编辑业务对象修改文件建议
	 * @param bo 文件建议编辑业务对象
	 * @return
	 */
	Boolean updateByBo(FileAdviseBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
