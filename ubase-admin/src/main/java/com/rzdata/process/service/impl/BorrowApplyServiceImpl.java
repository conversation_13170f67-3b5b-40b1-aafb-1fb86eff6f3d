package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BorrowApply;
import com.rzdata.process.domain.BorrowApplyUser;
import com.rzdata.process.domain.bo.BorrowApplyBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.mapper.BorrowApplyMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件借阅申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class BorrowApplyServiceImpl extends ServicePlusImpl<BorrowApplyMapper, BorrowApply, BorrowApplyVo> implements IBorrowApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    IBorrowApplyUserService iBorrowApplyUserService;
    @Autowired
    private  IGenerateIdService iGenerateIdService;

    @Autowired
    StandardMapper standardMapper;

    @Autowired
    ISysDeptService sysDeptService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IVersionService versionService;

    @Override
    public BorrowApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<BorrowApplyVo> queryPageList(BorrowApplyBo bo) {
        PagePlus<BorrowApply, BorrowApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public TableDataInfo<BorrowApplyVo> queryPageListNew(BorrowApplyBo bo) {
        bo.setBorrowUser(SecurityUtils.getUsername());
        Page<BorrowApplyVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<BorrowApplyVo> iPage = this.baseMapper.selectBorrowApplyPage(page, bo);
        //LambdaQueryWrapper<Version> queryWrapper = new LambdaQueryWrapper();

        /*if (ObjectUtil.isNotEmpty(iPage) && ObjectUtil.isNotEmpty(iPage.getRecords())) {
            Long total = iPage.getRecords().stream().count();
            List<String> ids = iPage.getRecords().stream().map(x -> x.getDocIds()).collect(Collectors.toList());
            List<String> docIds = new ArrayList<>();
            for (String id : ids) {
                docIds.addAll(Arrays.asList(id.split(",")));
            }
            docIds = docIds.stream().distinct().collect(Collectors.toList());
            List<DocInfoVo> docInfoVoList = standardMapper.selectDocInfo(docIds);
            Map<String, DocInfoVo> map = docInfoVoList.stream().collect(Collectors.toMap(x -> x.getDocId(), x -> x, (next, last) -> next));
            for (BorrowApplyVo record : iPage.getRecords()) {
                queryWrapper.eq(Version::getDocId,record.getDocIds());
                //1表示有效 一个文档只有一个生效版本
                queryWrapper.eq(Version::getStatus,NumberConstants.ONE);
                VersionVo voOne = versionService.getVoOne(queryWrapper);
                if (voOne!=null){
                    record.setFileId(voOne.getFileId());
                    record.setMergeFileId(voOne.getMergeFileId());
                    record.setEncryptFileId(voOne.getEncryptFileId());
                }
                List<String> docName = new ArrayList<>();
                List<String> docClass = new ArrayList<>();
                List<String> docV = new ArrayList<>();
                List<String> dept = new ArrayList<>();
                List<String> type = new ArrayList<>();
                List<FileVo> fileVoList = new ArrayList<>();
                for (String s : record.getDocIds().split(",")) {
                    if (ObjectUtil.isNotEmpty(map.get(s))) {
                        docName.add(map.get(s).getDocName());
                        docClass.add(map.get(s).getDocClass());
                        docV.add(map.get(s).getVersionValue());
                        dept.add(map.get(s).getDeptName());
                        type.add(map.get(s).getTypeName());
                        FileVo fileVo = BeanUtil.toBean(map.get(s), FileVo.class);
                        fileVoList.add(fileVo);
                    }
                }
                record.setFileVoList(fileVoList);
                record.setDocNames(String.join(",", docName.stream().distinct().collect(Collectors.toList())));
                record.setDocClass(String.join(",", docClass.stream().distinct().collect(Collectors.toList())));
                record.setDocVersion(String.join(",", docV.stream().distinct().collect(Collectors.toList())));
                record.setMakeDeptName(String.join(",", dept.stream().distinct().collect(Collectors.toList())));
                record.setTypeName(String.join(",", type.stream().distinct().collect(Collectors.toList())));
                if (ObjectUtil.isNotEmpty(record.getStartTime()) && ObjectUtil.isNotEmpty(record.getEndTime())) {
                    record.setBorrowTime(DateUtil.between(record.getEndTime(), record.getStartTime(), DateUnit.MINUTE));
                }
            }
            //人工条件查询 筛选分页数据
            List<BorrowApplyVo> list = iPage.getRecords();
            if (ObjectUtil.isNotEmpty(bo.getDocNames())) {
                list = list.stream().filter(x -> x.getDocNames().contains(bo.getDocNames())).collect(Collectors.toList());
            }
            if (ObjectUtil.isNotEmpty(bo.getDeptId())) {
                list = list.stream().filter(x -> x.getDeptId().equals(bo.getDeptId())).collect(Collectors.toList());
            }
            if (ObjectUtil.isNotEmpty(bo.getDocClass())) {
                list = list.stream().filter(x -> x.getDocClass().equals(bo.getDocClass())).collect(Collectors.toList());
            }
            iPage.setTotal(list.size());
            iPage.setRecords(list);
        }*/
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<BorrowApplyVo> queryList(BorrowApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<BorrowApply> buildQueryWrapper(BorrowApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BorrowApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), BorrowApply::getApplyTitle, bo.getApplyTitle());
        //lqw.eq(StringUtils.isNotBlank(bo.getDocIds()), BorrowApply::getDocIds, bo.getDocIds());
        lqw.eq(bo.getDeptId() != null, BorrowApply::getDeptId, bo.getDeptId());
        lqw.eq(bo.getStartTime() != null, BorrowApply::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, BorrowApply::getEndTime, bo.getEndTime());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), BorrowApply::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyReason()), BorrowApply::getApplyReason, bo.getApplyReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), BorrowApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Override
    public ProcessInstanceModel insertByBo(BorrowApplyBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        BorrowApply add = BeanUtil.toBean(bo, BorrowApply.class);
        bo.setChangeType(ApplyTypeEnum.BORROW.toString());
        String id = iGenerateIdService.generateApplyId(ApplyTypeEnum.BORROW,loginUser.getUsername());
        add.setId(id);
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) + bo.getDayTime());
        //创建时间和开始时间都是当前时间
        add.setCreateTime(now);
        add.setStartTime(now);
        //结束时间为当前时间+DayTime后
        add.setEndTime(calendar.getTime());
        add.setDeptId(loginUser.getDeptId());
        add.setStatus(ProcessStatusConstants.TO_DO);
        add.setUserName(loginUser.getUsername());
        add.setApplyTitle("借阅申请");
        boolean flag = save(add);
        ProcessInstanceModel processInstanceModel = null;
        if (flag) {
            bo.setId(add.getId());
            saveBorrowUser(bo,add.getId());
            // 开启流程 调用工作流相关接口
            try {
                bo.getBpmClientInputModel().getModel().setWf_sendUserId(loginUser.getUsername());
                bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(loginUser.getDeptId().toString());
                processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(),add.getId());
                saveWorkFlowLog(bo, processInstanceModel);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return processInstanceModel;
    }

    private void saveBorrowUser(BorrowApplyBo bo,String applyId) {
        List<String> borrowUserList = bo.getBorrowUsers();
        if (ObjectUtil.isNotEmpty(borrowUserList)) {
            //先删再操作 ?为啥要这样操作? 这样不会导致主键一直在变吗 后期要对借阅详情操作的话 都会有问题 主键一直在变
            iBorrowApplyUserService.remove(new LambdaQueryWrapper<BorrowApplyUser>().eq(BorrowApplyUser::getApplyId,applyId));
            for (String borrowUser : borrowUserList) {
                bo.getDocInfos().forEach(doc->{
                    BorrowApplyUser borrowApplyUser = new BorrowApplyUser();
                    //借阅人
                    borrowApplyUser.setBorrowUser(borrowUser);
                    //关联的业务id
                    borrowApplyUser.setApplyId(applyId);
                    //文档编号
                    borrowApplyUser.setDocId(doc.getDocId());
                    //文档名称
                    borrowApplyUser.setDocName(doc.getDocName());
                    //文档类型
                    borrowApplyUser.setDocClass(doc.getDocClass());
                    //版本
                    borrowApplyUser.setVersionValue(doc.getVersionValue());
                    //版本号
                    borrowApplyUser.setVersionId(doc.getId());
                    String deptId = standardMapper.selectById(doc.getStandardId()).getDeptId();
                    borrowApplyUser.setDeptId(deptId);
                    borrowApplyUser.setDeptName(sysDeptService.getById(deptId).getDeptName());
                    //主键设置为null  自动生成
                    borrowApplyUser.setId(null);
                    //创建时间是当前时间
                    borrowApplyUser.setCreateTime(new Date());
                    iBorrowApplyUserService.save(borrowApplyUser);
                });
            }
        }
    }
    /**
     * 保存申请记录
     * @param bo
     * @param processInstanceModel
     */
    private void saveWorkFlowLog(BorrowApplyBo bo, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLogBo applyLogBo = new WorkflowApplyLogBo();
        applyLogBo.setId(bo.getId());
        applyLogBo.setApplyClass(ApplyTypeEnum.BORROW.name());
        //默认用第一个文件的数据
        applyLogBo.setDocClass(bo.getDocInfos().get(0).getDocClass());
        applyLogBo.setDocName(bo.getDocInfos().get(0).getDocName());
        applyLogBo.setDocId(bo.getDocInfos().get(0).getDocId());
        applyLogBo.setProcDefKey(processInstanceModel.getProcDefId());
        applyLogBo.setProcInstId(processInstanceModel.getProcInstId());
        applyLogBo.setApplyStatus(ApplyStatusEnum.PROCESSING.getCode());
        applyLogBo.setProcStatus(ProcessStatusConstants.TO_DO);
        iWorkflowApplyLogService.insertByBo(applyLogBo);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key -> key.equals(processConfig.getProcDefKeyJYSQ())).isPresent()) {
            return;
        }
        BorrowApply borrowApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(borrowApply)) {
            return;
        }
        borrowApply.setStatus(event.getStatus());
        borrowApply.setApplyStatus(event.getApplyStatus());
        borrowApply.setUpdateTime(new Date());
        this.baseMapper.updateById(borrowApply);
        String status = event.getStatus();
        if (Objects.equals(ProcessStatusConstants.TO_DONE , event.getStatus())) {
            //生成一条借阅消息
            docMessageService.insertMessageByBorrow(event, MsgTypeEnum.BORROW);

        }
        iWorkflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, event.getModel().getWf_nextActDefName(),event.getApplyStatus());

    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    @Override
    public Boolean updateByBo(BorrowApplyBo bo) {
        BorrowApply update = BeanUtil.toBean(bo, BorrowApply.class);
        bo.setChangeType(ApplyTypeEnum.BORROW.toString());
        validEntityBeforeSave(update);
        saveBorrowUser(bo,bo.getId());
        // 开启流程 调用工作流相关接口
        try {
            bo.getBpmClientInputModel().getModel().setWf_sendUserId(update.getUserName());
            bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(update.getDeptId().toString());
            ProcessInstanceModel processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(),update.getId());
            saveWorkFlowLog(bo, processInstanceModel);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(BorrowApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<BorrowApplyVo> selectBorrowList() {
        return this.baseMapper.selectBorrowList();
    }
}
