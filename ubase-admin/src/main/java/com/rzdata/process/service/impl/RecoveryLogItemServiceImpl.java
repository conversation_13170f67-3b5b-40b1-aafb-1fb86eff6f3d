package com.rzdata.process.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.process.domain.RecoveryLogItem;
import com.rzdata.process.domain.bo.RecoveryLogItemBo;
import com.rzdata.process.domain.vo.RecoveryLogItemVo;
import com.rzdata.process.mapper.RecoveryLogItemMapper;
import com.rzdata.process.service.IRecoveryLogItemService;
import com.rzdata.process.service.IRecoveryLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 文件回收记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Service
public class RecoveryLogItemServiceImpl extends ServicePlusImpl<RecoveryLogItemMapper, RecoveryLogItem, RecoveryLogItemVo> implements IRecoveryLogItemService {


    @Resource
    private IRecoveryLogService iRecoveryLogService;
    @Override
    public RecoveryLogItemVo queryById(String id) {
        return null;
    }

    @Override
    @DataScope
    public TableDataInfo<RecoveryLogItemVo> queryPageList(RecoveryLogItemBo bo) {
        //LambdaQueryWrapper<RecoveryLogItem> query = Wrappers.lambdaQuery();
        //query.eq(RecoveryLogItem::getRecoveryId,bo.getRecoveryId());
        //PagePlus<RecoveryLogItem, RecoveryLogItemVo> result = pageVo(PageUtils.buildPagePlus(), query);
        Page<RecoveryLogItemVo> result =  this.baseMapper.queryPageList(PageUtils.buildPage(),bo);
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<RecoveryLogItemVo> queryList(RecoveryLogItemBo bo) {
        LambdaQueryWrapper<RecoveryLogItem> query = new LambdaQueryWrapper<>();
        query.eq(RecoveryLogItem::getRecoveryId,bo.getRecoveryId());
        query.eq(RecoveryLogItem::getDocId,bo.getDocId());
        return listVo(query);
    }

    @Override
    public Boolean insertByBo(RecoveryLogItemBo bo) {
        return null;
    }

    @Override
    public Boolean insertByBoFromDistribute(RecoveryLogItemBo bo) {
        return null;
    }

    @Override
    public Boolean updateByBo(RecoveryLogItemBo bo) {
        return null;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult recovery(RecoveryLogItemBo bo) {
        Arrays.stream(bo.getIds()).forEach(id->{
            RecoveryLogItem recoveryLogItem = getById(id);
            //更新回收类型
            recoveryLogItem.setType(bo.getType());
            //更新回收列表数据中的已回收数量
            RecoveryLog recoveryLog = iRecoveryLogService.getById(recoveryLogItem.getRecoveryId());
            //如果状态是未回收 说明是首次回收 已回收数+1 不然就是更新回收状态 不用给数量+1
            if (ObjectUtil.equals("N",recoveryLogItem.getStatus())) {
                recoveryLog.setRecyclNum(recoveryLog.getRecyclNum()+1);
                //更新回收状态
                recoveryLogItem.setStatus("Y");
            }

            recoveryLogItem.setUpdateTime(new Date());
            recoveryLogItem.setUserName(SecurityUtils.getUsername());
            recoveryLogItem.setNickName(SecurityUtils.getNickname());
            //更新数据库
            updateById(recoveryLogItem);

            iRecoveryLogService.updateById(recoveryLog);
        });

        return AjaxResult.success();
    }
}
