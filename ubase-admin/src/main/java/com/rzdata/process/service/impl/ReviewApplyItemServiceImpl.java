package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.PageResultModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.blueland.bpmclient.model.SearchQuery;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.eventbus.StartModifyApplyEvent;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.ReviewApply;
import com.rzdata.process.domain.ReviewApplyItem;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.WorkFlowInfo;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.MsgTypeEnum;
import com.rzdata.process.enums.ReviewResultEnum;
import com.rzdata.process.mapper.ReviewApplyItemMapper;
import com.rzdata.process.mapper.ReviewApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.WorkflowService;
import com.xxl.job.core.context.XxlJobHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件复审申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class ReviewApplyItemServiceImpl extends ServicePlusImpl<ReviewApplyItemMapper, ReviewApplyItem, ReviewApplyItemVo> implements IReviewApplyItemService {

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReviewApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<ReviewApplyItemVo> queryItemList(ReviewApplyItemBo bo) {
        return this.baseMapper.queryItemList(bo);
    }
}
