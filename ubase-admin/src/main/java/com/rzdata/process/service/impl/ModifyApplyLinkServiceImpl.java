package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.ModifyApplyLinkMapper;
import com.rzdata.process.service.IModifyApplyLinkService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 文件变更操作申请引用Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
public class ModifyApplyLinkServiceImpl extends ServicePlusImpl<ModifyApplyLinkMapper, ModifyApplyLink, ModifyApplyLinkVo> implements IModifyApplyLinkService {

    @Override
    public ModifyApplyLinkVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ModifyApplyLinkVo> queryPageList(ModifyApplyLinkBo bo) {
        PagePlus<ModifyApplyLink, ModifyApplyLinkVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ModifyApplyLinkVo> queryList(ModifyApplyLinkBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    @Override
    public List<ModifyApplyLinkVo> selectListByVersionLink(ModifyApplyLinkBo bo) {
        return baseMapper.selectListByVersionLink(bo);
    }

    private LambdaQueryWrapper<ModifyApplyLink> buildQueryWrapper(ModifyApplyLinkBo bo) {
        LambdaQueryWrapper<ModifyApplyLink> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStatus() != null, ModifyApplyLink::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ModifyApplyLink::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkId()), ModifyApplyLink::getLinkId, bo.getLinkId());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkClass()), ModifyApplyLink::getLinkClass, bo.getLinkClass());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()), ModifyApplyLink::getLinkType, bo.getLinkType());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), ModifyApplyLink::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ModifyApplyLink::getVersionValue, bo.getVersionValue());
        lqw.eq(bo.getIsDeleted() != null, ModifyApplyLink::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ModifyApplyLinkBo bo) {
        ModifyApplyLink add = BeanUtil.toBean(bo, ModifyApplyLink.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ModifyApplyLinkBo bo) {
        ModifyApplyLink update = BeanUtil.toBean(bo, ModifyApplyLink.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApplyLink entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 通过业务id查询ModifyApplyLink表的关联记录(ModifyApplyLink表这是暂存 因为业务中文件的关系可能会调整变化 最终生效文件的关联记录在doc_link_log表中)
     * @param applyId  业务id
     * @param linkType 关联类型 {@link com.rzdata.process.enums.ApplyTypeEnum}
     * @return
     */
    @Override
    public List<ModifyApplyLinkVo> queryDocByApplyIdAndType(String applyId, LinkTypeEnum linkType) {
        return baseMapper.queryDocByApplyIdAndType( applyId, linkType.name());

    }
}
