package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.BorrowApply;
import com.rzdata.process.domain.vo.BorrowApplyVo;
import com.rzdata.process.domain.bo.BorrowApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件借阅申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IBorrowApplyService extends IServicePlus<BorrowApply, BorrowApplyVo> {
	/**
	 * 查询单个
	 * @return
	 */
	BorrowApplyVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BorrowApplyVo> queryPageList(BorrowApplyBo bo);

	/**
	 * 查询列表 关联查询
	 */
	TableDataInfo<BorrowApplyVo> queryPageListNew(BorrowApplyBo bo);

	/**
	 * 查询列表
	 */
	List<BorrowApplyVo> queryList(BorrowApplyBo bo);

	/**
	 * 根据新增业务对象插入文件借阅申请
	 * @param bo 文件借阅申请新增业务对象
	 * @return
	 */
	ProcessInstanceModel insertByBo(BorrowApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件借阅申请
	 * @param bo 文件借阅申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BorrowApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 查询列表
	 */
	List<BorrowApplyVo> selectBorrowList();
}
