package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.ReissueApply;
import com.rzdata.process.domain.vo.ReissueApplyVo;
import com.rzdata.process.domain.bo.ReissueApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件补发申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IReissueApplyService extends IServicePlus<ReissueApply, ReissueApplyVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ReissueApplyVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ReissueApplyVo> queryPageList(ReissueApplyBo bo);

	/**
	 * 查询列表
	 */
	List<ReissueApplyVo> queryList(ReissueApplyBo bo);

	/**
	 * 根据新增业务对象插入文件补发申请
	 * @param bo 文件补发申请新增业务对象
	 * @return
	 */
	ProcessInstanceModel insertByBo(ReissueApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件补发申请
	 * @param bo 文件补发申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ReissueApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 通过分发号查出补发数据
	 * @param disItemId
	 * @return
	 */
	List<ReissueApply> listByDisItemId(String disItemId);
}
