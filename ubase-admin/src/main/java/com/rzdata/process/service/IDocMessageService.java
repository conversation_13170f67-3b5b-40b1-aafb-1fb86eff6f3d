package com.rzdata.process.service;

import com.blueland.bpmclient.model.BpmClientInputModel;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.DocMessage;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.bo.DocMessageBo;
import com.rzdata.process.domain.vo.DocMessageVo;
import com.rzdata.process.enums.MsgTypeEnum;

import java.rmi.ServerException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/11 11:02
 * @Version 1.0
 * @Description 消息服务接口
 */

public interface IDocMessageService extends IServicePlus<DocMessage, DocMessageVo> {

    /**
     * 查询列表
     * @param bo 查询参数
     * @return 消息集合
     */
    TableDataInfo<DocMessageVo>  queryListByPage(DocMessageBo bo);

    /**
     * 获取未读消息数量
     * @param docMessageBo
     * @return
     */
    int getUnreadNum(DocMessageBo docMessageBo);


    /**
     * 文件变更/补发/增发/复审生成消息
     * @param event
     * @return true=成功 false=失败
     */
    void insertMessage (ProcessResultEvent event, MsgTypeEnum msgType);

    /**
     * 借阅申请生成消息
     * @param event
     * @return true=成功 false=失败
     */
    void insertMessageByBorrow(ProcessResultEvent event, MsgTypeEnum msgType);

    /**
     * 文件分发补发后生成待签收消息
     * @param distributeLog
     * @return
     */
    void insertMessageByDistribute(DocDistributeLog distributeLog);

    /**
     * 文件回收消息
     * @param applyPo
     */
    void insertMessageByReceive(ModifyApply applyPo);

    /**
     * 文件生效消息
     * @param distributeLog
     */
    void insertMessageByValidity(DocDistributeLog distributeLog);


    /**
     * 文件生效消息
     * @param applyPo
     */
    void insertMessageByValidity(ModifyApply applyPo);

    /**
     * 文件失效消息
     * @param distributeItemList
     */
    void insertMessageByInvalidation(List<DistributeItem> distributeItemList);


    void insertMessageEndArchiving(String applyId) throws ServerException;

    /**
     * 消息速递
     * @return
     */
    List<DocMessageVo> queryMessageDelivery();


    /**
     * 生成消息 定时任务专用
     * @param docMessage
     * @return true=成功 false=失败
     */
    Boolean insert (DocMessage docMessage);
}
