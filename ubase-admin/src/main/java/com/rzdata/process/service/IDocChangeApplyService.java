package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.DocChangeApply;
import com.rzdata.process.domain.vo.DocChangeApplyDetailVo;
import com.rzdata.process.domain.vo.DocChangeApplyVo;
import com.rzdata.process.domain.bo.DocChangeApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件变更申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IDocChangeApplyService extends IServicePlus<DocChangeApply, DocChangeApplyVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocChangeApplyDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocChangeApplyVo> queryPageList(DocChangeApplyBo bo);

	/**
	 * 查询列表
	 */
	List<DocChangeApplyVo> queryList(DocChangeApplyBo bo);

	/**
	 * 根据新增业务对象插入文件变更申请
	 * @param bo 文件变更申请新增业务对象
	 * @return
	 */
	ProcessInstanceModel insertByBo(DocChangeApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件变更申请
	 * @param bo 文件变更申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocChangeApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
