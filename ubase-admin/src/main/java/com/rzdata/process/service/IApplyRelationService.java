package com.rzdata.process.service;

import com.rzdata.process.domain.ApplyRelation;
import com.rzdata.process.domain.vo.ApplyRelationVo;
import com.rzdata.process.domain.bo.ApplyRelationBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件申请流程关联Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IApplyRelationService extends IServicePlus<ApplyRelation, ApplyRelationVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ApplyRelationVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ApplyRelationVo> queryPageList(ApplyRelationBo bo);

	/**
	 * 查询列表
	 */
	List<ApplyRelationVo> queryList(ApplyRelationBo bo);

	/**
	 * 根据新增业务对象插入文件申请流程关联
	 * @param bo 文件申请流程关联新增业务对象
	 * @return
	 */
	Boolean insertByBo(ApplyRelationBo bo);

	/**
	 * 根据编辑业务对象修改文件申请流程关联
	 * @param bo 文件申请流程关联编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ApplyRelationBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
