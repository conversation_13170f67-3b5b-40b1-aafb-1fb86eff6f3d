package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocVersionFavorites;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.bo.DocVersionFavoritesBo;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import com.rzdata.process.domain.vo.DocVersionFavoritesVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.mapper.DocVersionFavoritesMapper;
import com.rzdata.process.service.IDocVersionFavoritesService;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 我的收藏Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-03
 */
@Service
public class DocVersionFavoritesServiceImpl extends ServicePlusImpl<DocVersionFavoritesMapper, DocVersionFavorites, DocVersionFavoritesVo> implements IDocVersionFavoritesService {

    @Autowired
    ISysDeptService iSysDeptService;

    @Override
    public TableDataInfo<DocDistributeLogVo> queryPageFavorites(DocDistributeLogBo bo) {
        Page<DocDistributeLogVo> page = PageUtils.buildPage();
        Page<DocDistributeLogVo> docDistributeLogVoPage = baseMapper.queryPageFavorites(page, bo, SecurityUtils.getDeptId(),SecurityUtils.getUserId());
        return PageUtils.buildDataInfo(docDistributeLogVoPage);
    }

    @Override
    public TableDataInfo<VersionVo> queryPageFavoritesRecordFile(VersionBo bo) {
        Page<VersionVo> page = PageUtils.buildPage();
        Page<VersionVo> iPage = this.baseMapper.queryPageFavoritesRecordFile(page, bo,SecurityUtils.getDeptId(),SecurityUtils.getUserId());
        for (VersionVo vVo:iPage.getRecords()) {
            vVo.setCompileDeptName(iSysDeptService.getById(vVo.getDeptId()).getDeptName());
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public Boolean saveOrRemoveFavorites(String versionId) {
        if (inFavorites(versionId)){
            return  remove(new LambdaQueryWrapper<DocVersionFavorites>().eq(DocVersionFavorites::getVersionId,versionId).eq(DocVersionFavorites::getUserId,SecurityUtils.getUserId()));
        }else {
            DocVersionFavorites favorites = new DocVersionFavorites();
            favorites.setUserId(SecurityUtils.getUserId());
            favorites.setVersionId(versionId);
            return save(favorites);
        }
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids) {
        return remove(new LambdaQueryWrapper<DocVersionFavorites>().in(DocVersionFavorites::getVersionId,ids).eq(DocVersionFavorites::getUserId,SecurityUtils.getUserId()));
    }

    @Override
    public Boolean inFavorites(String versionId) {
        return count(new LambdaQueryWrapper<DocVersionFavorites>().eq(DocVersionFavorites::getVersionId,versionId).eq(DocVersionFavorites::getUserId,SecurityUtils.getUserId()))>0;
    }
}
