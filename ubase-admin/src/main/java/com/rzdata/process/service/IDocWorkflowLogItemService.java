package com.rzdata.process.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.PageResultModel;
import com.blueland.bpmclient.model.SearchQuery;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocWorkflowLogItem;
import com.rzdata.process.domain.bo.DocWorkflowLogItemBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.vo.DocWorkflowLogItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
public interface IDocWorkflowLogItemService extends IServicePlus<DocWorkflowLogItem, DocWorkflowLogItemVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocWorkflowLogItemVo queryById(Long id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocWorkflowLogItemVo> queryPageList(DocWorkflowLogItemBo bo);

	/**
	 * 查询列表
	 */
	List<DocWorkflowLogItemVo> queryList(DocWorkflowLogItemBo bo);

	/**
	 * 根据新增业务对象插入【请填写功能名称】
	 * @param bo 【请填写功能名称】新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocWorkflowLogItemBo bo);

	/**
	 * 根据编辑业务对象修改【请填写功能名称】
	 * @param bo 【请填写功能名称】编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocWorkflowLogItemBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
