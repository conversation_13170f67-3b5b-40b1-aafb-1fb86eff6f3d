package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.VersionBo;
import com.rzdata.process.domain.vo.VersionVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件版本记录Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IVersionService extends IServicePlus<Version, VersionVo> {
	/**
	 * 查询单个
	 * @return
	 */
	VersionVo queryById(String id);

	/**
	 * 查询有效的版本
	 * @return
	 */
	Version queryOneByStandardId(String id);

	Version queryByDocIdAndVersion(String docId, String versionValue);

	/**
	 * 查询列表
	 */
    TableDataInfo<VersionVo> queryPageList(VersionBo bo);

	/**
	 * 查询列表
	 */
	List<VersionVo> queryList(VersionBo bo);

	/**
	 * 根据新增业务对象插入文件版本记录
	 * @param bo 文件版本记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(VersionBo bo);

	/**
	 * 根据编辑业务对象修改文件版本记录
	 * @param bo 文件版本记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(VersionBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	List<VersionVo> selectVersionListByAppId(String applyId);

	List<VersionVo> selectVersionList(List<String> applyId);

	List<VersionVo> selectVersionListByDocId(String docId);

	VersionVo selectVersionByDocId(String docId);

	List<VersionVo> selectVersionListByDocIdAndVersionId(String docId, String versionId);

	TableDataInfo<VersionVo> selectRecordFile(VersionBo bo);

	TableDataInfo<VersionVo> selectRecordFileCompany(VersionBo bo);

	TableDataInfo<VersionVo> selectDeptFile(VersionBo bo);

}
