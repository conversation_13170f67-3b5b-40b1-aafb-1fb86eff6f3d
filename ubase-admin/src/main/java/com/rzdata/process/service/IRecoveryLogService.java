package com.rzdata.process.service;

import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.process.domain.vo.RecoveryLogVo;
import com.rzdata.process.domain.bo.RecoveryLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件回收记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IRecoveryLogService extends IServicePlus<RecoveryLog, RecoveryLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	RecoveryLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<RecoveryLogVo> queryPageList(RecoveryLogBo bo);

	/**
	 * 查询列表
	 */
	List<RecoveryLogVo> queryList(RecoveryLogBo bo);

	/**
	 * 根据新增业务对象插入文件回收记录
	 * @param bo 文件回收记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(RecoveryLogBo bo);


	/**
	 * 根据新增业务对象插入文件回收记录
	 * @param bo 文件回收记录新增业务对象
	 * @return
	 */
	Boolean insertByBoFromDistribute(RecoveryLogBo bo);

	/**
	 * 根据编辑业务对象修改文件回收记录
	 * @param bo 文件回收记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(RecoveryLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	RecoveryLog selectRecoveryByApplyId(String id);

	RecoveryLog selectRecoveryByApplyIdAndDeptId(String applyId,String deptId);

}
