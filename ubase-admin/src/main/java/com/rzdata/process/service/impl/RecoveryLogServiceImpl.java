package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.process.domain.bo.RecoveryLogBo;
import com.rzdata.process.domain.vo.RecoveryLogVo;
import com.rzdata.process.mapper.RecoveryLogMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.IDocDistributeLogService;
import com.rzdata.process.service.IRecoveryLogService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文件回收记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
@Service
public class RecoveryLogServiceImpl extends ServicePlusImpl<RecoveryLogMapper, RecoveryLog, RecoveryLogVo> implements IRecoveryLogService {

    @Autowired
    VersionMapper versionMapper;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    IDocDistributeLogService docDistributeLogService;
    @Override
    public RecoveryLogVo queryById(String id){
        RecoveryLogVo vo = getVoById(id);
        return vo;
    }

    @Override
    @DataScope()
    public TableDataInfo<RecoveryLogVo> queryPageList(RecoveryLogBo bo) {
        Page<RecoveryLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<RecoveryLogVo> iPage = this.baseMapper.listPage(page, bo);
        iPage.getRecords().forEach(vo->{
            vo.setVersionVo(versionMapper.selectDetailById(vo.getVersionId()));
        });
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<RecoveryLogVo> queryList(RecoveryLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<RecoveryLog> buildQueryWrapper(RecoveryLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RecoveryLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), RecoveryLog::getDocId, bo.getDocId());
        //lqw.eq(StringUtils.isNotBlank(bo.getStatus()), RecoveryLog::getStatus, bo.getStatus());
        //lqw.like(StringUtils.isNotBlank(bo.getUserName()), RecoveryLog::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), RecoveryLog::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), RecoveryLog::getApplyId, bo.getApplyId());
        lqw.eq(bo.getDeptId() != null, RecoveryLog::getDeptId, bo.getDeptId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(RecoveryLogBo bo) {
        RecoveryLog add = BeanUtil.toBean(bo, RecoveryLog.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //部门
        add.setDeptId(loginUser.getDeptId());
        //回收人是当前登录人 因为只有QA02能请求到这个API 所以直接获取
        //add.setUserName(loginUser.getUsername());
//        if (ObjectUtil.isNotEmpty(bo.getDocId())) {
//            QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
//            queryWrapper.lambda().eq(Version:: getDocId, bo.getDocId());
//            List<Version> list = iVersionService.list(queryWrapper);
//            if (ObjectUtil.isNotEmpty(list)) {
//                add.setVersionId(list.get(0).getId());
//            }
//        }
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean insertByBoFromDistribute(RecoveryLogBo bo) {
        RecoveryLog add = BeanUtil.toBean(bo, RecoveryLog.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(RecoveryLogBo bo) {
        RecoveryLog update = BeanUtil.toBean(bo, RecoveryLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(RecoveryLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public RecoveryLog selectRecoveryByApplyId(String id) {
        QueryWrapper<RecoveryLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RecoveryLog:: getDocId, id);
//        queryWrapper.lambda().eq(RecoveryLog:: getStatus, NumberConstants.ZERO+"");
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public RecoveryLog selectRecoveryByApplyIdAndDeptId(String applyId,String deptId) {
        QueryWrapper<RecoveryLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RecoveryLog::getApplyId, applyId);
        queryWrapper.lambda().eq(RecoveryLog:: getDeptId,deptId);
        return this.baseMapper.selectOne(queryWrapper);
    }

}
