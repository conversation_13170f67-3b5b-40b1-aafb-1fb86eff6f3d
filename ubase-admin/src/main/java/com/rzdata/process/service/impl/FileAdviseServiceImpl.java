package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.FileAdviseBo;
import com.rzdata.process.domain.vo.FileAdviseVo;
import com.rzdata.process.domain.FileAdvise;
import com.rzdata.process.mapper.FileAdviseMapper;
import com.rzdata.process.service.IFileAdviseService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件建议Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-19
 */
@Service
public class FileAdviseServiceImpl extends ServicePlusImpl<FileAdviseMapper, FileAdvise, FileAdviseVo> implements IFileAdviseService {

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    ISysDeptService deptService;

    @Override
    public FileAdviseVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<FileAdviseVo> queryPageList(FileAdviseBo bo) {
        if(ObjectUtil.isNotEmpty(bo.getSearchType())) {
            bo.setCreateUser(SecurityUtils.getUsername());
        }
        PagePlus<FileAdvise, FileAdviseVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<FileAdviseVo> queryList(FileAdviseBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<FileAdvise> buildQueryWrapper(FileAdviseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FileAdvise> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), FileAdvise::getDocId, bo.getDocId());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), FileAdvise::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), FileAdvise::getDocTypeCode, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocTypeName()), FileAdvise::getDocTypeName, bo.getDocTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), FileAdvise::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), FileAdvise::getVersionValue, bo.getVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getDocDeptId()), FileAdvise::getDocDeptId, bo.getDocDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getDocDeptName()), FileAdvise::getDocDeptName, bo.getDocDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getSummary()), FileAdvise::getSummary, bo.getSummary());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptId()), FileAdvise::getDeptId, bo.getDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateUser()), FileAdvise::getCreateUser, bo.getCreateUser());
        lqw.like(StringUtils.isNotBlank(bo.getCreateName()), FileAdvise::getCreateName, bo.getCreateName());
        lqw.between(ObjectUtil.isNotEmpty(params.get("startTime")) && ObjectUtil.isNotEmpty(params.get("endTime")),
                FileAdvise::getCreateTime ,params.get("startTime"), params.get("endTime"));
        lqw.orderByDesc(FileAdvise:: getCreateTime);
        return lqw;
    }

    @Override
    public Boolean insertByBo(FileAdviseBo bo) {
        FileAdvise add = BeanUtil.toBean(bo, FileAdvise.class);
        validEntityBeforeSave(add);
        add.setCreateUser(SecurityUtils.getUsername());
        add.setCreateName(SecurityUtils.getNickname());
        add.setDeptId(SecurityUtils.getDeptId());
        add.setCreateTime(new Date());
        if (ObjectUtil.isNotEmpty(bo.getDocTypeCode()) && ObjectUtil.isEmpty(bo.getDocTypeName())) {
            DocClass docClass = iDocClassService.getById(bo.getDocTypeCode());
            if (ObjectUtil.isNotEmpty(docClass)) {
                add.setDocTypeName(docClass.getClassName());
            }
        }
        add.setDeptName(deptService.getById(SecurityUtils.getDeptId()).getDeptName());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(FileAdviseBo bo) {
        FileAdvise update = BeanUtil.toBean(bo, FileAdvise.class);
        update.setCreateName(this.baseMapper.selectUserName(SecurityUtils.getUsername()));
        update.setDeptId(SecurityUtils.getDeptId());
        update.setDeptName(deptService.getById(SecurityUtils.getDeptId()).getDeptName());
        if (ObjectUtil.isNotEmpty(bo.getDocTypeCode()) && ObjectUtil.isEmpty(bo.getDocTypeName())) {
            DocClass docClass = iDocClassService.getById(bo.getDocTypeCode());
            if (ObjectUtil.isNotEmpty(docClass)) {
                update.setDocTypeName(docClass.getClassName());
            }
        }
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(FileAdvise entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
