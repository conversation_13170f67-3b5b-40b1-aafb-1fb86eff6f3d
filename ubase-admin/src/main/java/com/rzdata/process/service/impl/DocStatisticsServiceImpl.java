package com.rzdata.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocDistributeLog;
import com.rzdata.process.domain.RecoveryLog;
import com.rzdata.process.domain.bo.DocDisStatisticsBo;
import com.rzdata.process.domain.bo.DocStatisticsBo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.mapper.DocStatisticsMapper;
import com.rzdata.process.service.IDocStatisticsService;
import com.rzdata.process.service.IRecoveryLogService;
import com.rzdata.process.service.IStandardService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:29
 * @Version 1.0
 * @Description
 */
@Service
public class DocStatisticsServiceImpl implements IDocStatisticsService {


    @Resource
    DocStatisticsMapper docStatisticsMapper;

    @Resource
    IStandardService standardService;

    @Resource
    IRecoveryLogService recoveryLogService;


    /**
     * @param bo 文件统计实体类
     * @return
     */
    @Override
    public AjaxResult<List<DocStatisticsChangeTypeVo>> changeType(DocStatisticsBo bo) {
        try {
            List<DocStatisticsChangeTypeVo> number = docStatisticsMapper.selectChangeType(bo);
            return AjaxResult.success(number);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询统计文件失败", null);
        }
    }

    @Override
    public AjaxResult<List<DocStatisticsChangeFactorVo>> changeFactor(DocStatisticsBo bo) {
        try {
            List<DocStatisticsChangeFactorVo> number = docStatisticsMapper.selectChangeFactor(bo);
            return AjaxResult.success(number);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询统计文件失败", null);
        }
    }

    @Override
    public AjaxResult<List<DocStatisticsRecDisVo>> recovery(StandardBo bo) {
        try {
            //分页查出doc_standard表数据
            TableDataInfo<StandardVo> list = standardService.queryPageList(bo);
            //返回结果集合
            List docStatisticsRecDisVoList = new ArrayList<DocStatisticsRecDisVo>();
            //遍历数据
            list.getRows().forEach(standardVo -> {
                DocStatisticsRecDisVo docStatisticsRecDisVo = new DocStatisticsRecDisVo();
                //通过docId和deptId查分发记录
                LambdaQueryWrapper<DocDistributeLog> queryDisLog = new LambdaQueryWrapper<>();
                queryDisLog.eq(DocDistributeLog::getDocId, standardVo.getId());
                queryDisLog.eq(DocDistributeLog::getDeptId, standardVo.getDeptId());
                DocDistributeLog distributeLog = docStatisticsMapper.selectOne(queryDisLog);
                //通过docId和deptId查回收记录
                LambdaQueryWrapper<RecoveryLog> queryRecLog = new LambdaQueryWrapper<>();
                queryRecLog.eq(RecoveryLog::getDocId, standardVo.getId());
                queryRecLog.eq(RecoveryLog::getDeptId, standardVo.getDeptId());
                RecoveryLogVo recoveryLogVo = recoveryLogService.getVoOne(queryRecLog);
                //从分发表数据赋值
                if (ObjectUtils.isNotEmpty(distributeLog)) {
                    docStatisticsRecDisVo.setDocId(distributeLog.getDocId());
                    docStatisticsRecDisVo.setVersionValue(distributeLog.getVersionValue());
                    docStatisticsRecDisVo.setDeptId(distributeLog.getDeptId());
                    docStatisticsRecDisVo.setDistributeNum(distributeLog.getNums());
                    //docStatisticsRecDisVo.setPrintStatus(distributeLog.getPrintStatus());
                }
                //从回收表数据赋值
                if (ObjectUtils.isNotEmpty(recoveryLogVo)) {
                    docStatisticsRecDisVo.setDocName(recoveryLogVo.getDocName());
                    docStatisticsRecDisVo.setDocClass(recoveryLogVo.getDocClass());
                    //docStatisticsRecDisVo.setPrintNum(recoveryLogVo.getPrintNum());
                    docStatisticsRecDisVo.setRecoveryNum(recoveryLogVo.getRecyclNum());
                    //docStatisticsRecDisVo.setRecoveryStatus(Integer.parseInt(recoveryLogVo.getStatus()));
                }
                //文件状态
                docStatisticsRecDisVo.setFileStatus(Integer.parseInt(standardVo.getStatus()));
                //加入到list
                docStatisticsRecDisVoList.add(docStatisticsRecDisVo);
            });
            //过程中未抛出异常则返回成功
            return AjaxResult.success(docStatisticsRecDisVoList);
        } catch (Exception e) {
            e.printStackTrace();
            //返回失败
            return AjaxResult.error("分发回收统计失败!", null);
        }
    }

    @Override
    public AjaxResult<List<HashMap>> distribute(DocDisStatisticsBo bo) {
        List<DocStatisticsDistributeVo> distributeLogList = docStatisticsMapper.selectDistribute(bo);

        List<HashMap> list = new ArrayList<>();
        for (DocStatisticsDistributeVo distributeLog:distributeLogList) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("deptId",distributeLog.getDeptId());
            map.put("distribute",distributeLog.getDistributeNum());
            list.add(map);
            //通过docId和deptId查回收记录
            LambdaQueryWrapper<RecoveryLog> queryRecLog = new LambdaQueryWrapper<>();
            queryRecLog.eq(RecoveryLog::getDocId, distributeLog.getDocId());
            queryRecLog.eq(RecoveryLog::getDeptId, distributeLog.getDeptId());
            queryRecLog.eq(RecoveryLog::getApplyId, distributeLog.getApplyId());
            RecoveryLogVo recoveryLogVo = recoveryLogService.getVoOne(queryRecLog);
            if (recoveryLogVo!=null){
                String number = (recoveryLogVo.getRecyclNum()/(recoveryLogVo.getNoRecyclNum()+recoveryLogVo.getRecyclNum())*100)+"%";
                map.put("number",number);
            }
        }
        return AjaxResult.success(list);
    }
}
