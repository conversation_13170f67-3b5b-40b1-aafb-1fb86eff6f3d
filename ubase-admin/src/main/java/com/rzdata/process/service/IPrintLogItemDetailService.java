package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.PrintLogItemDetail;
import com.rzdata.process.domain.bo.PrintLogItemDetailBo;
import com.rzdata.process.domain.vo.PrintLogItemDetailVo;

/**
 * 文件打印记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
public interface IPrintLogItemDetailService extends IServicePlus<PrintLogItemDetail, PrintLogItemDetailVo> {
	/**
	 * 查询列表
	 */
    TableDataInfo<PrintLogItemDetailVo> queryPageList(PrintLogItemDetailBo bo);
}
