package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.ReprintLog;
import com.rzdata.process.domain.bo.ReprintLogBo;
import com.rzdata.process.domain.vo.ReprintLogVo;
import com.rzdata.process.mapper.ReprintLogMapper;
import com.rzdata.process.service.IReprintLogService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:57
 * @Version 1.0
 * @Description
 */
@Service
public class ReprintServiceImpl extends ServicePlusImpl<ReprintLogMapper, ReprintLog, ReprintLogVo> implements IReprintLogService {


    @Override
    public TableDataInfo<ReprintLogVo> queryPageList(ReprintLogBo bo) {
        Page<ReprintLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<ReprintLogVo> iPage = this.baseMapper.selectReprintLogPage(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public boolean insertByBo(ReprintLogBo bo) {
        try {
            ReprintLog add = BeanUtil.toBean(bo, ReprintLog.class);
                add.setReprintUserName(SecurityUtils.getUsername());
                add.setReprintNickName(SecurityUtils.getNickname());
                add.setReprintTime(new Date());
                add.setReprintNum(1);
                add.setId(null);
                saveOrUpdate(add);
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }
}
