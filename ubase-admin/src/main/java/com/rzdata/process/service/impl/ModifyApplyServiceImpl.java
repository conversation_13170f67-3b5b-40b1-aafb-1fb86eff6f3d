package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.BpmClientInputModel;
import com.blueland.bpmclient.model.ProcessDefinitionModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.eventbus.StartModifyApplyEvent;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.mapper.SysUserMapper;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件变更操作申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
@Slf4j
@Service
public class ModifyApplyServiceImpl extends ServicePlusImpl<ModifyApplyMapper, ModifyApply, ModifyApplyVo> implements IModifyApplyService {

    private static final String ID_SPLIT_KEY = ",";
    private static final Integer IS_DELETED = 1;
    private static final String KEY_GENERATOR_DOC = "key.id.generator.doc";

    private Logger logger = LoggerFactory.getLogger(ModifyApplyServiceImpl.class);

    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IModifyApplyDistributeService modifyApplyDistributeService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    private IStandardService standardService;
    @Autowired
    private IVersionService versionService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    private IDocDistributeLogService docDistributeLogService;
    @Autowired
    private IBasicFileService basicFileService;
    @Autowired
    private IModifyApplyTrainService modifyApplyTrainService;
    @Autowired
    private IApplyRelationService applyRelationService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Resource
    private SysUserMapper sysUserMapper;
    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    IDocClassFlowService iDocClassFlowService;

    @Autowired
    IRecoveryLogService iRecoveryLogService;

    @Autowired
    IRecoveryLogItemService iRecoveryLogItemService;

    @Autowired
    IDocDistributeLogService iDocDistributeLogService;

    @Autowired
    IDistributeItemService iDistributeItemService;

    @Autowired
    IDocChangeApplyService iDocChangeApplyService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    ISysRoleService sysRoleService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IDocVersionLinkService iDocVersionLinkService;

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public boolean distribute(String id) {
        if (ObjectUtil.isEmpty(id)) {
            return false;
        }
        ModifyApply applyPo = getById(id);
        if (ObjectUtil.isEmpty(applyPo)) {
            return false;
        }
        /**
         * 已经分发
         */
        if (YNEnum.toType(applyPo.getYNDistribute()) == YNEnum.YES) {
            return true;
        }
        switch (ApplyTypeEnum.toType(applyPo.getChangeType())) {
            case ADD:
                handleAddApplySuccess(applyPo, null, null);
                break;
            case UPDATE:
                handleUpdateApplySuccess(applyPo, null, null);
                break;
            case DISUSE:
                handleDisuseApplySuccess(applyPo, null, null);
                break;
            default:
                break;
        }
        return true;
    }

    @Override
    public ModifyApplyVo queryById(String id) {
        ModifyApplyVo applyVo = getVoById(id);
        if (ObjectUtil.isEmpty(applyVo)) {
            return applyVo;
        }
        List<String> deptIdList = new ArrayList<>();
        Collection<String> userNameList = new ArrayList<>();
        deptIdList.add(applyVo.getDeptId());
        userNameList.add(applyVo.getUserName());

        //倒序查询 第0个为最新一条数据 这个时候新的数据已经插入 第一条就是当前的数据
        List<String> oldApplyId = this.baseMapper.getApplyIdByDocId(applyVo.getDocId());

        List<ModifyApplyDistributeVo> distributeVoList = new ArrayList<>();
        //作废的分发数据
        if(ApplyTypeEnum.DISUSE.name().equals(applyVo.getChangeType())) {
            ModifyApplyDistributeBo distributeBo = new ModifyApplyDistributeBo();

            //表示从变更申请发起的作废
            if(StringUtils.isNotEmpty(applyVo.getChangeId())) {
                distributeBo.setChangeId(applyVo.getChangeId());
                distributeBo.setIsDeleted(0);
                distributeVoList = modifyApplyDistributeService.queryListByChangeIdApplyId(distributeBo);
            }else {
                distributeBo.setApplyId(id);
                distributeBo.setIsDeleted(1);
                distributeVoList = modifyApplyDistributeService.queryList(distributeBo);
            }

            distributeVoList = distributeVoList.stream().distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(distributeVoList)) {
                applyVo.setDistributeDepths(distributeVoList);
                deptIdList.addAll(distributeVoList.stream().map(d -> d.getDeptId()).collect(Collectors.toList()));
            }
        }else {
            /**
             * 分发
             */
            ModifyApplyDistributeBo distributeBo = new ModifyApplyDistributeBo();
            distributeBo.setApplyId(id);
            distributeBo.setIsDeleted(0);
            distributeVoList = modifyApplyDistributeService.queryList(distributeBo);
        }

        distributeVoList = distributeVoList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(distributeVoList)) {
            applyVo.setDistributeDepths(distributeVoList);
            deptIdList.addAll(distributeVoList.stream().map(d -> d.getDeptId()).collect(Collectors.toList()));
        }


        //查询文件历史
        List<VersionVo> versions = versionService.selectVersionListByDocId(applyVo.getDocId());
        if(ObjectUtil.isNotEmpty(versions)) {
            applyVo.setVersions(versions);
        }

        /**
         * 关联
         */

        //不为ADD(新增)则一定是修订或者作废
        if (!ApplyTypeEnum.toType(applyVo.getChangeType()).equals(ApplyTypeEnum.ADD)){
            //把当前生效主文件查出来
            List<DocLinkLogVo> docLinkLogVos = docLinkLogService.queryDocLinkVo(applyVo.getDocId(), null, LinkTypeEnum.DOC);
            //虽然没有传versionId可能会查出之前多个版本的主文件 但是SQL做了创建时间的倒序排序 所以如果有数据 第一条则是上一次的主文件(因为这个时候流程还没有结束
            // 当前流程的主文件还没有插入到doc_link_log表中 则查到的是一定上次的主文件(也就是当前生效的)
            if (ObjectUtil.isNotEmpty(docLinkLogVos)){
                applyVo.setPreStandardDoc(docLinkLogVos.get(0));
            }
            //附件同理
            List<DocLinkLogVo> appendixLinkLogVos = docLinkLogService.queryDocLinkVo(applyVo.getDocId(), applyVo.getVersionId(), LinkTypeEnum.APPENDIX);
            applyVo.setPreAppendixes(appendixLinkLogVos);

        }

        //当前变更版本主文件
        List<ModifyApplyLinkVo> modifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.DOC);
        if (ObjectUtil.isNotEmpty(modifyApplyLinkVoList)) {
            applyVo.setStandardDoc(modifyApplyLinkVoList.get(0));
        }
        //当前变更版本附件
        List<ModifyApplyLinkVo> appendixModifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.APPENDIX);
        applyVo.setAppendixes(appendixModifyApplyLinkVoList);

        //备注附件
        List<ModifyApplyLinkVo> appendixRemarkModifyApplyLinkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(id,LinkTypeEnum.APPENDIX_REMARK);
        applyVo.setRemarkDoc(appendixRemarkModifyApplyLinkVoList);

        /*
         * 关联文件和关键记录不在这个接口直接查出了 改成接口查询了 前端请求接口拿到关联文件和关键记录的数据
         */


        //ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
        //linkBo.setIsDeleted(0);
        //linkBo.setApplyId(id);

        //LambdaQueryWrapper<ModifyApplyLink> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //lambdaQueryWrapper.eq(ModifyApplyLink::getApplyId,id);
        //lambdaQueryWrapper.in(ModifyApplyLink::getIsDeleted,0,3);

        //List<ModifyApplyLinkVo> linkVoList = this.modifyApplyLinkService.listVo(lambdaQueryWrapper);
        //查询变更备注附件
//        if(ApplyTypeEnum.UPDATE.name().equals(applyVo.getChangeType()) &&
//                applyVo.getChangeId()!=null) {
//            linkBo.setApplyId(applyVo.getChangeId());
//            linkVoList.addAll(modifyApplyLinkService.queryList(linkBo));
//        }
//        if (ObjectUtil.isNotEmpty(applyVo.getChangeId()) && !Objects.equals("ADD", applyVo.getChangeType()) && ObjectUtil.isNotEmpty(oldApplyId)) {
//            linkBo.setApplyId(null);
//            for (String s : oldApplyId) {
//                linkBo.setApplyId(s);
//                List<ModifyApplyLinkVo> linkVoList2 = modifyApplyLinkService.queryList(linkBo);
//                linkVoList.addAll(linkVoList2);
//            }
//        }
        /*if (CollectionUtil.isNotEmpty(linkVoList)) {
            List<String> basicFileIdLinks = new ArrayList<>();
            List<String> docIdLinks = new ArrayList<>();
            List<String> records = new ArrayList<>();
            List<ModifyApplyLinkVo> remarkList = new ArrayList<>();
            for (ModifyApplyLinkVo linkVo : linkVoList) {
                switch (LinkTypeEnum.toType(linkVo.getLinkType())) {
                    case DOC:
                        //if (!ObjectUtil.equal(applyVo.getChangeType(),ApplyTypeEnum.DISUSE.name())){
                        applyVo.setStandardDoc(linkVo);
                        linkVo.setDocName(basicFileService.getVoById(linkVo.getFileId()).getFileName());
                        //}
                        break;
                    case DOC_1:
                        //if (!ObjectUtil.equal(applyVo.getChangeType(),"ADD")){
                        applyVo.setPreStandardDoc(linkVo);
                        //}
                        break;
                    case RECORD:
                        if (CollectionUtil.isEmpty(applyVo.getRecordLinks())) {
                            applyVo.setRecordLinks(new ArrayList<>());
                        }
                        linkVo.setStatus(linkVo.getIsDeleted());
                        applyVo.getRecordLinks().add(linkVo);
                        break;
                    case APPENDIX:
                        if (CollectionUtil.isEmpty(applyVo.getAppendixes())) {
                            applyVo.setAppendixes(new ArrayList<>());
                        }
                        applyVo.getAppendixes().add(linkVo);
                        break;
                    case APPENDIX_1:
                        if (CollectionUtil.isEmpty(applyVo.getPreAppendixes())) {
                            applyVo.setPreAppendixes(new ArrayList<>());
                        }
                        applyVo.getPreAppendixes().add(linkVo);
                        break;

                    case APPENDIX_REMARK:
                        remarkList.add(linkVo);

                        *//*
                        if (CollectionUtil.isEmpty(applyVo.getRemarkDoc())) {
                            applyVo.setRemarkDoc(new ArrayList<>());
                        }*//*
                        //查询具体版本号的备注文件
                        *//*if(applyVo.getVersionValue().equals(linkVo.getVersionValue())) {
                            applyVo.getRemarkDoc().add(linkVo);
                        }*//*
                        break;
                    case REF_DOC:
                        if (CollectionUtil.isEmpty(applyVo.getDocLinks())) {
                            applyVo.setDocLinks(new ArrayList<>());
                        }
                        //REF_DOC linkId存的是version_id
                        //VersionVo voById = versionService.getVoById(linkVo.getLinkId());
                        //linkVo.setLinkId(voById.getEncryptFileId());
                        applyVo.getDocLinks().add(linkVo);
                        records.add(linkVo.getLinkId());
                        break;
                }
                if (LinkClassEnum.DOC.name().equalsIgnoreCase(linkVo.getLinkClass())) {
                    docIdLinks.add(linkVo.getDocId());
                } else {
                    basicFileIdLinks.add(linkVo.getLinkId());
                }
            }
            applyVo.setRemarkDoc(remarkList);
            Map<String, String> idNameMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(basicFileIdLinks)) {
//                List<DocLinkLogVo> linkLogVos = iDocLinkLogService.listVoByIds(basicFileIdLinks);
//                if (ObjectUtil.isNotEmpty(linkLogVos)) {
//                    Map<String, DocLinkLogVo> map = linkLogVos.stream().collect(Collectors.toMap(x -> x.getLinkCode(), x -> x, (next, last) -> next));
//                    List<String> ids = linkLogVos.stream().map(x -> x.getLinkCode()).collect(Collectors.toList());
//                    List<BasicFileVo> fileVoList = basicFileService.listVoByIds(ids);
//                    if (CollectionUtil.isNotEmpty(fileVoList)) {
//                        for (BasicFileVo fileVo : fileVoList) {
//                            idNameMap.put(map.get(fileVo.getId()).getId(), fileVo.getFileName());
//                        }
//                    }
//                }
                List<BasicFileVo> fileVoList = basicFileService.listVoByIds(basicFileIdLinks);
                if (CollectionUtil.isNotEmpty(fileVoList)) {
                    for (BasicFileVo fileVo : fileVoList) {
                        idNameMap.put(fileVo.getId(), fileVo.getFileName());
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(docIdLinks)) {
                List<StandardVo> standardVoList = standardService.listVoByIds(docIdLinks);
                if (CollectionUtil.isNotEmpty(standardVoList)) {
                    for (StandardVo standardVo : standardVoList) {
                        idNameMap.put(standardVo.getId(), standardVo.getDocName());
                    }
                }
//                QueryWrapper<BasicFile> queryWrapper = new QueryWrapper<>();
//                queryWrapper.lambda().in(BasicFile::getBusinessId, docIdLinks);
//                List<BasicFile> standardVoList = basicFileService.list(queryWrapper);
//                if (CollectionUtil.isNotEmpty(standardVoList)) {
//                    for (BasicFile basicFile : standardVoList) {
//                        idNameMap.put(basicFile.getBusinessId(), basicFile.getFileName());
//                    }
//                }
            }
            if (ObjectUtil.isNotEmpty(records)) {
                QueryWrapper<BasicFile> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().in(BasicFile::getBusinessId, records);
                List<BasicFile> list = basicFileService.list(queryWrapper);
                if (ObjectUtil.isNotEmpty(list)) {
                    for (BasicFile basicFile : list) {
                        idNameMap.put(basicFile.getBusinessId(), basicFile.getFileName());
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(linkVoList)) {
                for (ModifyApplyLinkVo modifyApplyLinkVo : linkVoList) {
                    if (ObjectUtil.isNotEmpty(modifyApplyLinkVo.getDocId())) {
                        String version = this.baseMapper.getDocVersionByDocId(modifyApplyLinkVo.getDocId());
                        if (ObjectUtil.isNotEmpty(version)) {
                            modifyApplyLinkVo.setVersionValue(version);
                        }
                    }
                }
            }
            if (MapUtil.isNotEmpty(idNameMap)) {
                for (ModifyApplyLinkVo linkVo : linkVoList) {
                    if ("DOC".equals(linkVo.getLinkClass())) {
                        linkVo.setDocName(idNameMap.get(linkVo.getDocId()));
                        linkVo.setLinkClass(this.baseMapper.getDocTypeByDocId(linkVo.getDocId()));
                    }
                    if (ObjectUtil.isEmpty(linkVo.getDocName())) {
                        linkVo.setDocName(idNameMap.get(linkVo.getLinkId()));
                    }
                }
            }
        }*/

//        if (applyVo.getChangeType().equals(ApplyTypeEnum.DISUSE.name()) && ObjectUtil.isNotEmpty(applyVo.getChangeId())) {
//            DocChangeApply changeApplies = iDocChangeApplyService.getById(applyVo.getChangeId());
//            if (ObjectUtil.isNotEmpty(changeApplies)) {
//                if (ObjectUtil.isEmpty(applyVo.getPreAppendixes())) {
//                    applyVo.setPreAppendixes(new ArrayList<>());
//                }
//                QueryWrapper<BasicFile> queryWrapper = new QueryWrapper<>();
//                queryWrapper.lambda().in(BasicFile::getId, changeApplies.getAppendixs().split(","));
//                List<BasicFile> list = basicFileService.list(queryWrapper);
//                for (BasicFile basicFile : list) {
//                    ModifyApplyLinkVo linkVo = new ModifyApplyLinkVo();
//                    linkVo.setLinkId(basicFile.getId());
//                    linkVo.setDocName(basicFile.getFileName());
//                    linkVo.setId(basicFile.getId());
//                    applyVo.getPreAppendixes().add(linkVo);
//                    applyVo.getAppendixes().add(linkVo);
//                }
//            }
//            if (ObjectUtil.isEmpty(applyVo.getPreStandardDoc())) {
//                QueryWrapper<DocLinkLog> queryWrapper = new QueryWrapper<>();
//                queryWrapper.lambda().eq(DocLinkLog:: getLinkCode, applyVo.getDocId());
//                queryWrapper.lambda().eq(DocLinkLog:: getLinkType, "DOC");
//                List<DocLinkLog> docLinkLogs = docLinkLogService.list(queryWrapper);
//                if (ObjectUtil.isNotEmpty(docLinkLogs)) {
//                    DocLinkLog docLinkLog = docLinkLogs.get(0);
//                    BasicFile file = basicFileService.getById(docLinkLog.getLinkCode());
//                    ModifyApplyLinkVo linkVo = new ModifyApplyLinkVo();
//                    linkVo.setLinkId(file.getId());
//                    linkVo.setDocName(file.getFileName());
//                    linkVo.setId(file.getId());
//                    applyVo.setPreStandardDoc(linkVo);
//                }
//            }
//            if (ObjectUtil.isEmpty(applyVo.getStandardDoc())&&!ObjectUtil.equal(applyVo.getChangeType(),ApplyTypeEnum.DISUSE.name())) {
//                QueryWrapper<DocLinkLog> queryWrapper = new QueryWrapper<>();
//                queryWrapper.lambda().eq(DocLinkLog:: getLinkCode, applyVo.getDocId());
//                queryWrapper.lambda().eq(DocLinkLog:: getLinkType, "DOC");
//                List<DocLinkLog> docLinkLogs = docLinkLogService.list(queryWrapper);
//                if (ObjectUtil.isNotEmpty(docLinkLogs)) {
//                    DocLinkLog docLinkLog = docLinkLogs.get(0);
//                    BasicFile file = basicFileService.getById(docLinkLog.getLinkCode());
//                    ModifyApplyLinkVo linkVo = new ModifyApplyLinkVo();
//                    linkVo.setLinkId(file.getId());
//                    linkVo.setDocName(file.getFileName());
//                    linkVo.setId(file.getId());
//                    applyVo.setStandardDoc(linkVo);
//                }
//            }
//        }
        //if (applyVo.getChangeType().equals(ApplyTypeEnum.ADD.name()) && ObjectUtil.isNotEmpty(applyVo.getChangeId())) {
        //    applyVo.setTrainDept(null);
        //}
        /**
         * 培训记录
         */
        queryModifyApplyTrain(applyVo, deptIdList, userNameList, oldApplyId);

        /**
         * 当前版本
         */
        //applyVo.setFile(standardService.getVoById(applyVo.getDocId()));

        /**
         * 历史版本
         */
//        VersionBo versionBo = new VersionBo();
//        versionBo.setApplyId(id);
//        if (oldApplyId.size()>0){
//            List<VersionVo> versionVoList = versionService.selectVersionList(oldApplyId);
//            if (CollectionUtil.isNotEmpty(versionVoList)) {
//                applyVo.setVersions(versionVoList);
//            }
//        }

        /**
         * 统一补充用户昵称和部门名称
         */
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getUserName, userNameList);
        Map<String, String> userNameNickMap = Optional.ofNullable(sysUserMapper.selectList(queryWrapper)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));
        Map<String, String> deptIdNameMap = Optional.ofNullable(sysDeptService.listByIds(deptIdList)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        applyVo.setDeptName(deptIdNameMap.get(applyVo.getDeptId()));
        applyVo.setNickName(userNameNickMap.get(applyVo.getUserName()));

        if (CollectionUtil.isNotEmpty(applyVo.getDistributeDepths())) {
            for (ModifyApplyDistributeVo vo : applyVo.getDistributeDepths()) {
                vo.setDeptName(deptIdNameMap.get(vo.getDeptId()));
            }
        }
        if (CollectionUtil.isNotEmpty(applyVo.getTrains())) {
            for (ModifyApplyTrainVo vo : applyVo.getTrains()) {
                vo.setDeptName(deptIdNameMap.get(vo.getDeptId()));
                vo.setNickName(userNameNickMap.get(vo.getUserName()));
            }
        }



        return applyVo;
    }

    /**
     * 查询培训记录
     *
     * @param applyVo
     */
    private void queryModifyApplyTrain(ModifyApplyVo applyVo, List<String> deptIdList, Collection<String> userNameList, List<String> oldApplyId) {
        //修订不需要查出培训记录
        //if (applyVo.getChangeType().equals(ApplyTypeEnum.UPDATE.name()) && ObjectUtil.isNotEmpty(applyVo.getChangeId())) {
        //    return;
        //}
        ModifyApplyTrainBo trainBo = new ModifyApplyTrainBo();
        trainBo.setApplyId(applyVo.getId());
        trainBo.setIsDeleted(0);
        List<ModifyApplyTrainVo> trainVoList = modifyApplyTrainService.queryList(trainBo);
//        if (ObjectUtil.isNotEmpty(applyVo.getChangeId()) && !Objects.equals("ADD", applyVo.getChangeType()) && ObjectUtil.isNotEmpty(oldApplyId)) {
//            trainBo.setApplyId(null);
//            for (String s : oldApplyId) {
//                trainBo.setApplyId(s);
//                List<ModifyApplyTrainVo> trainVoList2 = modifyApplyTrainService.queryList(trainBo);
//                trainVoList.addAll(trainVoList2);
//            }
//        }
        if (CollectionUtil.isNotEmpty(trainVoList)) {
            for (ModifyApplyTrainVo trainVo : trainVoList) {
                deptIdList.add(trainVo.getDeptId());
                userNameList.add(trainVo.getUserName());
                if (ObjectUtil.isNotEmpty(trainVo.getFileIds())) {
                    List<BasicFileVo> fileVoList = basicFileService.listVoByIds(Arrays.asList(trainVo.getFileIds().split(ID_SPLIT_KEY)));
                    fileVoList.stream().forEach(file->{
                        file.setLinkId(file.getId());
                    });
                    trainVo.setFiles(fileVoList);
                }
            }
            applyVo.setTrains(trainVoList);
        }
    }

    @Override
    @DataScope(deptAlias = "d")
    public TableDataInfo<ModifyApplyVo> queryPageList(ModifyApplyBo bo) {
        //原来的程序写法
        //PagePlus<ModifyApply, ModifyApplyVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        Page<ModifyApply> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<ModifyApply> result = this.baseMapper.getApplyModifyList(page,bo);
        List<ModifyApplyVo> modifyApplyVoList = new ArrayList<>();
        /**
         * 查询流程状态
         */
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            Set<String> idSet = result.getRecords().stream().map(r -> r.getId()).collect(Collectors.toSet());
            List<WorkflowApplyLogVo> voList = workflowApplyLogService.listVoByIds(idSet);
            if (CollectionUtil.isNotEmpty(voList)) {
                Map<String, String> idStatusMap = new HashMap<>();
                for (WorkflowApplyLogVo vo : voList) {
                    if("NEW".equals(vo.getActDefName())) {
                        vo.setActDefName("进行中");
                    }else if(org.apache.commons.lang3.StringUtils.isEmpty(vo.getActDefName())) {
                        vo.setActDefName("已办结");
                    }

//                    idStatusMap.put(vo.getId(), vo.getActDefName());
                    idStatusMap.put(vo.getId(),vo.getProcStatus());
                }

                ModifyApplyVo modifyApplyVo;
                for (ModifyApply vo : result.getRecords()) {
                    modifyApplyVo = new ModifyApplyVo();
                    BeanUtils.copyProperties(vo,modifyApplyVo);

//                    modifyApplyVo.setProcessStatus(idStatusMap.get(vo.getId()));
                    modifyApplyVo.setProcessStatus(RecordStatusEnum.getMsg(idStatusMap.get(vo.getId())));
                    //文件变更 查询列表信息 如果数据中有修订变更
                    if (ObjectUtil.equals(modifyApplyVo.getChangeType(),ApplyTypeEnum.UPDATE.name())){
                        //因为modify表中存的versionValue是提交申请时的版本号(旧版本的)
                        //变更列表这里的查询显示 要显示变更后的版本信息 所以直接插version表 拿到这个文档最新版本
                        LambdaQueryWrapper<ModifyApply> queryWrapper = Wrappers.lambdaQuery();
                        queryWrapper.eq(ModifyApply::getId,modifyApplyVo.getId());
                        ModifyApply one = this.getOne(queryWrapper);
                        if (ObjectUtil.isNotEmpty(one)){
                            modifyApplyVo.setVersionValue(one.getVersionValue());
                        }
                    }
                    modifyApplyVoList.add(modifyApplyVo);
                }
            }
        }

        TableDataInfo<ModifyApplyVo> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        rspData.setRows(modifyApplyVoList);
        rspData.setTotal(page.getTotal());
        return rspData;
    }

    @Override
    public List<ModifyApplyVo> queryList(ModifyApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    public static void main(String[] args) {
        String formatValue = String.format("%02d", 9);
        System.out.println("formatValue = " + formatValue);

        System.out.println(Integer.parseInt("02") + "------------------");
    }

    @Override
    public ModifyApplyResultVo insertBoNoBpm(ModifyApplyBo bo) {
        ModifyApplyResultVo resultVo = new ModifyApplyResultVo();
        if(StrUtil.isBlank(bo.getUserName())) {
            bo.setUserName(SecurityUtils.getUsername());
        }
        if(ObjectUtil.isNull(bo.getDeptId()) || bo.getDeptId()== "0") {
            bo.setDeptId(SecurityUtils.getDeptId());
        }
        LambdaQueryWrapper<ModifyApply> lambdaQueryWrapper = new LambdaQueryWrapper();
        List<ModifyApplyVo> oldApplyVoList = this.listVo(lambdaQueryWrapper.eq(ModifyApply::getDocId, bo.getDocId()));
        String formatValue = ObjectUtil.isNotEmpty(bo.getVersionValue())?String.format("%02d", Integer.parseInt(bo.getVersionValue())):"01";
        //作废的时候版本号不变
        if (ApplyTypeEnum.DISUSE.name().equals(bo.getChangeType())) {
            formatValue = oldApplyVoList.get(0).getVersionValue();
        } else {
            if (oldApplyVoList != null && oldApplyVoList.size() > 0) {
                String preVersionValue = oldApplyVoList.get(0).getVersionValue();
                formatValue = String.format("%02d", Integer.parseInt(preVersionValue) + 1);
            }
        }

        bo.setVersionValue(formatValue);
        Optional<String> applyIdOpt = insertAllByBo(bo);

        if (applyIdOpt.isPresent()) {

            bo.setId(applyIdOpt.get());
            resultVo.setId(applyIdOpt.get());
            if (!bo.getChangeType().equals("ADD")) {
                if (ObjectUtil.isNotEmpty(oldApplyVoList)) {
                    // 复制分发部门
                    QueryWrapper<ModifyApplyDistribute> distributeQueryWrapper = new QueryWrapper<>();
                    distributeQueryWrapper.lambda().eq(ModifyApplyDistribute::getApplyId, oldApplyVoList.get(0).getId());

                    Map<String, Object> params = new HashMap<>();

                    List<ModifyApplyDistribute> distributeList = modifyApplyDistributeService.list(distributeQueryWrapper);
                    if (ObjectUtil.isNotEmpty(distributeList)) {
                        for (ModifyApplyDistribute modifyApplyDistribute : distributeList) {
                            modifyApplyDistribute.setId(null);
                            modifyApplyDistribute.setApplyId(applyIdOpt.get());

                            if (ApplyTypeEnum.toType(bo.getChangeType()).equals(ApplyTypeEnum.DISUSE)) {
                                modifyApplyDistribute.setIsDeleted(1);
                            }

                            params.put("applyId", applyIdOpt.get());
                            params.put("deptId", modifyApplyDistribute.getDeptId());
                            //如果已经存在记录就不需要复制
                            Integer nums = modifyApplyDistributeService.queryNumsByMap(params);
                            if (nums == null || nums == 0) {
                                modifyApplyDistributeService.save(modifyApplyDistribute);
                            }
                        }
                    }
                    // 复制培训部门,不需要复制培训部门
                    /*QueryWrapper<ModifyApplyTrain> trainQueryWrapper = new QueryWrapper<>();
                    trainQueryWrapper.lambda().eq(ModifyApplyTrain:: getApplyId, oldApplyVoList.get(0).getId());
                    trainQueryWrapper.lambda().eq(ModifyApplyTrain:: getIsDeleted, 0);
                    List<ModifyApplyTrain> trainVoList = modifyApplyTrainService.list(trainQueryWrapper);
                    if (ObjectUtil.isNotEmpty(trainVoList)) {
                        for (ModifyApplyTrain modifyApplyTrain : trainVoList) {
                            modifyApplyTrain.setId(null);
                            modifyApplyTrain.setApplyId(applyIdOpt.get());
                            modifyApplyTrainService.save(modifyApplyTrain);
                        }
                    }*/
                }
            }

        }
        return resultVo;
    }
    //@Transactional(rollbackFor = Exception.class)
    @Override
    public Optional<ModifyApplyResultVo> insertByBo(ModifyApplyBo bo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ModifyApplyResultVo resultVo = this.insertBoNoBpm(bo);
        try {
            BpmClientInputModelBo bpmClientInputModelBo;
            ProcessInstanceModel processInstanceModel;
            if(StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.DRAFT.getCode())){
                //暂存
                bpmClientInputModelBo = new BpmClientInputModelBo();

                ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
                bo.setBpmClientInputModel(bpmClientInputModelBo);
                String procDefKey = ApplyTypeEnum.UNKNOWN.toString();
                /*
                if(ApplyTypeEnum.ADD.toString().equals(bo.getChangeType())){
                    procDefKey = processConfig.getProcDefKeyADD();
                }else if(ApplyTypeEnum.UPDATE.toString().equals(bo.getChangeType())){
                    procDefKey = processConfig.getProcDefKeyUPDATE();
                }else if(ApplyTypeEnum.DISUSE.toString().equals(bo.getChangeType())){
                    procDefKey = processConfig.getProcDefKeyDISUSE();
                }
                */
                if(ApplyTypeEnum.ADD.toString().equals(bo.getChangeType())
                        || ApplyTypeEnum.UPDATE.toString().equals(bo.getChangeType())
                        || ApplyTypeEnum.DISUSE.toString().equals(bo.getChangeType())){
                    // 文件新增、文件修订、文件作废流程根据文件分类设置-流程设置，动态获取流程KEY
                    DocClassFlowVo result = iDocClassFlowService.getByUpDocClassAndBizType(bo.getDocClass(),bo.getChangeType(),"");
                    if(result == null) {
                        throw new Exception("此文件分类"+bo.getDocClass()+"和业务类型"+bo.getChangeType()+"未进行分类流程设置，请联系管理员配置。");
                    }
                    procDefKey = result.getFlowKey();
                }
                ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(
                        procDefKey);
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_sendUserId(loginUser.getUsername());
                bpmClientInputModel.setWf_sendUserOrgId(loginUser.getDeptId().toString());
                bpmClientInputModel.setWf_procDefKey(processDefinitionModel.getProcDefKey());
                bpmClientInputModel.setWf_procDefId(processDefinitionModel.getProcDefId());
                bpmClientInputModel.setWf_businessKey(bo.getId());
                bpmClientInputModel.setWf_procTitle("zancun");
                bpmClientInputModel.setWf_uniteworkUrl(processConfig.getUniteworkUrl());
                // 设置接收人
                List<Map<String,Object>> list = new ArrayList<>();
                Map<String,Object> map = new HashMap<>();
                map.put("receiveUserId",loginUser.getUsername());
                map.put("receiveUserOrgId",loginUser.getDeptId().toString());
                list.add(map);
                bpmClientInputModel.setWf_receivers(list);
                bpmClientInputModelBo.setModel(bpmClientInputModel);
                bpmClientInputModelBo.setApplyStatus(bo.getRecordStatus());
                // 暂存流程平台
                processInstanceModel = workflowService.processProcInst(bpmClientInputModelBo,
                        WorkflowService.ENUM_ACTION.save.name());
            }else {
                // 提交流程 对象已前端组装
                bo.getBpmClientInputModel().getModel().setWf_sendUserId(loginUser.getUsername());
                bo.getBpmClientInputModel().getModel().setWf_sendUserOrgId(loginUser.getDeptId().toString());
                processInstanceModel = workflowService.nextExecute(bo.getBpmClientInputModel(),resultVo.getId());
            }
            // 保存流程日志
            saveWorkFlowLog(bo, processInstanceModel);
            if (ObjectUtil.isNotEmpty(bo.getDocId())) {
                Standard standard = standardService.getById(bo.getDocId());
                if (ObjectUtil.isNotEmpty(standard)) {
                    standard.setLockStatus(NumberConstants.ONE);
                    standardService.updateById(standard);
                }
            }
            resultVo.setProcessInstanceModel(processInstanceModel);
            resultVo.setProcDefId(Optional.ofNullable(processInstanceModel).map(p -> p.getProcDefId()).orElse(""));
            resultVo.setCurActInstId(Optional.ofNullable(processInstanceModel).map(p -> p.getNextActInsts()).map(n -> n.get(0)).map(a -> a.getActInstId()).orElse(""));
            return Optional.ofNullable(resultVo);
        } catch (Exception e) {
            log.error("start modify apply fail:", e);
        }
        return Optional.empty();
    }

    /**
     * 获取工作流定义名称
     */
    private String getProDefKey(String changeType) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        switch (ApplyTypeEnum.toType(changeType)) {
            case ADD:
                return processConfig.getProcDefKeyADD();
            case UPDATE:
                return processConfig.getProcDefKeyUPDATE();
            case DISUSE:
                return processConfig.getProcDefKeyDISUSE();
            default:
                return null;
        }
    }

    /**
     * 保存变更操作数据，包括附件，关联文件，新增或关联记录文件
     *
     * @param bo
     * @return
     */
    //@Transactional(rollbackFor = Exception.class)
    @Override
    public Optional<String> insertAllByBo(ModifyApplyBo bo) {
        ModifyApply applyPo = saveModifyApplyData(bo);
        bo.setId(applyPo.getId()).setVersionId(applyPo.getVersionId()).setVersionValue(applyPo.getVersionValue()).setCreateTime(new Date());
        /**
         * 创建部门分发记录
         */
        if (!ApplyTypeEnum.toType(applyPo.getChangeType()).equals(ApplyTypeEnum.DISUSE)){
            createModifyApplyDistribute(bo);
        }
        /**
         * 构造关联数据：将编制正文，附件文件，关联文件，新增或关联记录文件统一保存
         * 关联文件或记录时间linkId为versionId,其它为basic_file的id
         */
        List<ModifyApplyLink> linkList = buildModifyApplyLink(bo);
        if (ObjectUtil.isNotEmpty(linkList)) {
            modifyApplyLinkService.saveOrUpdateAll(linkList);
        }
        return Optional.ofNullable(applyPo.getId());
    }

    /**
     * 生成分发记录
     */
    private void createModifyApplyDistribute(ModifyApplyBo bo) {
        if (ObjectUtil.isNotEmpty(bo.getDistributeDepths())) {
            List<ModifyApplyDistribute> distributeList = bo.getDistributeDepths().stream().map(distributeBo -> {
                ModifyApplyDistribute distributePo = BeanUtil.toBean(distributeBo, ModifyApplyDistribute.class);
                distributePo.setApplyId(bo.getId());
                return distributePo;
            }).collect(Collectors.toList());
            modifyApplyDistributeService.saveAll(distributeList);
        }
    }

    /**
     * 组装关联记录
     */
    private List<ModifyApplyLink> buildModifyApplyLink(ModifyApplyBo bo) {
        List<ModifyApplyLink> linkList = Lists.newArrayList();
        //编制正文
        if (ObjectUtil.isNotEmpty(bo.getStandardDoc())) {
            bo.setFileId(bo.getStandardDoc().getFileId());
            ModifyApplyLink linkPo = createModifyApplyLink(bo.getStandardDoc().getId(),bo.getId(), bo.getFileId(), bo.getStandardDoc().getLinkId(),LinkClassEnum.HOST.name(), LinkTypeEnum.DOC.name(), bo.getDocName(), bo.getVersionValue(), null, bo.getDocClass(), NumberConstants.ZERO, bo.getDocId());
            linkList.add(linkPo);
        }

        //备注的附件
        buildModifyApplyLink(linkList, bo.getRemarkDoc(), bo.getId(), LinkTypeEnum.APPENDIX_REMARK, LinkClassEnum.FILE);
        //附件文件
        buildModifyApplyLink(linkList, bo.getAppendixes(), bo.getId(),  LinkTypeEnum.APPENDIX, LinkClassEnum.FILE);
        //关联文件
        buildModifyApplyLink(linkList, bo.getDocLinks(), bo.getId(),  LinkTypeEnum.REF_DOC, LinkClassEnum.DOC);
        //新增或关联记录文件
        buildModifyApplyLink(linkList, bo.getRecordLinks(), bo.getId(), LinkTypeEnum.RECORD, LinkClassEnum.LINK);
        return linkList;
    }

    /**
     * 生成多条记录
     */
    private void buildModifyApplyLink(List<ModifyApplyLink> linkList, List<ModifyApplyLinkBo> linkBoList, String applyId, LinkTypeEnum linkTypeEnum, LinkClassEnum linkClassEnum) {
        if (ObjectUtil.isNotEmpty(linkBoList)) {
            List<ModifyApplyLink> appendixList = linkBoList.stream().map(appendixBo ->
                    createModifyApplyLink(appendixBo.getId(),applyId, appendixBo.getFileId(), appendixBo.getLinkId(), linkClassEnum.name(), linkTypeEnum.name(), appendixBo.getDocName(), appendixBo.getVersionValue(), appendixBo.getIsDeleted(), appendixBo.getDocClass(), appendixBo.getStatus(), appendixBo.getDocId())
            ).collect(Collectors.toList());
            linkList.addAll(appendixList);
        }
    }

    /**
     * 创建一条关联记录
     */
    private ModifyApplyLink createModifyApplyLink(String id,String applyId,String fileId,String linkId, String linkClass, String linkType, String docName,String versionValue,Integer isDeleted, String docClass, Integer status,String docId) {
        ModifyApplyLink linkPo = new ModifyApplyLink();
        linkPo.setId(id);
        linkPo.setApplyId(applyId);
        linkPo.setFileId(fileId);
        linkPo.setLinkId(linkId);
        linkPo.setLinkClass(linkClass);
        linkPo.setLinkType(linkType);
        linkPo.setDocName(docName);
        linkPo.setVersionValue(versionValue);
        linkPo.setIsDeleted(isDeleted);
        linkPo.setDocClass(docClass);
        linkPo.setStatus(status);
        linkPo.setDocId(docId);
        return linkPo;
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ModifyApplyBo bo) {
        if (ObjectUtil.isEmpty(bo) || ObjectUtil.isEmpty(bo.getId())) {
            log.error("update with empty id");
            return false;
        }
        ModifyApply update = BeanUtil.toBean(bo, ModifyApply.class);
        Boolean result = updateById(update);
        if (ObjectUtil.isNotEmpty(result) && result) {
            /**
             * 更新分发
             */
            updateDistributeDepths(bo.getId(), bo.getDistributeDepths());
            /**
             * 更新关联
             */
            updateModifyApplyLink(bo);

            /**
             * 更新流程状态及申请结果
             */
            updateModifyLog(bo);
        }
        return result;
    }

    private void updateModifyLog(ModifyApplyBo bo) {
        WorkflowApplyLog workflowApplyLog = workflowApplyLogService.getById(bo.getId());
        workflowApplyLog.setApplyStatus(bo.getRecordStatus());
        workflowApplyLog.setProcStatus(bo.getRecordStatus());
        workflowApplyLogService.updateById(workflowApplyLog);
    }

    /**
     * 更新正文，附件，关联文档，关联或新增附件
     */
    private void updateModifyApplyLink(ModifyApplyBo bo) {
        List<ModifyApplyLink> newLinkList = buildModifyApplyLink(bo);
//        List<String> ids=newLinkList.stream().filter(item->StringUtils.isNotBlank(item.getId())).map(ModifyApplyLink::getId).collect(Collectors.toList());
        //删除传参没有 而数据库有的数据
        LambdaQueryWrapper<ModifyApplyLink> query = Wrappers.lambdaQuery();
        query.eq(ModifyApplyLink::getApplyId,bo.getId());
        /*if (ObjectUtil.isNotEmpty(ids)) {
            query.notIn(ModifyApplyLink::getId,ids);
        }*/
        for (ModifyApplyLink link : newLinkList){
            link.setId(null);
            link.setStatus(NumberConstants.ZERO);
        }
        modifyApplyLinkService.remove(query);
        modifyApplyLinkService.saveOrUpdateAll(newLinkList);
    }

    /**
     * 更新分发
     */
    private void updateDistributeDepths(String applyId, List<ModifyApplyDistributeBo> boList) {
        QueryWrapper<ModifyApplyDistribute> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ModifyApplyDistribute::getApplyId, applyId);
        modifyApplyDistributeService.remove(queryWrapper);
        if (CollectionUtil.isNotEmpty(boList)) {
            List<ModifyApplyDistribute> addDistributeList = new ArrayList<>();
            List<ModifyApplyDistribute> updateDistributeList = new ArrayList<>();
            for (ModifyApplyDistributeBo distributeBo : boList) {
                distributeBo.setApplyId(applyId);
                if (ObjectUtil.isEmpty(distributeBo.getId())) {
                    ModifyApplyDistribute distributePo = BeanUtil.toBean(distributeBo, ModifyApplyDistribute.class);
                    addDistributeList.add(distributePo);
                } else {
                    ModifyApplyDistribute distributePo = modifyApplyDistributeService.getById(distributeBo.getId());
                    if (ObjectUtil.isEmpty(distributePo)) {
                        continue;
                    }
                    distributePo.setIsDeleted(distributeBo.getIsDeleted());
                    distributePo.setDeptId(distributeBo.getDeptId());
                    distributePo.setNums(distributeBo.getNums());
                    updateDistributeList.add(distributePo);
                }
            }
            if (CollectionUtil.isNotEmpty(addDistributeList)) {
                modifyApplyDistributeService.saveAll(addDistributeList);
            }
            if (CollectionUtil.isNotEmpty(updateDistributeList)) {
                modifyApplyDistributeService.updateBatchById(updateDistributeList);
            }
        }
    }

    /**
     * 保存新修废申请记录
     *
     * @param bo
     * @return
     */
    private ModifyApply saveModifyApplyData(ModifyApplyBo bo) {
        ModifyApply add = BeanUtil.toBean(bo, ModifyApply.class);
        validEntityBeforeSave(add);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String applyId = iGenerateIdService.generateApplyId(ApplyTypeEnum.toType(bo.getChangeType()), ObjectUtil.isNotEmpty(bo.getUserName()) ? bo.getUserName() : loginUser.getUsername());
        Date now = new Date();
        add.setApplyTime(new Date());
        if (ObjectUtil.isEmpty(add.getUserName())) {
            add.setUserName(loginUser.getUsername());
        }
        if (ObjectUtil.isEmpty(add.getDeptId())) {
            add.setDeptId(loginUser.getDeptId());
        }
//        add.setRecordStatus(ApplyStatusEnum.APPLIED.getStatus());
        add.setId(applyId);
        add.setApplyTime(now);
        save(add);
        return add;
    }

    /**
     * 保存申请记录
     */
    private void saveWorkFlowLog(ModifyApplyBo bo, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLogBo applyLogBo = BeanUtil.toBean(bo, WorkflowApplyLogBo.class);
        applyLogBo.setId(bo.getId());
        applyLogBo.setApplyClass(bo.getChangeType());
        applyLogBo.setProcDefKey(processInstanceModel.getProcDefId());
        applyLogBo.setProcInstId(processInstanceModel.getProcInstId());
        applyLogBo.setActDefName(StringUtils.equals(bo.getRecordStatus(),RecordStatusEnum.DRAFT.getCode())? "草稿":"NEW");
        applyLogBo.setApplyStatus(ApplyStatusEnum.PROCESSING.getCode());
        if(ProcessStatusConstants.TO_DRAFT.equals(bo.getRecordStatus())){
            applyLogBo.setApplyStatus(ApplyStatusEnum.DRAFT.getCode());
        }
        applyLogBo.setProcStatus(bo.getRecordStatus());
        workflowApplyLogService.insertByBo(applyLogBo);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 开启增修废子流程
     *
     * @param event
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onStartModifyApplyEvent(StartModifyApplyEvent event) {
        if(log.isDebugEnabled()){
            log.debug("onStartModifyApplyEvent====1==="+event.getModifyApplyBo());
        }

        if (ObjectUtil.isEmpty(event) || ObjectUtil.isEmpty(event.getModifyApplyBo())) {
            return;
        }
        /**
         * 查出这个文档的操作记录 申请时间倒序排序 所以list如果不为空 则第一条就是上一次变更的数据
         */
        List<String> oldApplyId = this.baseMapper.getApplyIdByDocId(event.getModifyApplyBo().getDocId());
        //如果不为null 说明之前有操作过该文件
        if (ObjectUtil.isNotEmpty(oldApplyId)) {
            //拿到上一次的操作数据
            ModifyApply modifyApply = this.baseMapper.selectById(oldApplyId.get(0));
            //如果上一次数据不为空且本次操作不是新增操作 将上一次的培训部门信息带到这一次操作的对象中
            if (ObjectUtil.isNotEmpty(modifyApply)&&!event.getModifyApplyBo().getChangeType().equals(ApplyTypeEnum.ADD.name())) {
                event.getModifyApplyBo().setYNTrain(modifyApply.getYNTrain());
                event.getModifyApplyBo().setTrainDept(modifyApply.getTrainDept());
            }
        }
        //数据库保存这次操作的数据 目前已经有了上一次的培训部门(如果有的话)
        // 查询上一次关联的备注附件
        if(ApplyTypeEnum.CHANGE.toString().equals(event.getFireApplyType())) {
            //查到上一版本的关联文件 关键记录 变更文件 变更附件 以及变更申请的备注附件
            //备注附件
            List<ModifyApplyLinkVo> remarks = modifyApplyLinkService.queryDocByApplyIdAndType(event.getFireApplyId(), LinkTypeEnum.APPENDIX_REMARK);
            //关联文件
            List<ModifyApplyLinkVo> refDocList = modifyApplyLinkService.queryDocByApplyIdAndType(oldApplyId.get(0), LinkTypeEnum.REF_DOC);
            //关联记录
            List<ModifyApplyLinkVo> recordList = modifyApplyLinkService.queryDocByApplyIdAndType(oldApplyId.get(0), LinkTypeEnum.RECORD);
            //当前变更版本
            List<ModifyApplyLinkVo> docList = modifyApplyLinkService.queryDocByApplyIdAndType(oldApplyId.get(0), LinkTypeEnum.DOC);
            //当前变更版本附件
            List<ModifyApplyLinkVo> appendixList = modifyApplyLinkService.queryDocByApplyIdAndType(oldApplyId.get(0), LinkTypeEnum.APPENDIX);

            switch (ApplyTypeEnum.toType(event.getModifyApplyBo().getChangeType())){
                case UPDATE:
                    //修订只要复制上一版本关联文件 关联记录 和备注附件即可
                    //变更申请的备注附件 放到这次申请的备注附件中
                    if (ObjectUtil.isNotEmpty(remarks)) {
                        //设为null
                        remarks.forEach(remark->{remark.setId(null);});
                        event.getModifyApplyBo().setRemarkDoc(BeanUtil.copyToList(remarks, ModifyApplyLinkBo.class));
                    }
                    //关联文件
                    if (ObjectUtil.isNotEmpty(refDocList)) {
                        //设为null
                        refDocList.forEach(refDoc -> {
                            refDoc.setId(null);
                        });
                        event.getModifyApplyBo().setDocLinks(BeanUtil.copyToList(refDocList, ModifyApplyLinkBo.class));
                    }
                    //关联记录
                    if (ObjectUtil.isNotEmpty(recordList)) {
                        //设为null
                        recordList.forEach(record->{record.setId(null);});
                        event.getModifyApplyBo().setRecordLinks(BeanUtil.copyToList(recordList, ModifyApplyLinkBo.class));
                    }
                    break;
                case DISUSE:
                    //作废则需要复制上一版本的变更文件和附件加上关联文件和关联记录以及备注附件这些了
                    //变更版本主文件
                    if (ObjectUtil.isNotEmpty(docList)) {
                        //设为null
                        docList.forEach(doc->{doc.setId(null);});
                        event.getModifyApplyBo().setStandardDoc(BeanUtil.toBean(docList.get(0),ModifyApplyLinkBo.class));
                    }
                    //变更版本附件文件
                    if (ObjectUtil.isNotEmpty(appendixList)) {
                        //设为null
                        appendixList.forEach(appendix->{appendix.setId(null);});
                        event.getModifyApplyBo().setAppendixes(BeanUtil.copyToList(appendixList,ModifyApplyLinkBo.class));
                    }
                    //变更申请的备注附件 放到这次申请的备注附件中
                    if (ObjectUtil.isNotEmpty(remarks)) {
                        //设为null
                        remarks.forEach(remark->{remark.setId(null);});
                        event.getModifyApplyBo().setRemarkDoc(BeanUtil.copyToList(remarks, ModifyApplyLinkBo.class));
                    }
                    //关联文件
                    if (ObjectUtil.isNotEmpty(refDocList)) {
                        //设为null
                        refDocList.forEach(refDoc->{refDoc.setId(null);});
                        event.getModifyApplyBo().setDocLinks(BeanUtil.copyToList(refDocList, ModifyApplyLinkBo.class));
                    }
                    //关联记录
                    if (ObjectUtil.isNotEmpty(recordList)) {
                        //设为null
                        recordList.forEach(record->{record.setId(null);});
                        event.getModifyApplyBo().setRecordLinks(BeanUtil.copyToList(recordList, ModifyApplyLinkBo.class));
                    }
                    break;
                default:
                    //不为上面的修订或者作废 说明是新增或者其他 就复制变更申请的备注附件就行了
                    //备注附件
                    if (ObjectUtil.isNotEmpty(remarks)) {
                        //设为null
                        remarks.forEach(remark->{remark.setId(null);});
                        event.getModifyApplyBo().setRemarkDoc(BeanUtil.copyToList(remarks, ModifyApplyLinkBo.class));
                    }
                    break;
            }

        }
        Optional<String> applyIdOpt = insertAllByBo(event.getModifyApplyBo());

        if (applyIdOpt.isPresent()) {
            //只有非新增的操作 才需要带出上一次的信息 新增的就不用带出
            if (!event.getModifyApplyBo().getChangeType().equals(ApplyTypeEnum.ADD.name())) {
                if (ObjectUtil.isNotEmpty(oldApplyId)) {
                    // 复制上一次的分发部门
                    QueryWrapper<ModifyApplyDistribute> distributeQueryWrapper = new QueryWrapper<>();
                    distributeQueryWrapper.lambda().eq(ModifyApplyDistribute:: getApplyId, oldApplyId.get(0));
                    List<ModifyApplyDistribute> distributeList = modifyApplyDistributeService.list(distributeQueryWrapper);
                    if (ObjectUtil.isNotEmpty(distributeList)) {
                        for (ModifyApplyDistribute modifyApplyDistribute : distributeList) {
                            modifyApplyDistribute.setId(null);
                            modifyApplyDistribute.setApplyId(applyIdOpt.get());
                            modifyApplyDistributeService.save(modifyApplyDistribute);
                        }
                    }

                    // 复制上一次的文件记录
                    //QueryWrapper<ModifyApplyLink> linkQueryWrapper = new QueryWrapper<>();
                    //linkQueryWrapper.lambda().eq(ModifyApplyLink:: getApplyId, oldApplyId.get(0));
                    //List<ModifyApplyLink> linkList = modifyApplyLinkService.list(linkQueryWrapper);
                    //if (ObjectUtil.isNotEmpty(linkList)) {
                    //    for (ModifyApplyLink modifyApplyLink : linkList) {
                    //        modifyApplyLink.setId(null);
                    //        modifyApplyLink.setIsDeleted(0);
                    //        modifyApplyLink.setApplyId(applyIdOpt.get());
                    //        if (modifyApplyLink.getLinkType().equals("DOC") || modifyApplyLink.getLinkType().equals("APPENDIX")) {
                    //            modifyApplyLink.setLinkType(modifyApplyLink.getLinkType() + "_1");
                    //        }
                    //    }
                    //    modifyApplyLinkService.saveAll(linkList);
                    //}
                }
            }

            /**
             * 保存到关联表doc_apply_relation
             */
            ApplyRelation applyRelation = new ApplyRelation();
            applyRelation.setApplyId(applyIdOpt.get()).setApplyType(event.getModifyApplyBo().getChangeType()).setUpdateTime(new Date())
                    .setRelationApplyId(event.getFireApplyId()).setRelationApplyType(event.getFireApplyType()).setCreateTime(new Date());
            applyRelationService.save(applyRelation);
            /**
             * 启动工作流到提交申请步骤
             */
            event.getModifyApplyBo().setId(applyIdOpt.get());
            //LoginUser loginUser = SecurityUtils.getLoginUser();
            // start modify apply
            try {
                if(log.isDebugEnabled()){
                    log.debug("onStartModifyApplyEvent====2==="+event.getModifyApplyBo());
                }
                ProcessInstanceModel processInstanceModel = workflowService.startProcess(event.getModifyApplyBo().getDocName(),
                        getProDefKey(event.getModifyApplyBo().getChangeType()), event.getModifyApplyBo().getUserName(), String.valueOf(event.getModifyApplyBo().getDeptId()), applyIdOpt.get());
//                BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
//                bpmClientInputModelBo.setApplyStatus();
//                event.getModifyApplyBo().setBpmClientInputModel(bpmClientInputModelBo);
                saveWorkFlowLog(event.getModifyApplyBo(), processInstanceModel);
            } catch (Exception e) {
                log.error("启动工作流失败:", e);
            }
        }
    }


    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    //@Transactional(rollbackFor = Exception.class)
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key ->
                (key.contains(processConfig.getProcDefKeyADD()) || key.contains(processConfig.getProcDefKeyUPDATE()) || key.contains(processConfig.getProcDefKeyDISUSE()))).isPresent()) {
            return;
        }
        ModifyApply applyPo = getById(event.getApplyId());
        if (ObjectUtil.isEmpty(applyPo)) {
            return;
        }
        applyPo.setSetupTime(event.getSetupTime());
        logger.info("流程状态onProcessEvent返回值:{}",event.toString());
        String status = event.getStatus();

        if (Objects.equals(String.valueOf(ProcessStatusConstants.TO_DONE), event.getStatus())) {
            if (ObjectUtil.isNotEmpty(applyPo.getDocId())) {
                Standard standard = standardService.getById(applyPo.getDocId());
                if (ObjectUtil.isNotEmpty(standard)) {
                    standard.setLockStatus(NumberConstants.ZERO);
                    standardService.updateById(standard);
                }
            }
        }

        try {
            //流程结束
            if (ProcessStatusConstants.TO_DONE.equals(event.getStatus())) {
                switch (ApplyTypeEnum.toType(applyPo.getChangeType())) {
                    case ADD:
                        handleAddApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    case UPDATE:
                        handleUpdateApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    case DISUSE:
                        handleDisuseApplySuccess(applyPo, status, event.getModel().getWf_nextActDefName());
                        break;
                    default:
                        break;
                }
            } else {
                applyPo.setRecordStatus(status);
                updateById(applyPo);
                workflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, event.getModel().getWf_nextActDefName(),event.getApplyStatus());
            }
        }catch (Exception e) {
            logger.error("工作流调用失败：",e);
        }
    }

    /**
     * 新增文档流程成功后:
     * 1.创建文件，文件版本
     * 2.若新增附件需要创建附件文件和版本
     * 3.编制文件，文件附件，关联文件，关联记录进行关联
     * 4.文件分发到文件签收,文件打印
     * 5.更新申请状态和文件ID
     * 6.更新申请流程日志信息
     */
    private void handleAddApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        if (YNEnum.toType(applyPo.getYNDistribute()) == YNEnum.YES) {
            handleUpdateApplyStatus(applyPo.getDocId(), applyPo, null, status, actDefName);
        } else {
            //更新状态
            String docId = applyPo.getDocId();
//            Standard standard = BeanUtil.toBean(applyPo, Standard.class);
//            standard.setId(docId);
//            ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
//            linkBo.setIsDeleted(0);
//            linkBo.setApplyId(applyPo.getId());
//            linkBo.setLinkType("DOC");
//            List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryList(linkBo);
//            if (ObjectUtil.isNotEmpty(linkVoList)) {
//                standard.setFileId(linkVoList.get(0).getLinkId());
//            }
//            standardService.save(standard);

            Version version = handleCreateVersion(applyPo, docId, ApplyTypeEnum.ADD);
            //关联
            handApplyLink(applyPo.getId(), null, version.getId());
            //分发
            applyPo.setYNDistribute(YNEnum.YES.name());
            handleDistribute(applyPo, version);
            //培训记录
            handleApplyTrain(applyPo.getId(), docId, version.getId());
            //更新状态
            handleUpdateApplyStatus(docId, applyPo, version, status, actDefName);
            docMessageService.insertMessageByValidity(applyPo);

        }
    }

    /**
     * 修订文档流程成功后:
     * 1.旧版本失效
     * 2.新版本生效
     * 3.线下上交旧版本文件，存储文件回收记录
     * 4.若新增附件需要创建附件文件和版本
     * 5.编制文件，文件附件，关联文件，关联记录进行关联
     * 6.文件分发到文件签收,文件打印
     * 7.更新申请状态和文件ID
     * 8.更新申请流程日志信息
     */
    private void handleUpdateApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        if (ObjectUtil.isEmpty(applyPo) || ObjectUtil.isEmpty(applyPo.getDocId())) {
            log.error("handle update apply error: no id");
            return;
        }
        // 删除分发记录
        //iDocDistributeLogService.updateByDocId(applyPo.getDocId());
        String docId = applyPo.getDocId();
        //失效上一个版本
        QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Version:: getDocId, applyPo.getDocId());
        queryWrapper.lambda().eq(Version:: getStatus, NumberConstants.ONE);
        List<Version> list = versionService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            for (Version version : list) {
                version.setStatus(CommonStatusEnum.INVALID.getStatus());
                version.setEndDate(new Date());
                versionService.updateById(version);
            }

            handleRecovery(applyPo.getId(), applyPo.getDocId(), list.get(0),applyPo);
        }
        //生成一个新版本
        Version newVersion = handleCreateVersion(applyPo, applyPo.getDocId(), ApplyTypeEnum.UPDATE);
        //关联
        handApplyLink(applyPo.getId(), applyPo.getVersionId(), newVersion.getId());
        //分发
        applyPo.setYNDistribute(YNEnum.YES.name());
        String oldVersion = applyPo.getVersionValue();
        handleDistribute(applyPo, newVersion);
        //培训记录
        handleApplyTrain(applyPo.getId(), docId, newVersion.getId());
        //更新状态
        handleUpdateApplyStatus(docId, applyPo, newVersion, status, actDefName);

        applyPo.setVersionId(newVersion.getId());
        this.baseMapper.updateById(applyPo);
        docMessageService.insertMessageByValidity(applyPo);
        //生成待回收消息 获取失效版本的最新一次版本号
        QueryWrapper<Version> invalidQueryWrapper = new QueryWrapper<>();
        invalidQueryWrapper.lambda().eq(Version:: getDocId, applyPo.getDocId());
        invalidQueryWrapper.lambda().eq(Version:: getStatus, CommonStatusEnum.INVALID.getStatus());
        List<Version> invalidList = versionService.list(invalidQueryWrapper);
        if(ObjectUtil.isNotEmpty(invalidList)) {
            List<Version> newList = invalidList.stream().sorted(Comparator.comparing(Version::getUpdateTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
            applyPo.setVersionValue(newList.get(0).getVersionValue());
        }
        docMessageService.insertMessageByReceive(applyPo);
    }

    /**
     * 作废文档流程成功后：
     * 1.文件版本失效
     * 2.线下上交旧版本文件,存储文件回收记录
     */
    private void handleDisuseApplySuccess(ModifyApply applyPo, String status, String actDefName) {
        if (ObjectUtil.isEmpty(applyPo) || ObjectUtil.isEmpty(applyPo.getDocId())) {
            log.error("handle disuse apply error: no id");
            return;
        }
        StandardVo standard = standardService.getStandardVoByVersionId(applyPo.getVersionId());
        if (ObjectUtil.isEmpty(standard)) {
            return;
        }
        // 删除分发记录
        //  iDocDistributeLogService.updateByDocId(applyPo.getDocId());

        //失效关联文件
        List <DocVersionLink> dvlList = iDocVersionLinkService.list(new LambdaQueryWrapper<DocVersionLink>().eq(DocVersionLink::getVersionId,applyPo.getVersionId()));
        List <String> linkIds= dvlList.stream().map(DocVersionLink::getLinkId).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(linkIds)) {
            docLinkLogService.update(new LambdaUpdateWrapper<DocLinkLog>().in(DocLinkLog::getId,linkIds).set(DocLinkLog::getStatus,Constants.TWO));
        }

        //失效当前版本
        //失效上一个版本
        QueryWrapper<Version> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Version:: getDocId, applyPo.getDocId());
        queryWrapper.lambda().eq(Version:: getStatus, NumberConstants.ONE);
        List<Version> list = versionService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            for (Version version : list) {
                version.setStatus(CommonStatusEnum.INVALID.getStatus());
                version.setEndDate(new Date());
                versionService.updateById(version);
            }

            handleRecovery(applyPo.getId(), applyPo.getDocId(), list.get(0),applyPo);
            applyPo.setVersionId(list.get(0).getId());
            this.baseMapper.updateById(applyPo);
        }

        //失效文件
        if (ObjectUtil.isNotEmpty(standard)) {
            standard.setStatus(CommonStatusEnum.INVALID.getStatus());
            standardService.updateById(BeanUtil.toBean(standard,Standard.class));
        }
        //更新状态
        handleUpdateApplyStatus(applyPo.getDocId(), applyPo, list.get(0), status, actDefName);
        //生成文件待回收消息给部门文件管理员
        docMessageService.insertMessageByReceive(applyPo);

    }


    /**
     * 生成文件回收记录
     *
     * @param applyId
     * @param docId
     * @param version 这个就是旧版本
     */
    private void handleRecovery(String applyId, String docId, Version version,ModifyApply applyPo) {
//        QueryWrapper<DocDistributeLog> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(DocDistributeLog::getDocId,docId);
//        queryWrapper.lambda().eq(DocDistributeLog::getDocId,docId);
//        queryWrapper.lambda().groupBy(DocDistributeLog::getDeptId);
        //从分发表中查出这个文件的这个版本的所有分发数据
        List<DocDistributeLogVo> list = iDocDistributeLogService.getDocDistributeNumsByDocId(docId,version.getId());


        //ArrayList<RecoveryLog> recoveryLogs = new ArrayList<>();
        list.forEach(vo->{
            //一条分发记录 表示一个文档的一个版本和一个分发部门 生成一条回收记录
            RecoveryLog recoveryLog = new RecoveryLog();
            recoveryLog.setId(null);
            recoveryLog.setDocName(vo.getDocName());
            recoveryLog.setDocId(docId);
            recoveryLog.setDocClass(vo.getDocClass());
            recoveryLog.setDistributeTime(vo.getDistributeTime());
            recoveryLog.setVersionId(vo.getVersionId());
            recoveryLog.setVersionValue(vo.getVersionValue());
            recoveryLog.setApplyId(applyPo.getId());
            recoveryLog.setDeptId(vo.getDeptId());
            recoveryLog.setDeptName(vo.getDeptName());
            recoveryLog.setCompileDeptId(vo.getCompileDeptId());
            recoveryLog.setCompileDeptName(vo.getCompileDeptName());
            recoveryLog.setCreateTime(new Date());
            recoveryLog.setUpdateTime(null);
            recoveryLog.setRecyclNum(0);
            recoveryLog.setNoRecyclNum(vo.getTotalNums());
            iRecoveryLogService.save(recoveryLog);

            //查出这个版本的这个部门的所有分发号
            LambdaQueryWrapper<DistributeItem> wrapper = new LambdaQueryWrapper<>();
            //分发部门
            wrapper.eq(DistributeItem::getDeptId, vo.getDeptId());
            wrapper.eq(DistributeItem::getVersionId, version.getId());
            List<DistributeItemVo> itemVoList = iDistributeItemService.listVo(wrapper);
            //有多少条分发号 就生成多少条回收记录
            itemVoList.forEach(itemVo->{
                RecoveryLogItem item = new RecoveryLogItem();
                item.setId(null);
                item.setDocId(vo.getDocId());
                item.setVersionId(vo.getVersionId());
                item.setApplyId(applyPo.getId());
                item.setDeptId(vo.getDeptId());
                item.setDeptName(vo.getDeptName());
                item.setRecoveryId(recoveryLog.getId());
                //这个分发号要插入 但是不知道从哪里插入 先暂时置空
                item.setDistributeItemId(itemVo.getId());
                //item.setUserName();
                //item.setNickName();
                item.setStatus("N");
                item.setChangeType(vo.getChangeType());
                item.setCreateTime(new Date());
                iRecoveryLogItemService.save(item);
            });
        });
        //iRecoveryLogService.insertListAndUpdateItem(recoveryLogs);
        /*ModifyApplyDistributeBo distributeBo = new ModifyApplyDistributeBo();
        distributeBo.setApplyId(applyId);
        distributeBo.setIsDeleted(0);

        //作废的分发数据
        if(ApplyTypeEnum.DISUSE.name().equals(applyPo.getChangeType())) {
            distributeBo.setIsDeleted(1);
        }

        if(ApplyTypeEnum.toType(applyPo.getChangeType()).equals(ApplyTypeEnum.DISUSE)) {
            distributeBo.setIsDeleted(1);
        }

        List<ModifyApplyDistributeVo> distributeVoList = modifyApplyDistributeService.queryList(distributeBo);

        if (ObjectUtil.isNotEmpty(distributeVoList)) {
            RecoveryLogBo bo;
            Map<String,Object> params = new HashMap<>();
            for (ModifyApplyDistributeVo modifyApplyDistributeVo : distributeVoList) {
                params.put("applyId",version.getApplyId());
                params.put("deptId",modifyApplyDistributeVo.getDeptId());

                bo = new RecoveryLogBo();
                //上一个生效版本的值
                bo.setApplyId(version.getApplyId());
                bo.setDocId(docId);
                bo.setDeptId(modifyApplyDistributeVo.getDeptId());
                bo.setVersionId(version.getId());
                bo.setStatus(NumberConstants.ZERO + "");
                bo.setNoRecyclNum(modifyApplyDistributeService.queryNumsByMap(params));
                bo.setRecyclNum(NumberConstants.ZERO);
                bo.setCompileUserName(applyPo.getUserName());
                bo.setCompileDeptId(modifyApplyDistributeVo.getDeptId());
                iRecoveryLogService.insertByBoFromDistribute(bo);
            }
        }*/

    }

    /**
     * 更新培训记录
     */
    private void handleApplyTrain(String applyId, String docId, String versionId) {
        ModifyApplyTrainBo trainBo = new ModifyApplyTrainBo();
        trainBo.setApplyId(applyId);
        List<ModifyApplyTrainVo> trainVoList = modifyApplyTrainService.queryList(trainBo);
        if (CollectionUtil.isNotEmpty(trainVoList)) {
            List<ModifyApplyTrain> trainList = new ArrayList<>();
            for (ModifyApplyTrainVo trainVo : trainVoList) {
                ModifyApplyTrain train = BeanUtil.toBean(trainVo, ModifyApplyTrain.class);
                train.setDocId(docId).setVersionId(versionId);
                trainList.add(train);
            }
            modifyApplyTrainService.saveOrUpdateAll(trainList);
        }
    }

    /**
     * 失效版本
     */
    private Version invalidVersion(String versionId) {
        Version version = versionService.getById(versionId);
        if (ObjectUtil.isNotEmpty(version)) {
            version.setStatus(CommonStatusEnum.INVALID.getStatus());
            version.setEndDate(new Date());
            versionService.updateById(version);
        }
        return version;
    }

    /**
     * 创建一个新的版本
     *
     * @param applyPo
     * @param docId
     * @return
     */
    private Version handleCreateVersion(ModifyApply applyPo, String docId, ApplyTypeEnum applyTypeEnum) {
        Version version = new Version();
        version.setStartDate(applyPo.getSetupTime());
        DocClassVo docClassConfig = this.getDocClassConfig(applyPo.getDocClass());
        long expiration = ObjectUtil.isNotEmpty(applyPo.getExpiration()) &&
                applyPo.getExpiration() > 0 ?
                applyPo.getExpiration() : docClassConfig.getExpiration();
        if("'true'".equals(docClassConfig.getOpenPrescription())){
            Date date = DateUtil.offset(version.getStartDate(),DateField.YEAR,docClassConfig.getExpiration().intValue());
            version.setEndDate((DateUtil.offset(date,DateField.DAY_OF_MONTH,-1)));
            // 文件失效日期为永久时
            if(NumberConstants.ZERO.equals(docClassConfig.getExpiration().intValue())){
                version.setForever(String.valueOf(NumberConstants.ONE));
            }
        }
        version.setApplyId(applyPo.getId()).setDocId(docId);
        version.setVersionValue(applyPo.getVersionValue()).setStatus(CommonStatusEnum.VALID.getStatus());
        version.setTrainDept(applyPo.getTrainDept());
        version.setContent(applyPo.getContent());
        version.setRemark(applyPo.getRemark());
        version.setChangeFactor(applyPo.getChangeFactor());
        version.setChangeReason(applyPo.getChangeReason());
        //流程走到这里 获取从DocSignatureUtils.download方法中传入的缓存值 拿到签章之后的文件id
        String encryptFileId = null;
        if (ObjectUtil.isNotEmpty(docId)) {
            encryptFileId = SpringUtils.getBean(RedisCache.class).getCacheObject(docId);
            version.setEncryptFileId(encryptFileId);
        }
        if("'true'".equals(docClassConfig.getOpenReview())) {
            version.setReviewTime(DateUtil.offset(DateUtils.addYears(new Date(),docClassConfig.getReviewCycle().intValue()),DateField.DAY_OF_MONTH,-1));
        }


        //更新DocStandard
        /*StandardBo bo = new StandardBo();
        bo.setEncryptFileId(encryptFileId);
        bo.setId(docId);

        if(bo.getEncryptFileId()!=null) {
            standardService.updateByBo(bo);
        }*/
        //ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
        //linkBo.setIsDeleted(0);
        //linkBo.setApplyId(applyPo.getId());
        //linkBo.setLinkType("DOC");
        List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryDocByApplyIdAndType(applyPo.getId(),LinkTypeEnum.DOC);

        //创建standard文件
        if (ApplyTypeEnum.ADD.equals(applyTypeEnum)){
            //新增文件则插入数据
            Standard standard = BeanUtil.toBean(applyPo, Standard.class);
            standard.setId(null);
            standard.setExpiration(Long.valueOf(expiration));
            standard.setChangeFactor(applyPo.getChangeFactor());
            standard.setChangeReason(applyPo.getChangeReason());
            standard.setEncryptFileId(encryptFileId);
            if (ObjectUtil.isNotEmpty(linkVoList)) {
                standard.setFileId(linkVoList.get(0).getFileId());
                version.setFileId(linkVoList.get(0).getFileId());
            }
            standardService.save(standard);
            //更新doc_version 表的standardId
            version.setStandardId(standard.getId());
            //version.setStandardId(standard.getId());
            //this.baseMapper.updateById(applyPo);
        }else if (ApplyTypeEnum.UPDATE.equals(applyTypeEnum)){
            VersionVo voById = versionService.getVoById(applyPo.getVersionId());
            version.setStandardId(voById.getStandardId());
            if (ObjectUtil.isNotEmpty(linkVoList)) {
                version.setFileId(linkVoList.get(0).getFileId());
            }
        }
        versionService.save(version);
        return version;
    }

    private DocClassVo getDocClassConfig(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        return voById;
    }
    /**
     * 处理关联
     *
     * @param applyId  流程编号
     * @param oldVersionId 旧版本主键
     * @param newVersionId 新版本主键
     */
    private void handApplyLink(String applyId, String oldVersionId,String newVersionId) {
        //List<ModifyApplyLink> list = modifyApplyLinkService.list(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getApplyId,applyId).in(ModifyApplyLink::getLinkClass,LinkClassEnum.LINK.name(),LinkClassEnum.DOC.name(),LinkClassEnum.HOST.name()));
        List<ModifyApplyLink> list = modifyApplyLinkService.list(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getApplyId,applyId));

        for (ModifyApplyLink malVo: list) {
            if (NumberUtil.equals(malVo.getStatus(),NumberConstants.ZERO)) {
                // 待生效
                DocLinkLog docLinkLog=new DocLinkLog();
                docLinkLog.setLinkCode(malVo.getDocId());
                docLinkLog.setFileName(malVo.getDocName());
                docLinkLog.setFileId(malVo.getFileId());
                docLinkLog.setVersionValue(malVo.getVersionValue());
                docLinkLog.setDocClass(malVo.getDocClass());
                docLinkLog.setLinkType(malVo.getLinkType());
                docLinkLog.setStatus(Constants.ONE);
                //docLinkLog.setVersionId(newVersionId);
                docLinkLogService.save(docLinkLog);
                //插入关联记录表
                DocVersionLink docVersionLink = new DocVersionLink();
                docVersionLink.setLinkId(docLinkLog.getId());
                docVersionLink.setVersionId(newVersionId);
                iDocVersionLinkService.save(docVersionLink);
                //更新linkId
                malVo.setLinkId(docLinkLog.getId());
                modifyApplyLinkService.updateById(malVo);
            }else if(NumberUtil.equals(malVo.getStatus(),NumberConstants.ONE)){
                // 待删除
                DocLinkLog docLinkLog= docLinkLogService.getById(malVo.getLinkId());
                if(ObjectUtil.isNotEmpty(docLinkLog)) {
                    iDocVersionLinkService.remove(new LambdaQueryWrapper<DocVersionLink>().eq(DocVersionLink::getLinkId,docLinkLog.getId()).eq(DocVersionLink::getVersionId,oldVersionId));
                }
            }else if(NumberUtil.equals(malVo.getStatus(),NumberConstants.TWO)){
                // 待作废
                docLinkLogService.update(new LambdaUpdateWrapper<DocLinkLog>().set(DocLinkLog::getStatus,Constants.TWO).eq(DocLinkLog::getId,malVo.getLinkId()));
            }
        }
    }
    /**
     * 处理关联
     *
     * @param applyId
     * @param docId
     * @param version
     */
    private void handApplyLink(String applyId, String docId, Version version) {
        ModifyApplyLinkBo modifyApplyLinkBo = new ModifyApplyLinkBo();
        modifyApplyLinkBo.setApplyId(applyId);
        modifyApplyLinkBo.setIsDeleted(0);
        List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryList(modifyApplyLinkBo);
        final Map<String, String> addFileIdMap = new HashMap<>();
        // TODO: 不确定是否需要新增加的记录创建doc_standard, version, link_log记录，需求上有待评估
//        for (ModifyApplyLinkVo linkVo : linkVoList) {
//            if (LinkClassEnum.FILE.name().equals(linkVo.getLinkClass()) && LinkTypeEnum.RECORD.name().equals(linkVo.getLinkType())) {
//
//            }
//        }
        // 创建关联
        List<DocLinkLog> linkLogList = Optional.ofNullable(linkVoList).orElse(CollectionUtil.newArrayList()).stream().map(linkVo -> {
            DocLinkLog linkLog = BeanUtil.toBean(linkVo, DocLinkLog.class);

            //linkLog.setDocId(docId).setLinkCode(linkVo.getLinkId()).setVersionValue(version.getVersionValue()).setVersionId(version.getId());
            linkLog.setLinkCode(docId).setVersionValue(version.getVersionValue());

            if (LinkClassEnum.FILE.name().equals(linkVo.getLinkClass())) {
                addFileIdMap.put(linkVo.getLinkId(), linkVo.getLinkType());
            }
            return linkLog;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(linkLogList)) {
            //新增或者更新
            docLinkLogService.saveAll(linkLogList);
        }
        //新增的文件，更新basic_file的业务ID
        if (CollectionUtil.isNotEmpty(addFileIdMap)) {
            List<BasicFile> basicFileList = basicFileService.listByIds(addFileIdMap.keySet());
            List<BasicFile> updateFileList = new ArrayList<>();
            for (BasicFile basicFile : basicFileList) {
                if (ObjectUtil.isEmpty(basicFile.getBusinessId())) {
                    basicFile.setBusinessId(version.getId()).setFileClass(addFileIdMap.get(basicFile.getId()));
                    updateFileList.add(basicFile);
                }
            }
            basicFileService.updateBatchById(updateFileList);
        }
    }

    /**
     * 分发文档特定版本到各个部门
     *
     */
    private void handleDistribute(ModifyApply applyPo, Version version) {
        ModifyApplyDistributeBo distributeBo = new ModifyApplyDistributeBo();
        distributeBo.setApplyId(applyPo.getId());
        distributeBo.setIsDeleted(0);
        List<ModifyApplyDistributeVo> distributeVoList = modifyApplyDistributeService.queryList(distributeBo);
        List<DocDistributeLog> distributeLogList = new ArrayList<>();

        DocDistributeLog distributeLog;
        for (ModifyApplyDistributeVo modifyApplyDistributeVo : distributeVoList) {
            distributeLog = BeanUtil.toBean(modifyApplyDistributeVo, DocDistributeLog.class);
            if (ObjectUtil.isEmpty(distributeLog.getDeptName())){
                distributeLog.setDeptName(sysDeptService.getById(modifyApplyDistributeVo.getDeptId()).getDeptName());
            }
            distributeLog.setId(null);
            distributeLog.setDocId(applyPo.getDocId()).setVersionId(version.getId()).setVersionValue(version.getVersionValue()).setDistributeTime(applyPo.getSetupTime()).setReceiveStatus(CommonStatusEnum.UNKNOWN.getStatus());
            distributeLog.setCreateTime(new Date());
            distributeLog.setChangeType(ApplyTypeEnum.toType(applyPo.getChangeType()));
            distributeLog.setPreviousVersionValue(applyPo.getVersionValue());
            distributeLog.setDistributeUserName(SecurityUtils.getUsername());
            distributeLog.setDistributeUserNickName(SecurityUtils.getNickname());
            distributeLog.setDocName(applyPo.getDocName());
            distributeLog.setDocClass(applyPo.getDocClass());
            distributeLog.setCompileDeptId(applyPo.getDeptId());
            distributeLog.setCompileDeptName(sysDeptService.getById(applyPo.getDeptId()).getDeptName());

            distributeLogList.add(distributeLog);
        }


        if (ObjectUtil.isNotEmpty(distributeLogList)) {
            //插入签收日志表
            docDistributeLogService.saveAll(distributeLogList);
            distributeLogList.forEach(docDistributeLog -> {
                docMessageService.insertMessageByDistribute(docDistributeLog);
            });
        }
    }

    /**
     * 流程数据更新状态和docId
     *
     * @param docId
     * @param applyPo
     */
    private void handleUpdateApplyStatus(String docId, ModifyApply applyPo, Version version, String status, String actDefName) {
        if (ObjectUtil.isNotEmpty(applyPo)) {
            if (ObjectUtil.isNotEmpty(status)) {
                applyPo.setRecordStatus(status);
            }
            if (ObjectUtil.isNotEmpty(version)) {
                applyPo.setDocId(docId);
                applyPo.setVersionId(version.getId());
                applyPo.setVersionValue(version.getVersionValue());
            }
            updateById(applyPo);

            if (ObjectUtil.isNotEmpty(status)) {
                workflowApplyLogService.updateStatusByBusId(applyPo.getId(), status, actDefName,"");
            }
        }
    }


    /**
     * TODO: 根据文档类型获取有效年限
     *
     * @param docClass
     * @return
     */
    @Override
    public Long getDocExpiration(String docClass) {
        DocClassVo voById = iDocClassService.getVoById(docClass);
        if (ObjectUtil.isNotEmpty(voById)){
            return voById.getExpiration();
        }else {
            return 1L;
        }
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ModifyApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    private LambdaQueryWrapper<ModifyApply> buildQueryWrapper(ModifyApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ModifyApply> lqw = Wrappers.lambdaQuery();

        lqw.eq(ModifyApply::getUserName, SecurityUtils.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), ModifyApply::getChangeType, bo.getChangeType());
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), ModifyApply::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), ModifyApply::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ModifyApply::getDocId, bo.getDocId());
        lqw.eq(bo.getDeptId() != null, ModifyApply::getDeptId, bo.getDeptId());
        lqw.eq(bo.getApplyTime() != null, ModifyApply::getApplyTime, bo.getApplyTime());
        lqw.eq(bo.getExpiration() != null, ModifyApply::getExpiration, bo.getExpiration());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), ModifyApply::getVersionId, bo.getVersionId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionValue()), ModifyApply::getVersionValue, bo.getVersionValue());
        lqw.eq(StringUtils.isNotBlank(bo.getYNTrain()), ModifyApply::getYNTrain, bo.getYNTrain());
        lqw.eq(StringUtils.isNotBlank(bo.getTrainDept()), ModifyApply::getTrainDept, bo.getTrainDept());
        lqw.eq(StringUtils.isNotBlank(bo.getYNMergeDraft()), ModifyApply::getYNMergeDraft, bo.getYNMergeDraft());
        lqw.eq(StringUtils.isNotBlank(bo.getYNEncrypt()), ModifyApply::getYNEncrypt, bo.getYNEncrypt());
        lqw.eq(StringUtils.isNotBlank(bo.getYNDistribute()), ModifyApply::getYNDistribute, bo.getYNDistribute());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), ModifyApply::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordStatus()), ModifyApply::getRecordStatus, bo.getRecordStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeReason()), ModifyApply::getChangeReason, bo.getChangeReason());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ModifyApply::getContent, bo.getContent());

        lqw.orderByDesc(ModifyApply::getApplyTime);
        return lqw;
    }

    /**
     * 生成新的文档ID
     *
     * @return
     */


    @Override
    public DocNoVo getDocNoByApplyId(String applyId) {
        ModifyApply applyPo = getById(applyId);
        try{
            if (StringUtils.isNotEmpty(applyPo.getDocId())) {
                DocNoVo docNoVo = new DocNoVo();
                docNoVo.setDocId(applyPo.getDocId());
                docNoVo.setDocName(applyPo.getDocName());
                return docNoVo;
            }else {
                DocNoVo docNoVo = iGenerateIdService.generateDocId(applyPo);
                applyPo.setDocId(docNoVo.getDocId());
                this.baseMapper.updateById(applyPo);
                docNoVo.setDocName(applyPo.getDocName());
                return docNoVo;
            }
        }
        catch(Exception ex){
            log.error("getDocNoByApplyId====",ex);
        }
        return null;
        //String docId = docNoVo.getDocId();
        //Standard standard = BeanUtil.toBean(applyPo, Standard.class);
        //if(applyPo.getExpiration()!=null) {
        //        standard.setExpiration(Long.valueOf(applyPo.getExpiration()));
        //        Long expiration = ObjectUtil.isNotEmpty(applyPo.getExpiration()) && applyPo.getExpiration() > 0 ? applyPo.getExpiration() : getDocExpiration(applyPo.getDocClass());
        //        standard.setExpiration(Long.valueOf(expiration));
        //}

            //standard.setChangeFactor(applyPo.getChangeFactor());
            //standard.setChangeReason(applyPo.getChangeReason());
            //ModifyApplyLinkBo linkBo = new ModifyApplyLinkBo();
            //linkBo.setIsDeleted(0);
            //linkBo.setApplyId(applyPo.getId());
            //linkBo.setLinkType("DOC");
            //List<ModifyApplyLinkVo> linkVoList = modifyApplyLinkService.queryList(linkBo);
//            if (ObjectUtil.isNotEmpty(linkVoList)) {
//                standard.setFileId(linkVoList.get(0).getLinkId());
//            }


//            if(ObjectUtil.isEmpty(applyPo.getDocId())) {
//                standard.setId(null);
//                applyPo.setDocId(docId);
//                standardService.save(standard);
//            }


    }



    /**
     * 生产记录文件的文件编号
     * @param requestMap 关联记录文件id
     * @return
     */
    @Override
    public List<DocNoVo> getRecordDocNoByLinkId(Map<String,String> requestMap) {
        Map<String,DocNoVo> map = Maps.newHashMap();
        String applyId = requestMap.get("applyId");
        String fileIds = requestMap.get("fileIds");
        ModifyApply applyPo = getById(applyId);
        List<String> linkList = StrUtil.split(fileIds,',');
        List<DocNoVo> list =new ArrayList<>();
        if (linkList.size()>0){
            for(String fileId:linkList){
                ModifyApplyLink applyLink = this.modifyApplyLinkService.
                        getOne(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getFileId, fileId).eq(ModifyApplyLink::getApplyId, applyId));
                if (ObjectUtil.isNotEmpty(applyLink)){
                    try{
                        if (StringUtils.isNotEmpty(applyLink.getDocId())) {
                            DocNoVo docNoVo = new DocNoVo();
                            docNoVo.setDocId(applyLink.getDocId());
                            docNoVo.setDocName(applyLink.getDocName());
                            list.add(docNoVo);
                        }else {
                            DocNoVo docNoVo = iGenerateIdService.generateRecordDocId(applyPo,applyLink);
                            applyLink.setDocId(docNoVo.getDocId());
                            this.modifyApplyLinkService.updateById(applyLink);
                            docNoVo.setLinkId(fileId);
                            docNoVo.setDocName(applyLink.getDocName());
                            list.add(docNoVo);
                        }
                    }
                    catch (Exception ex){
                        log.error("getRecordDocNoByLinkId===="+fileId,ex);
                    }

                }
//            map.put(linkId,docNoVo);
            }
        }
        return list;

    }
    @Override
    public boolean checkDocIdExist(String docId){
        return iGenerateIdService.checkDocIdExist(docId);
    }

    @Override
    public Boolean updateDocSerialNumber(CreateNewNoBo bo) throws Exception{
        return iGenerateIdService.updateDocSerialNumber(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRecordSerialNumber(List<CreateNewNoBo> listBo) throws Exception {
        for (CreateNewNoBo bo : listBo) {
            iGenerateIdService.updateRecordSerialNumber(bo);
        }
        return true;
    }
}
