package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.ReviewApply;
import com.rzdata.process.domain.ReviewApplyItem;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.ReviewApplyVo;

import java.util.Collection;
import java.util.List;

/**
 * 复审文件清单Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IReviewApplyItemService extends IServicePlus<ReviewApplyItem, ReviewApplyItemVo> {

    /**
     * 根据参数查询复审信息
     * @param bo
     * @return
     */
    List<ReviewApplyItemVo> queryItemList(ReviewApplyItemBo bo);

}
