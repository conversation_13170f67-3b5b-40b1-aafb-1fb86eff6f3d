package com.rzdata.process.service;


import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.ReprintLog;
import com.rzdata.process.domain.bo.ReprintLogBo;
import com.rzdata.process.domain.vo.ReprintLogVo;

/**
 * <AUTHOR>
 * @Date 2022/2/26 11:52
 * @Version 1.0
 * @Description
 */
public interface IReprintLogService extends IServicePlus<ReprintLog, ReprintLogVo> {


    TableDataInfo<ReprintLogVo> queryPageList(ReprintLogBo bo);

    boolean insertByBo(ReprintLogBo bo);
}
