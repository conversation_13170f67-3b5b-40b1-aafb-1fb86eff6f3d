package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.mapper.DocLinkLogMapper;
import com.rzdata.process.service.IDocLinkLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件关联记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Service
public class DocLinkLogServiceImpl extends ServicePlusImpl<DocLinkLogMapper, DocLinkLog, DocLinkLogVo> implements IDocLinkLogService {

    @Override
    public DocLinkLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocLinkLogVo> queryPageList(DocLinkLogBo bo) {
        Page<DocLinkLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<DocLinkLogVo> result = this.baseMapper.getPageList(page, bo);
//        PagePlus<DocLinkLog, DocLinkLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocLinkLogVo> queryList(DocLinkLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocLinkLog> buildQueryWrapper(DocLinkLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocLinkLog> lqw = Wrappers.lambdaQuery();
        //lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DocLinkLog::getDocId, bo.getDocId());
        //lqw.eq(StringUtils.isNotBlank(bo.getStandardId()), DocLinkLog::getStandardId, bo.getStandardId());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()),DocLinkLog::getLinkType,bo.getLinkType());
        //lqw.eq(StringUtils.isNotBlank(bo.getVersionId()), DocLinkLog::getVersionId, bo.getVersionId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocLinkLogBo bo) {
        DocLinkLog add = BeanUtil.toBean(bo, DocLinkLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocLinkLogBo bo) {
        DocLinkLog update = BeanUtil.toBean(bo, DocLinkLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocLinkLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public DocLinkLogVo queryLinkVo(String docId, String linkCode, String versionId) {
        return this.baseMapper.queryLinkVo(docId, linkCode, versionId);
    }

    @Override
    public List<DocLinkLogVo> queryDocLinkVo(String docId, String versionId, LinkTypeEnum linkType) {
        return this.baseMapper.queryDocLinkVo(docId,versionId,linkType.name());
    }
}
