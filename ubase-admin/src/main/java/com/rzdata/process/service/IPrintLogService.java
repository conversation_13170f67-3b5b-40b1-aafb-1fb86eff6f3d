package com.rzdata.process.service;

import com.rzdata.process.domain.PrintLog;
import com.rzdata.process.domain.vo.PrintLogVo;
import com.rzdata.process.domain.bo.PrintLogBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件打印记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
public interface IPrintLogService extends IServicePlus<PrintLog, PrintLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	PrintLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<PrintLogVo> queryPageList(PrintLogBo bo);

	/**
	 * 查询列表
	 */
	List<PrintLogVo> queryList(PrintLogBo bo);

	/**
	 * 根据新增业务对象插入文件打印记录
	 * @param bo 文件打印记录新增业务对象
	 * @return
	 */
	String insertByBo(PrintLogBo bo);

	/**
	 * 根据编辑业务对象修改文件打印记录
	 * @param bo 文件打印记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(PrintLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	//PrintLog selectPrintByDistId(String id);

}
