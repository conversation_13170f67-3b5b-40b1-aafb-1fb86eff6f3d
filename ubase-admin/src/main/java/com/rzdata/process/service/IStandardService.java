package com.rzdata.process.service;

import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.domain.bo.StandardBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.listener.ibo.LinkLogIBo;
import com.rzdata.process.listener.ibo.StandardIBo;
import com.rzdata.process.listener.ivo.StandardIVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 标准文件Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IStandardService extends IServicePlus<Standard, StandardVo> {
	/**
	 * 批量查询
	 * @param ids
	 * @return
	 */
	Map<String, StandardVo> queryByIdList(List<String> ids);
	/**
	 * 查询单个
	 * @return
	 */
	StandardVo queryById(String id);

	/**
	 * 查询单个
	 * @return
	 */
	Standard queryByDocIdAndVersion(String docId,String versionValue);

	/**
	 * 查询文档的特定版本详情信息，不传版本ID时为当前最新有效版本。
	 * @param docId 文档ID
	 * @param versionId 版本ID，传参时查询特定版本的详情
	 * @return
	 */
	StandardVo queryDetail(String docId, String versionId,String flag);

	/**
	 * 查询列表
	 */
    TableDataInfo<StandardVo> queryPageList(StandardBo bo);

	/**
	 * 查询列表
	 */
	TableDataInfo<StandardVo> selectPageList(StandardBo bo);

	/**
	 * 导出关联记录
	 */
	List<DocLinkLogVo> exportLinkLog(StandardBo bo,List<String> ids);

	/**
	 * 查询列表
	 */
	List<StandardVo> selectList(StandardBo bo);

	/**
	 * 查询列表
	 */
	List<StandardVo> queryList(StandardBo bo);

	/**
	 * 根据新增业务对象插入标准文件
	 * @param bo 标准文件新增业务对象
	 * @return
	 */
	Boolean insertByBo(StandardBo bo);

	/**
	 * 加入关联记录
	 * @param bo 标准文件新增业务对象
	 * @return
	 */
	Boolean join(StandardBo bo);

	/**
	 * 根据编辑业务对象修改标准文件
	 * @param bo 标准文件编辑业务对象
	 * @return
	 */
	Boolean updateByBo(StandardBo bo);

	Boolean updateByImport(StandardIBo ibo, int num, StandardIVo ivo);

	Boolean updateLinkLogByImport(LinkLogIBo ibo,String linkType, int num, StandardIVo ivo);

	/**
	 * 根据编辑业务对象修改标准文件和版本记录
	 * @param bo 标准文件编辑业务对象
	 * @return
	 */
	Boolean updateBoAndVersion(StandardBo bo);
	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 *  查出有效的文件列表
	 *  定时任务专用
	 * @return 文件列表
	 */
	List<StandardVo> selectValidFile();

	/**
	 * 通过versionId 查出标准文件信息
	 * @param versionId
	 * @return
	 */
	StandardVo getStandardVoByVersionId(String versionId);

	/**
	 * 删除文件
	 * @param bo
	 */
	Boolean deleteByBo(StandardBo bo);

	/**
	 * 根据id查询文件信息
	 * @param id	id
	 * @return
	 */
	StandardVo queryInfoById(String id);
}
