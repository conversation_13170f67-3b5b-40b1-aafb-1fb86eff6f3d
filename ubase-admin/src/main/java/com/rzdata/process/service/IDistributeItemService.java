package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.bo.DistributeItemBo;
import com.rzdata.process.domain.vo.DistributeItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件分发记录条目Service接口
 *
 * <AUTHOR>
 * @date 2022-01-10
 */
public interface IDistributeItemService extends IServicePlus<DistributeItem, DistributeItemVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DistributeItemVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DistributeItemVo> queryPageList(DistributeItemBo bo);

	/**
	 * 查询列表
	 */
	List<DistributeItemVo> queryList(DistributeItemBo bo);

	/**
	 * 根据新增业务对象插入文件分发记录条目
	 * @param bo 文件分发记录条目新增业务对象
	 * @param docClass
	 * @return
	 */
	Boolean insertByBo(DistributeItemBo bo,String docClass);

	/**
	 * 根据编辑业务对象修改文件分发记录条目
	 * @param bo 文件分发记录条目编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DistributeItemBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 更新数据
	 * @param ids id集合
	 * @param type 1: 打印，2：回收
	 */
	void updateByIds(List<String> ids, Integer type, String status, String id);

	TableDataInfo<DistributeItemVo> listByAppid(DistributeItemBo bo);

	TableDataInfo<DistributeItemVo> listRecoveryByAppid(DistributeItemBo bo);

	TableDataInfo<DistributeItemVo> listByDisId(DistributeItemBo bo);
}
