package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.DictConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.WorkflowApplyLog;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.vo.*;
import com.rzdata.process.enums.LinkTypeEnum;
import com.rzdata.process.listener.ibo.LinkLogIBo;
import com.rzdata.process.listener.ibo.StandardIBo;
import com.rzdata.process.listener.ivo.StandardIVo;
import com.rzdata.process.mapper.ModifyApplyMapper;
import com.rzdata.process.mapper.StandardMapper;
import com.rzdata.process.mapper.WorkflowApplyLogMapper;
import com.rzdata.process.mapper.WorkflowLogMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysDeptService;
import com.rzdata.system.service.ISysDictTypeService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标准文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class StandardServiceImpl extends ServicePlusImpl<StandardMapper, Standard, StandardVo> implements IStandardService {
    private static final String ID_SPLIT_KEY = ",";

    @Autowired
    private IVersionService versionService;
    @Autowired
    private IDocLinkLogService docLinkLogService;
    @Autowired
    private IBasicFileService basicFileService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IDocDistributeLogService docDistributeLogService;
    @Autowired
    private IModifyApplyTrainService modifyApplyTrainService;

    @Autowired
    private IModifyApplyDistributeService modifyApplyDistributeService;

    @Autowired
    private IStandardService standardService;

    @Autowired
    private IDocLinkLogService iDocLinkLogService;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    private IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IDocClassService iDocClassService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    private IDocVersionLinkService docVersionLinkService;

    @Autowired
    private WorkflowApplyLogMapper workflowApplyLogMapper;

    @Override
    public Map<String, StandardVo> queryByIdList(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        return Optional.ofNullable(this.listVoByIds(ids)).orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(StandardVo::getId, Function.identity()));
    }

    @Override
    public StandardVo queryById(String id) {
        StandardVo vo = getVoById(id);
        Version version = versionService.queryOneByStandardId(vo.getId());
        if (ObjectUtil.isNotEmpty(vo.getDeptId())) {
            vo.setDeptName(sysDeptService.getById(vo.getDeptId()).getDeptName());
        }
        if (ObjectUtil.isNotEmpty(vo.getUserName())) {
            SysUser user = sysUserService.selectUserByUserName(vo.getUserName());
            if(ObjectUtil.isNotEmpty(user)){
                vo.setNickName(user.getNickName());
            }
        }
        if (ObjectUtil.isNotNull(version)) {
            vo.setStartDate(version.getStartDate());
            vo.setDocId(version.getDocId());
            vo.setEndDate(version.getEndDate());
            vo.setReviewTime(version.getReviewTime());
            vo.setCurrentVersion(version.getVersionValue());
            vo.setForever(version.getForever());
            vo.setVersionId(version.getId());
        }
        return vo;
    }

    @Override
    public Standard queryByDocIdAndVersion(String docId, String versionValue) {
        Version version = versionService.queryByDocIdAndVersion(docId,versionValue);
        if (ObjectUtil.isNotEmpty(version)) {
            return getById(version.getStandardId());
        }
        return null;
    }

    @Override
    public StandardVo queryDetail(String docId, String versionId,String flag) {
        VersionVo vVo= versionService.getVoById(versionId);
        StandardVo standardVo = getVoById(vVo.getStandardId());
        standardVo.setDocId(vVo.getDocId());
        //查询当前文件的流程状态
        String changeType = this.workflowApplyLogService.queryProcessStatus(docId);
        standardVo.setChangeType(changeType);

        //文件历史
        List<VersionVo> versionVoList = versionService.selectVersionListByDocId(docId);
        standardVo.setVersions(versionVoList);

        //截止日期减少一天
        if(!vVo.getForever().equals(Constants.ONE)) {
            standardVo.setRecheckDate(vVo.getEndDate());
            //文件有效期
            standardVo.setExpirationDate(DateUtil.formatDate(vVo.getEndDate()));

        }
        //复审时间
        if(ObjectUtil.isNotEmpty(vVo.getReviewTime())) {
            standardVo.setReviewTime(vVo.getReviewTime());
        }

        //当前文件版本
        standardVo.setCurrentVersion(vVo.getVersionValue());
        //培训部门
        standardVo.setTrainDept(vVo.getTrainDept());

        /**
         * 查询文档关联的编制文件，附件，关联文档和关联记录
         */
        //DocLinkLogBo linkLogBo = new DocLinkLogBo();
        //linkLogBo.setVersionId(versionId);
        //查出当前文件的主文件 主文件只允许上传1个 所以List中如果有值 也会只有一条数据 因为方法复用 就不单独写一个SQL方法了
        List<DocLinkLogVo> docLinkLogVoList = docLinkLogService.queryDocLinkVo(docId,versionId,LinkTypeEnum.DOC);
        if (ObjectUtil.isNotEmpty(docLinkLogVoList)) {
            standardVo.setPreStandardDoc(docLinkLogVoList.get(0));
        }
        //当前版本的附件
        List<DocLinkLogVo> appendixLinkLogVoList = docLinkLogService.queryDocLinkVo(docId,versionId,LinkTypeEnum.APPENDIX);
        standardVo.setPreAppendixes(appendixLinkLogVoList);

        //文件备注附件
        List<DocLinkLogVo> remakeAppendixList = docLinkLogService.queryDocLinkVo(docId,versionId,LinkTypeEnum.APPENDIX_REMARK);
        standardVo.setRemakeAppendixes(remakeAppendixList);

        /**
         * 查询分发记录
         */
        List<ModifyApplyDistributeVo> distributeVoList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(vVo.getApplyId())) {
            ModifyApplyDistributeBo distributeBo = new ModifyApplyDistributeBo();
            distributeBo.setApplyId(vVo.getApplyId());
            distributeBo.setIsDeleted(0);
            distributeVoList = modifyApplyDistributeService.queryList(distributeBo);
        }


        /**
         * 查询版本的培训记录
         */
        ModifyApplyTrainBo modifyApplyTrainBo = new ModifyApplyTrainBo();
        modifyApplyTrainBo.setVersionId(versionId);
        List<ModifyApplyTrainVo> trainVoList = modifyApplyTrainService.queryList(modifyApplyTrainBo);

        /**
         * 部门ID，文件ID，用户ID 转 名称用来展示
         */
        List<String> basicFileIdLinks = new ArrayList<>();
        List<String> docIdLinks = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();
        List<String> userNames = new ArrayList<>();

        if (ObjectUtil.isNotEmpty(standardVo.getDeptId())) {
            deptIds.add(standardVo.getDeptId());
        }
        if (ObjectUtil.isNotEmpty(standardVo.getUserName())) {
            userNames.add(standardVo.getUserName());
        }
        if (CollectionUtil.isNotEmpty(distributeVoList)) {
            standardVo.setDistributeDepths(distributeVoList);
            deptIds.addAll(distributeVoList.stream().map(distribute -> distribute.getDeptId()).collect(Collectors.toList()));
        }

        /**
         * 设置培训部门
         */
        if (CollectionUtil.isNotEmpty(trainVoList)) {
            standardVo.setTrains(trainVoList);
            for (ModifyApplyTrainVo trainVo : trainVoList) {
                if (ObjectUtil.isNotEmpty(trainVo.getDeptId())) {
                    deptIds.add(trainVo.getDeptId());
                }
                if (ObjectUtil.isNotEmpty(trainVo.getUserName())) {
                    userNames.add(trainVo.getUserName());
                }
                if (ObjectUtil.isNotEmpty(trainVo.getFileIds())) {
                    basicFileIdLinks.addAll(Arrays.asList(trainVo.getFileIds().split(ID_SPLIT_KEY)));
                }
            }
            standardVo.setYNTrain(1);
        } else {
            standardVo.setYNTrain(2);
        }
//        if (CollectionUtil.isNotEmpty(docLinkLogVoList)) {
//            for (DocLinkLogVo docLinkLogVo : docLinkLogVoList) {
//                switch (LinkTypeEnum.toType(docLinkLogVo.getLinkType())) {
//                    case DOC:
//                        standardVo.setStandardDoc(docLinkLogVo);
//                        docIdLinks.add(docLinkLogVo.getDocId());
//
//                        break;
//                    case RECORD:
//                        if (CollectionUtil.isEmpty(standardVo.getRecordLinks())) {
//                            standardVo.setRecordLinks(new ArrayList<>());
//                        }
//                        standardVo.getRecordLinks().add(docLinkLogVo);
//                        basicFileIdLinks.add(docLinkLogVo.getLinkCode());
//                        break;
//                    case APPENDIX:
//                        if (CollectionUtil.isEmpty(standardVo.getAppendixes())) {
//                            standardVo.setAppendixes(new ArrayList<>());
//                        }
//                        standardVo.getAppendixes().add(docLinkLogVo);
//                        basicFileIdLinks.add(docLinkLogVo.getLinkCode());
//                        break;
//                    case REF_DOC:
//                        if (CollectionUtil.isEmpty(standardVo.getDocLinks())) {
//                            standardVo.setDocLinks(new ArrayList<>());
//                        }
//                        standardVo.getDocLinks().add(docLinkLogVo);
//                        basicFileIdLinks.add(docLinkLogVo.getLinkCode());
//                        break;
//                }
//            }
//        }
        /*if (ObjectUtil.isNotEmpty(docLinkLogVoList)) {
            QueryWrapper<ModifyApply> modifyApplyQueryWrapper = new QueryWrapper<>();
            modifyApplyQueryWrapper.lambda().eq(ModifyApply:: getDocId, docLinkLogVoList.get(0).getLinkCode());
            modifyApplyQueryWrapper.lambda().orderByDesc(ModifyApply:: getApplyTime);
            *//**
             * 不知道什么要加这两段
             *//*
           // modifyApplyQueryWrapper.lambda().ne(ModifyApply:: getChangeType, DISUSE);
           // modifyApplyQueryWrapper.lambda().ne(ModifyApply:: getChangeType, UPDATE);
            *//**
             * 不知道什么要加这两段
             *//*
            List<ModifyApply> modifyApply = modifyApplyMapper.selectList(modifyApplyQueryWrapper);
            if (ObjectUtil.isNotEmpty(modifyApply)) {
                List<ModifyApplyLink> linkVoList = new ArrayList<>();
                for (ModifyApply apply : modifyApply) {
                    QueryWrapper<ModifyApplyLink> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().in(ModifyApplyLink:: getIsDeleted, 0,3);
                    queryWrapper.lambda().eq(ModifyApplyLink:: getApplyId, apply.getId());

                    linkVoList.addAll(modifyApplyLinkService.list(queryWrapper));
                }
                if (CollectionUtil.isNotEmpty(linkVoList)) {
                    for (ModifyApplyLink modifyApplyLink : linkVoList) {
                        if(modifyApplyLink.getLinkType().equals("DOC") || modifyApplyLink.getLinkType().equals("APPENDIX")) {
                            modifyApplyLink.setLinkType(modifyApplyLink.getLinkType()+"_1");
                        }
                    }
//            List<String> basicFileIdLinks = new ArrayList<>();
//            List<String> docIdLinks = new ArrayList<>();
                    List<String> records = new ArrayList<>();
                    for (ModifyApplyLink linkVo : linkVoList) {
                        switch (LinkTypeEnum.toType(linkVo.getLinkType())) {
                            case DOC:
                                VersionVo versionVo = versionService.selectVersionByDocId(linkVo.getDocId());
                                if (ObjectUtil.isNotEmpty(versionVo)) {
                                    linkVo.setVersionValue(versionVo.getVersionValue());
                                    linkVo.setVersionId(versionVo.getId());
                                }
                                standardVo.setStandardDoc(linkVo);
                                break;
                            case DOC_1:
                                //if (!ObjectUtil.equal(applyVo.getChangeType(),"ADD")){
                                standardVo.setPreStandardDoc(linkVo);
                                linkVo.setDocName(basicFileService.getVoById(linkVo.getLinkId()).getFileName());
                                //}
                                break;
                            case RECORD:
                                if (CollectionUtil.isEmpty(standardVo.getRecordLinks())) {
                                    standardVo.setRecordLinks(new ArrayList<>());
                                }
                                linkVo.setStatus(linkVo.getIsDeleted());
                                standardVo.getRecordLinks().add(linkVo);
                                break;

                            case APPENDIX_REMARK:
                                if (CollectionUtil.isEmpty(standardVo.getRemarkDoc())) {
                                    standardVo.setRemarkDoc(new ArrayList<>());
                                }
                                standardVo.getRemarkDoc().add(linkVo);
                                break;
                            case APPENDIX:
                                if (CollectionUtil.isEmpty(standardVo.getAppendixes())) {
                                    standardVo.setAppendixes(new ArrayList<>());
                                }
                                standardVo.getAppendixes().add(linkVo);
                                break;
                            case APPENDIX_1:
                                if (CollectionUtil.isEmpty(standardVo.getPreAppendixes())) {
                                    standardVo.setPreAppendixes(new ArrayList<>());
                                }
                                standardVo.getPreAppendixes().add(linkVo);
                                break;
                            case REF_DOC:
                                if (CollectionUtil.isEmpty(standardVo.getDocLinks())) {
                                    standardVo.setDocLinks(new ArrayList<>());
                                }
                                standardVo.getDocLinks().add(linkVo);
                                records.add(linkVo.getLinkId());
                                break;
                            default:
                                break;
                        }
                        if (LinkClassEnum.DOC.name().equalsIgnoreCase(linkVo.getLinkClass())) {
                            docIdLinks.add(linkVo.getDocId());
                        } else {
                            basicFileIdLinks.add(linkVo.getLinkId());
                        }
                    }
                    Map<String, String> idNameMap = new HashMap<>();
                    if (CollectionUtil.isNotEmpty(basicFileIdLinks)) {
                        List<BasicFileVo> fileVoList = basicFileService.listVoByIds(basicFileIdLinks);
                        if (CollectionUtil.isNotEmpty(fileVoList)) {
                            for (BasicFileVo fileVo : fileVoList) {
                                idNameMap.put(fileVo.getId(), fileVo.getFileName());
                            }
                        }
                    }
                    if (CollectionUtil.isNotEmpty(docIdLinks)) {
                        List<StandardVo> standardVoList = standardService.listVoByIds(docIdLinks);
                        if (CollectionUtil.isNotEmpty(standardVoList)) {
                            for (StandardVo standardVo1 : standardVoList) {
                                idNameMap.put(standardVo1.getId(), standardVo1.getDocName());
                            }
                        }
                    }
                    if (ObjectUtil.isNotEmpty(records)) {
                        QueryWrapper<BasicFile> queryWrapper1 = new QueryWrapper<>();
                        queryWrapper1.lambda().in(BasicFile::getBusinessId, records);
                        List<BasicFile> list = basicFileService.list(queryWrapper1);
                        if (ObjectUtil.isNotEmpty(list)) {
                            for (BasicFile basicFile : list) {
                                idNameMap.put(basicFile.getBusinessId(), basicFile.getFileName());
                            }
                        }
                    }
                    if (MapUtil.isNotEmpty(idNameMap)) {
                        for (ModifyApplyLink linkVo : linkVoList) {
                            if (ObjectUtil.isEmpty(linkVo.getDocName())) {
                                if ("DOC".equals(linkVo.getLinkClass())) {
                                    linkVo.setDocName(idNameMap.get(linkVo.getDocId()));
                                } else {
                                    linkVo.setDocName(idNameMap.get(linkVo.getLinkId()));
                                }
                            }
                            if (StringUtils.isNotEmpty(linkVo.getDocId())) {
                                linkVo.setLinkClass(modifyApplyMapper.getDocTypeByDocId(linkVo.getDocId()));
                            }
                        }
                    }
                }
            }
        }*/

        Map<String, String> deptIdNameMap = sysDeptService.queryDeptName(deptIds);
        Map<String, String> userNameMap = sysUserService.queryNickNameByUserName(userNames);
        Map<String, BasicFileVo> idFileNameMap = basicFileService.queryByIdList(basicFileIdLinks);
        /**
         * 设置部门名称
         */
        if (CollectionUtil.isNotEmpty(standardVo.getDistributeDepths())) {
            for (ModifyApplyDistributeVo distributeLogVo : standardVo.getDistributeDepths()) {
                if (ObjectUtil.isNotEmpty(distributeLogVo.getDeptId())) {
                    distributeLogVo.setDeptName(deptIdNameMap.get(distributeLogVo.getDeptId()));
                }
            }
        }
        /**
         * 设置培训部门和培训人昵称
         */
        if (CollectionUtil.isNotEmpty(standardVo.getTrains())) {
            for (ModifyApplyTrainVo trainVo : standardVo.getTrains()) {
                if (ObjectUtil.isNotEmpty(trainVo.getDeptId())) {
                    trainVo.setDeptName(deptIdNameMap.get(trainVo.getDeptId()));
                }
                if (ObjectUtil.isNotEmpty(trainVo.getUserName())) {
                    trainVo.setNickName(userNameMap.get(trainVo.getUserName()));
                }
                if (ObjectUtil.isNotEmpty(trainVo.getFileIds())) {
                    List<String> fileIdList = Arrays.asList(trainVo.getFileIds().split(ID_SPLIT_KEY));
                    List<BasicFileVo> fileVoList = new ArrayList<>();
                    for (String fileId : fileIdList) {
                        BasicFileVo fileVo = idFileNameMap.get(fileId);
                        if (ObjectUtil.isNotEmpty(fileVo)) {
                            fileVoList.add(fileVo);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(fileVoList)) {
                        trainVo.setFiles(fileVoList);
                    }
                }
            }
        }
        /**
         * 设置编制人昵称，部门名称
         */
        if (ObjectUtil.isNotEmpty(standardVo.getDeptId())) {
            standardVo.setDeptName(deptIdNameMap.get(standardVo.getDeptId()));
        }
        if (ObjectUtil.isNotEmpty(standardVo.getUserName())) {
            standardVo.setNickName(userNameMap.get(standardVo.getUserName()));
        }

        if("1".equals(flag)) {
            //修订时删除部分信息
            standardVo.setRemarkDoc(null);
//            standardVo.setRemark("");
            standardVo.setChangeFactor("");
            standardVo.setChangeReason("");
            standardVo.setContent("");
        }
        return standardVo;
    }

    @Override
    public TableDataInfo<StandardVo> queryPageList(StandardBo bo) {
        PagePlus<Standard, StandardVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public TableDataInfo<StandardVo> selectPageList(StandardBo bo) {
        Page<StandardVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<StandardVo> iPage = baseMapper.selectPageList(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<DocLinkLogVo> exportLinkLog(StandardBo bo,List<String> ids) {
        return baseMapper.exportLinkLog(bo,ids);
    }


    @Override
    public List<StandardVo> selectList(StandardBo bo) {
        Page<StandardVo> page = new Page<>(PageUtils.DEFAULT_PAGE_NUM,PageUtils.DEFAULT_PAGE_SIZE);
        return baseMapper.selectPageList(page,bo).getRecords();
    }

    @Override
    public List<StandardVo> queryList(StandardBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Standard> buildQueryWrapper(StandardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Standard> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), Standard::getDocClass, bo.getDocClass());
        lqw.like(StringUtils.isNotBlank(bo.getDocName()), Standard::getDocName, bo.getDocName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Standard::getStatus, bo.getStatus());
        lqw.eq(bo.getExpiration() != null, Standard::getExpiration, bo.getExpiration());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrentVersion()), Standard::getCurrentVersion, bo.getCurrentVersion());
        lqw.eq(bo.getDeptId() != null, Standard::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), Standard::getUserName, bo.getUserName());
        lqw.eq(bo.getApplyTime() != null, Standard::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), Standard::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptFileId()), Standard::getEncryptFileId, bo.getEncryptFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), Standard::getContent, bo.getContent());
        if (ObjectUtil.isNotEmpty(bo.getSearchValue())) {
            lqw.like(Standard:: getDocName, bo.getSearchValue()).or().like(Standard:: getId, bo.getSearchValue())
                    .or().like(Standard::getCurrentVersion, bo.getSearchValue());
        }
        return lqw;
    }

    @Override
    public Boolean insertByBo(StandardBo bo) {
        Standard add = BeanUtil.toBean(bo, Standard.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean join(StandardBo bo) {
        // 加入关联记录及关联文件 根据linkType区分
        // 查询标准文件
        StandardVo standard =standardService.queryById(bo.getId());

        // 新增文件关联记录
        DocLinkLogBo dllBo = new DocLinkLogBo();
//        dllBo.setStandardId(standard.getId());
//        dllBo.setDocId(standard.getDocId());
        dllBo.setLinkCode(bo.getDocId());
        dllBo.setFileName(bo.getDocName());
        dllBo.setFileId(bo.getFileId());
        dllBo.setVersionValue(bo.getVersionValue());
//        dllBo.setVersionId(standard.getId());
        dllBo.setLinkType(bo.getLinkType());
        dllBo.setDocClass(bo.getDocClass());
        Boolean  insertDls = iDocLinkLogService.insertByBo(dllBo);

        // 新增 文件关联记录-关联表
        DocVersionLinkBo dvlBo = new DocVersionLinkBo();
        dvlBo.setLinkId(dllBo.getId());
        dvlBo.setVersionId(standard.getVersionId());
        Boolean insertDvls = docVersionLinkService.insertByBo(dvlBo);

        return insertDls && insertDvls;
    }

    @Override
    public Boolean updateByBo(StandardBo bo) {
        Standard update = BeanUtil.toBean(bo, Standard.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    @Override
    public Boolean updateByImport(StandardIBo ibo, int num, StandardIVo ivo) {
        // 校验是否产生了八大流程数据
        if(checkProcessMsg(ibo)){
            ivo.initFileMsgAdd(num,"已产生新数据不可修改");
            return false;
        }
        // 校验名称是否重复
        if(checkDocName(ibo)){
            ivo.initFileMsgAdd(num,"文件名称重复");
            return false;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<SysDictData> standardStatusList = dictTypeService.selectDictDataByType(DictConstants.STANDARD_STATUS);
        List<SysDictData> changeTypeList = dictTypeService.selectDictDataByType(DictConstants.CHANGE_TYPE);
        List<SysUser> userList = sysUserService.selectList();
        List<SysDept> deptList = sysDeptService.selectList();
        List<DocClass> dcList = iDocClassService.list();
        Version version = versionService.queryOneByStandardId(ibo.getId());
        if (ObjectUtil.isNull(version)) {
            version = new Version();
        }
        Standard standard = new Standard();
        standard.setId(ibo.getId());
        if (StringUtils.isNotBlank(ibo.getDocClass())) {
            Optional<DocClass> optional = dcList.stream().filter(i -> i.getClassName().equals(ibo.getDocClass())).findFirst();
            if (optional.isPresent()) {
                standard.setDocClass(optional.get().getId());
            } else {
                ivo.initFileMsgAdd(num,"文件类型不存在");
                return false;
            }
        } else {
            ivo.initFileMsgAdd(num,"文件类型不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getCurrentVersion())) {
            standard.setCurrentVersion(ibo.getCurrentVersion());
            version.setVersionValue(ibo.getCurrentVersion());
        } else {
            ivo.initFileMsgAdd(num,"文件版本号不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getDocName())) {
            standard.setDocName(ibo.getDocName());
        } else {
            ivo.initFileMsgAdd(num,"文件名称不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getStatus())) {
            Optional<SysDictData> optional = standardStatusList.stream().filter(i -> i.getDictLabel().equals(ibo.getStatus())).findFirst();
            if (optional.isPresent()) {
                standard.setStatus(optional.get().getDictValue());
            } else {
                ivo.initFileMsgAdd(num,"文件状态不存在");
                return false;
            }
        } else {
            ivo.initFileMsgAdd(num,"文件状态不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getChangeType())) {
            Optional<SysDictData> optional = changeTypeList.stream().filter(i -> ibo.getChangeType().contains(i.getDictLabel())).findFirst();
            if (optional.isPresent()) {
                standard.setChangeType(optional.get().getDictValue());
            } else {
                ivo.initFileMsgAdd(num,"变更类型不存在");
                return false;
            }
        } else {
            ivo.initFileMsgAdd(num,"变更类型不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getChangeReason())){
            standard.setChangeReason(ibo.getChangeReason());
            version.setReason(ibo.getChangeReason());
        }else {
            standard.setChangeReason(Constants.VALUE_INIT);
            version.setReason(Constants.VALUE_INIT);
        }
        if (StringUtils.isNotBlank(ibo.getContent())){
            standard.setContent(ibo.getContent());
        }else {
            standard.setContent(Constants.VALUE_INIT);
        }
        if (StringUtils.isNotBlank(ibo.getChangeFactor())){
            standard.setChangeFactor(ibo.getChangeFactor());
        }else {
            ivo.initFileMsgAdd(num,"变更要素不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getDeptId())) {
            Optional<SysDept> optional = deptList.stream().filter(i -> ibo.getDeptId().equals(i.getDeptName())).findFirst();
            if (optional.isPresent()) {
                standard.setDeptId(optional.get().getDeptId());
            } else {
                ivo.initFileMsgAdd(num,"编制部门不存在");
                return false;
            }
        } else {
            standard.setDeptId(loginUser.getDeptId());
        }
        if (StringUtils.isNotBlank(ibo.getUserName())) {
            Optional<SysUser> optional = userList.stream().filter(i -> ibo.getUserName().equals(i.getNickName())).findFirst();
            if (optional.isPresent()) {
                standard.setUserName(optional.get().getUserName());
            } else {
                ivo.initFileMsgAdd(num,"编制人员不存在");
                return false;
            }
        } else {
            standard.setUserName(loginUser.getUsername());
        }
        Date applyTime = Convert.toDate(ibo.getApplyTime());
        if (ObjectUtil.isNotEmpty(applyTime)) {
            standard.setApplyTime(applyTime);
        }else {
            standard.setApplyTime(DateUtil.date());
        }
        Date startDate = Convert.toDate(ibo.getStartDate());
        if (StringUtils.isNotBlank(ibo.getDocId())) {
            version.setDocId(ibo.getDocId());
        } else {
            ivo.initFileMsgAdd(num,"文件编号不能为空");
            return false;
        }
        if (ObjectUtil.isNotEmpty(startDate)) {
            version.setStartDate(startDate);
        } else {
            ivo.initFileMsgAdd(num,"生效日期读取错误");
            return false;
        }
        Date endDate = Convert.toDate(ibo.getEndDate());
        if (ObjectUtil.isNotEmpty(endDate)) {
            version.setEndDate(endDate);
        } else {
            ivo.initFileMsgAdd(num,"文件有效期读取错误");
            return false;
        }
        Date reviewTime = Convert.toDate(ibo.getReviewTime());
        if (ObjectUtil.isNotEmpty(reviewTime)) {
            version.setReviewTime(reviewTime);
        } else {
            ivo.initFileMsgAdd(num,"文件复审期读取错误");
            return false;
        }
        standard.setInitFile(Constants.ONE);
        version.setStandardId(ibo.getId());
        version.setStatus(Constants.ONE);
        versionService.saveOrUpdate(version);
        boolean bool = saveOrUpdate(standard);
        if (bool) {
            iGenerateIdService.saveOldDocId(standard.getId(),standard.getDocClass(),version.getDocId());
        }
        return bool;
    }

    /**
     * 校验文件名是否重复
     * @param bo
     * @return
     */
    private Boolean checkDocName(StandardIBo bo) {
        Standard standard = this.getById(bo.getId());
        if(ObjectUtil.isNotNull(standard) && !StringUtils.equals(bo.getDocName(),standard.getDocName())) {
            QueryWrapper<Standard> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Standard::getDocName, StringUtils.trim(bo.getDocName()));
            long count = this.count(queryWrapper);
            if (count > 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 校验是否产生了新数据
     * @param ibo
     * @return
     */
    private Boolean checkProcessMsg(StandardIBo ibo) {
        StandardVo standardVo = queryInfoById(ibo.getId());
        if(ObjectUtil.isNotNull(standardVo)){
            QueryWrapper<WorkflowApplyLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(WorkflowApplyLog::getDocId, standardVo.getDocId());
            queryWrapper.lambda().eq(WorkflowApplyLog::getVersionId, standardVo.getVersionId());
            Long count = workflowApplyLogMapper.selectCount(queryWrapper);
            if (count > 0) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean updateLinkLogByImport(LinkLogIBo ibo, String linkType, int num, StandardIVo ivo) {
        List<DocClass> dcList = iDocClassService.list();
        if (StringUtils.isBlank(ibo.getDocId())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件编号不能为空");
            return false;
        }
        if (StringUtils.isBlank(ibo.getLinkCode())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件编号不能为空");
            return false;
        }
        if (StringUtils.isBlank(ibo.getCurrentVersion())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件版本不能为空");
            return false;
        }
        if (StringUtils.isBlank(ibo.getVersionValue())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件版本不能为空");
            return false;
        }
        // 查询主文件
        Standard doc = standardService.queryByDocIdAndVersion(ibo.getDocId(),ibo.getCurrentVersion());
        // 查询关联文件
        Standard link = standardService.queryByDocIdAndVersion(ibo.getLinkCode(),ibo.getVersionValue());
        if (StringUtils.isNotBlank(ibo.getDsDocClass())) {
            Optional<DocClass> optional = dcList.stream().filter(i -> i.getClassName().equals(ibo.getDsDocClass())).findFirst();
            if (optional.isPresent()) {
                ibo.setDsDocClass(optional.get().getId());
            } else {
                ivo.linkFileMsgMsgAdd(linkType,num,"主-文件类型不存在");
                return false;
            }
        } else {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件类型不能为空");
            return false;
        }
        if (StringUtils.isNotBlank(ibo.getDocClass())) {
            Optional<DocClass> optional = dcList.stream().filter(i -> i.getClassName().equals(ibo.getDocClass())).findFirst();
            if (optional.isPresent()) {
                ibo.setDocClass(optional.get().getId());
            } else {
                ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件类型不存在");
                return false;
            }
        } else {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件类型不能为空");
            return false;
        }
        if (ObjectUtil.isNull(doc)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件不存在");
            return false;
        } else if (!doc.getStatus().equals(Constants.ONE)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件不是有效文件");
            return false;
        } else if (StringUtils.isBlank(ibo.getDsDocClass())||!doc.getDocClass().equals(ibo.getDsDocClass())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"主-文件类型与系统存储不一致");
            return false;
        }
        if (ObjectUtil.isNull(link)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件不存在");
            return false;
        } else if (!link.getStatus().equals(Constants.ONE)) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件不是有效文件");
            return false;
        }else if (StringUtils.isBlank(ibo.getDocClass())||!link.getDocClass().equals(ibo.getDocClass())) {
            ivo.linkFileMsgMsgAdd(linkType,num,"关联-文件类型与系统存储不一致");
            return false;
        }
        Version version = versionService.queryOneByStandardId(doc.getId());
        // 查询关联的文件信息
//        DocLinkLog docLinkLog = docLinkLogService.getOne(new LambdaQueryWrapper<DocLinkLog>().eq(DocLinkLog::getDocId,ibo.getDocId()).eq(DocLinkLog::getLinkCode,ibo.getLinkCode()).eq(DocLinkLog::getVersionId,version.getId()));
        DocLinkLogVo docLinkLogVo = docLinkLogService.queryLinkVo(ibo.getDocId(), ibo.getLinkCode(), version.getId());
        if (ObjectUtil.isNull(docLinkLogVo)) {
            DocLinkLogBo docLinkLog = new DocLinkLogBo();
//            docLinkLog.setStandardId(doc.getId());
//            docLinkLog.setDocId(ibo.getDocId());
            docLinkLog.setLinkCode(ibo.getLinkCode());
            docLinkLog.setLinkType(linkType);
            docLinkLog.setFileName(link.getDocName());
            docLinkLog.setFileId(link.getFileId());
            docLinkLog.setDocClass(ibo.getDocClass());
//            docLinkLog.setVersionId(version.getId());
            docLinkLog.setVersionValue(ibo.getVersionValue());
            Date createTime = Convert.toDate(ibo.getCreateTime());
            if (ObjectUtil.isNotEmpty(createTime)) {
                docLinkLog.setCreateTime(createTime);
            } else {
                docLinkLog.setCreateTime(DateUtil.date());
            }
            Boolean insertdll = iDocLinkLogService.insertByBo(docLinkLog);

            // 新增 文件关联记录-关联表
            DocVersionLinkBo dvlBo = new DocVersionLinkBo();
            dvlBo.setLinkId(docLinkLog.getId());
            dvlBo.setVersionId(version.getId());
            Boolean insertDvl = docVersionLinkService.insertByBo(dvlBo);

            return insertdll && insertDvl;
//            return docLinkLogService.save(docLinkLog);
        }else {
            ivo.linkFileMsgMsgAdd(linkType,num,"数据已存在不予添加");
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBoAndVersion(StandardBo bo) {
        Version version = versionService.queryOneByStandardId(bo.getId());
        if (ObjectUtil.isNull(version)) {
            version = new Version();
        }
        version.setStandardId(bo.getId());
        version.setDocId(bo.getDocId());
        version.setVersionValue(bo.getCurrentVersion());
        version.setStartDate(bo.getStartDate());
        version.setEndDate(bo.getEndDate());
        version.setStatus(Constants.ONE);
        version.setFileId(bo.getFileId());
        version.setReason(bo.getChangeReason());
        version.setReviewTime(bo.getReviewTime());
        version.setForever(bo.getForever());
        versionService.saveOrUpdate(version);
        bo.setStatus(Constants.ZERO);
        boolean bool = updateByBo(bo);
        if (bool) {
            iGenerateIdService.saveOldDocId(bo.getId(),bo.getDocClass(),bo.getDocId());
        }
        return bool;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Standard entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<StandardVo> selectValidFile() {
        return baseMapper.selectValidFile();
    }

    @Override
    public StandardVo getStandardVoByVersionId(String versionId) {
        return this.baseMapper.getStandardVoByVersionId(versionId);
    }

    @Override
    public Boolean deleteByBo(StandardBo bo) {
        versionService.remove(new LambdaQueryWrapper<Version>().eq(Version::getStandardId,bo.getId()));
        return this.removeById(bo.getId());
    }

    @Override
    public StandardVo queryInfoById(String id) {
        return this.baseMapper.queryInfoById(id);
    }
}
