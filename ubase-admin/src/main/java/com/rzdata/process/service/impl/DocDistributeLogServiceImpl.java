package com.rzdata.process.service.impl;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.annotation.DataScope;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.DistributeItemBo;
import com.rzdata.process.domain.bo.DocDistributeLogBo;
import com.rzdata.process.domain.vo.DistributeItemVo;
import com.rzdata.process.domain.vo.DocDistributeLogVo;
import com.rzdata.process.mapper.DocDistributeLogMapper;
import com.rzdata.process.mapper.VersionMapper;
import com.rzdata.process.service.*;
import com.rzdata.system.service.ISysRoleService;
import com.rzdata.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 文件分发记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
@Service
public class DocDistributeLogServiceImpl extends ServicePlusImpl<DocDistributeLogMapper, DocDistributeLog, DocDistributeLogVo> implements IDocDistributeLogService {

    @Autowired
    IPrintLogService iPrintLogService;

    @Autowired
    IRecoveryLogService iRecoveryLogService;

    @Autowired
    IRecoveryLogItemService iRecoveryLogItemService;

    @Autowired
    IPrintLogItemService iPrintLogItemService;

    //@Autowired
    //IPrintLogItemDetailService iPrintLogItemDetailService;

    @Autowired
    IDistributeItemService iDistributeItemService;

    @Autowired
    VersionMapper versionMapper;

    @Autowired
    ISysRoleService sysRoleService;

    @Autowired
    ISysUserService sysUserService;

    @Autowired
    IStandardService standardService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IDocVersionFavoritesService iDocVersionFavoritesService;

    @Override
    public DocDistributeLogVo queryById(String id){
        DocDistributeLogVo vo = getVoById(id);

        if (ObjectUtil.isNotEmpty(vo)) {
            vo.setVersionVo(versionMapper.selectDetailById(vo.getVersionId()));
        }
        return vo;
    }

    @Override
    public List<DistributeItemVo> queryDocDistributeLogVoByDisId(String id) {
        LambdaQueryWrapper<DistributeItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DistributeItem::getDistributeId,id);
        List<DistributeItemVo> list = iDistributeItemService.listVo(queryWrapper);
        //List<DistributeItemVo> DistributeItemVos = this.baseMapper.queryDocDistributeLogVoByAppId(params);
        //if (ObjectUtil.isNotEmpty(docDistributeLogVo)) {
        //    docDistributeLogVo.setVersionVo(versionMapper.selectDetailById(docDistributeLogVo.getVersionId()));
        //}
        return list;
    }

    @Override
    public List<DocDistributeLogVo> queryDocDistributeLogListVoByAppId(Map<String, Object> params) {
        List<DocDistributeLogVo> docDistributeLogVos = this.baseMapper.queryDocDistributeLogListVoByAppId(params);
        return docDistributeLogVos;
    }

    @Override
    public List<String> selectDistributeDept(String docId) {
        return baseMapper.selectDistributeDept(docId);
    }

    @Override
    public List<DocDistributeLogVo> getDocDistributeNumsByDocId(String docId, String versionId) {
        return baseMapper.getDocDistributeNumsByDocId(docId,versionId);
    }

    @Override
    public TableDataInfo<DocDistributeLogVo> queryPageByThisDept(DocDistributeLogBo bo) {
        Page<DocDistributeLogVo> page = PageUtils.buildPage();
        Page<DocDistributeLogVo> docDistributeLogVoPage = baseMapper.queryPageByThisDept(page, bo, SecurityUtils.getDeptId());
        docDistributeLogVoPage.getRecords().forEach(item->{
            item.setInFavorites(iDocVersionFavoritesService.inFavorites(item.getVersionId()));
        });
        return PageUtils.buildDataInfo(docDistributeLogVoPage);
    }

    @Override
    public TableDataInfo<DocDistributeLogVo> queryPageByOtherDept(DocDistributeLogBo bo) {
        Page<DocDistributeLogVo> page = PageUtils.buildPage();
        Page<DocDistributeLogVo> docDistributeLogVoPage = baseMapper.queryPageByOtherDept(page, bo, SecurityUtils.getDeptId());
        docDistributeLogVoPage.getRecords().forEach(item->{
            item.setInFavorites(iDocVersionFavoritesService.inFavorites(item.getVersionId()));
        });
        return PageUtils.buildDataInfo(docDistributeLogVoPage);
    }


    @Override
    @DataScope(deptAlias = "d",userAlias = "su")
    public TableDataInfo<DocDistributeLogVo> queryPageList(DocDistributeLogBo bo) {
        Page<DocDistributeLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<DocDistributeLogVo> iPage = this.baseMapper.selectDocDistributeLog4Page(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    @DataScope(deptAlias = "l")
    public TableDataInfo<DocDistributeLogVo> queryPageListForSign(DocDistributeLogBo bo) {
        //PagePlus<DocDistributeLog, DocDistributeLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        Page<DocDistributeLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());

        //bo.setDeptId(SecurityUtils.getDeptId());
        if (ObjectUtil.isEmpty(bo.getParams().get("endTime"))) {
            bo.getParams().put("endTime",DateUtil.formatDate(DateUtil.tomorrow()));
        }
        Page<DocDistributeLogVo> iPage = this.baseMapper.queryPageListForSign(page, bo);
        iPage.getRecords().forEach(vo->{
            vo.setVersionVo(versionMapper.selectDetailById(vo.getVersionId()));
        });
        return PageUtils.buildDataInfo(iPage);
        //return this.queryPageList(bo);
    }

    @Override
    public TableDataInfo<DocDistributeLogVo> queryNoProcessList(DocDistributeLogBo bo) {
        if (!Objects.equals(bo.getSearchType(), NumberConstants.THREE+"")) {
            bo.setDeptId(SecurityUtils.getDeptId());
        }
        Page<DocDistributeLogVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<DocDistributeLogVo> iPage = this.baseMapper.selectDistributeNoProcessPage(page, bo);
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<DocDistributeLogVo> queryList(DocDistributeLogBo bo) {
        if (!Objects.equals(bo.getSearchType(), NumberConstants.THREE+"")) {
            bo.setDeptId(SecurityUtils.getDeptId());
        }
//        listVo(buildQueryWrapper(bo));
        return this.baseMapper.selectDocDistributeLogList(bo);
    }

    private LambdaQueryWrapper<DocDistributeLog> buildQueryWrapper(DocDistributeLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocDistributeLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), DocDistributeLog::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), DocDistributeLog::getDocId, bo.getDocId());
        lqw.eq(bo.getDeptId() != null, DocDistributeLog::getDeptId, bo.getDeptId());
        lqw.eq(bo.getNums() != null, DocDistributeLog::getNums, bo.getNums());
        lqw.like(StringUtils.isNotBlank(bo.getReceiveUserName()), DocDistributeLog::getReceiveUserName, bo.getReceiveUserName());
        lqw.eq(bo.getDistributeTime() != null, DocDistributeLog::getDistributeTime, bo.getDistributeTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiveStatus()), DocDistributeLog::getReceiveStatus, bo.getReceiveStatus());
        lqw.eq(bo.getReceiveTime() != null, DocDistributeLog::getReceiveTime, bo.getReceiveTime());
        lqw.eq(bo.getDbStatus() != null, DocDistributeLog::getDbStatus, bo.getDbStatus());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocDistributeLogBo bo) {
        DocDistributeLog add = BeanUtil.toBean(bo, DocDistributeLog.class);
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocDistributeLogBo bo) {
        DocDistributeLog update = BeanUtil.toBean(bo, DocDistributeLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocDistributeLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    //@Transactional(rollbackFor = Exception.class)
    @Override
    public void signeFile(String id) {
        //DocDistributeLog distributeLog = this.baseMapper.selectById(id);
        QueryWrapper<DocDistributeLog> queryWrapper = Wrappers.query();
        queryWrapper.lambda().eq(DocDistributeLog::getId, id).eq(DocDistributeLog::getDeptId, SecurityUtils.getDeptId());
        DocDistributeLog distributeLog = this.baseMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(distributeLog)) {
            throw new ServiceException("id错误");
        }

//        PrintLogBo printLogBo = new PrintLogBo();
//        printLogBo.setDistributeId(id);
//        printLogBo.setDocId(distributeLog.getDocId());
//        String logId = iPrintLogService.insertByBo(printLogBo);

        LambdaQueryWrapper<PrintLog> query = Wrappers.lambdaQuery();
        query.eq(PrintLog::getDocId,distributeLog.getDocId());
        query.eq(PrintLog::getDeptId,distributeLog.getDeptId());
        query.eq(PrintLog::getVersionId,distributeLog.getVersionId());
        //通过docId deptId versionId查出是否已经存在了打印记录(用于区分增发是否新增了部门)
        PrintLog one = iPrintLogService.getOne(query);

        LambdaQueryWrapper<RecoveryLog> queryRec = Wrappers.lambdaQuery();
        queryRec.eq(RecoveryLog::getDocId,distributeLog.getDocId());
        queryRec.eq(RecoveryLog::getDeptId,distributeLog.getDeptId());
        queryRec.eq(RecoveryLog::getVersionId,distributeLog.getVersionId());
        //通过docId deptId versionId查出是否已经存在了回收记录(用于区分增发是否新增了部门)
        //RecoveryLog recoveryLog = iRecoveryLogService.getOne(queryRec);

        //更新打印数据
        PrintLog insertData = null;
        if (!Constants.ONE.equals(distributeLog.getNotPrint())) {
            insertData = setPrintLog(distributeLog,one);
        }
        //更新回收数据
        //RecoveryLog setRecoveryLog = setRecoveryLog(distributeLog, recoveryLog);
        //签收后生成分发记录
        if (ObjectUtil.isNotEmpty(distributeLog.getNums())) {
            for (Integer i = 0; i < distributeLog.getNums(); i++) {
                DistributeItemBo itemBo = new DistributeItemBo();
                itemBo.setId(null);
                itemBo.setApplyId(distributeLog.getApplyId());
                itemBo.setDeptId(distributeLog.getDeptId());
                itemBo.setDeptName(distributeLog.getDeptName());
                itemBo.setDistributeId(distributeLog.getId());
                itemBo.setDocId(distributeLog.getDocId());
                itemBo.setDocName(distributeLog.getDocName());
                itemBo.setVersionId(distributeLog.getVersionId());
                itemBo.setDistributeTime(distributeLog.getDistributeTime());
                itemBo.setReceiveUserName(SecurityUtils.getUsername());
                iDistributeItemService.insertByBo(itemBo,distributeLog.getDocClass());
                //生成打印详情
                if (!Constants.ONE.equals(distributeLog.getNotPrint())) {
                    setPrintItemLog(insertData,itemBo,distributeLog);
                }
                //setRecoveryItemLog(setRecoveryLog,itemBo,distributeLog);
            }
        }
        distributeLog.setReceiveUserName(SecurityUtils.getUsername());
        distributeLog.setReceiveNickName(SecurityUtils.getNickname());
        distributeLog.setReceiveTime(new Date());
        distributeLog.setReceiveStatus(NumberConstants.ONE+"");
        this.baseMapper.updateById(distributeLog);
        //生成提醒消息 文件签收后提醒该部门所有人文件生效
        docMessageService.insertMessageByValidity(distributeLog);
    }



    @Transactional(rollbackFor = Exception.class)
    public RecoveryLog setRecoveryLog(DocDistributeLog distributeLog,RecoveryLog one) {
        RecoveryLog recoveryLog = BeanUtil.toBean(distributeLog,RecoveryLog.class);
        recoveryLog.setId(null);
        recoveryLog.setCreateTime(new Date());
        recoveryLog.setNoRecyclNum(distributeLog.getNums());
        switch (distributeLog.getChangeType()){
            case EXTRA:
                //是增发且没有打印记录 说明是增发了新的分发部门 插入新的分发部门数据
                if (ObjectUtil.isEmpty(one)) {
                    iRecoveryLogService.save(recoveryLog);
                    return recoveryLog;
                }else {
                    //否则则为增发原分发部门 更新回收份数=第一次分发数+增发数量
                    one.setNoRecyclNum(one.getNoRecyclNum()+distributeLog.getNums());
                    iRecoveryLogService.saveOrUpdate(one);
                    return one;
                }
            case ADD:
            case UPDATE:
                //新增和修订产生的分发 肯定没有对应的打印记录 直接插入数据
                iRecoveryLogService.save(recoveryLog);
                return recoveryLog;
            default:
                return one;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public PrintLog setPrintLog(DocDistributeLog distributeLog,PrintLog one){
        PrintLog printLog = BeanUtil.toBean(distributeLog,PrintLog.class);
        printLog.setId(null);
        //更新分发数
        printLog.setDistributeNums(distributeLog.getNums());
       switch (distributeLog.getChangeType()){
           case EXTRA:
               //是增发且没有打印记录 说明是增发了新的分发部门 插入新的分发部门数据
               if (ObjectUtil.isEmpty(one)) {
                   printLog.setPrintNums(distributeLog.getNums());
                   iPrintLogService.save(printLog);
                   return printLog;
               }else {
                   //否则则为增发原分发部门 更新分发份数=第一次分发数+增发数量
                   one.setDistributeNums(one.getDistributeNums()+distributeLog.getNums());
                   //打印数=打印数+新分发数
                   one.setPrintNums(one.getPrintNums()+distributeLog.getNums());
                   iPrintLogService.saveOrUpdate(one);
                   return one;
               }
           case ADD:
           case UPDATE:
               //新增和修订产生的分发 肯定没有对应的打印记录 直接插入数据
               printLog.setPrintNums(distributeLog.getNums());
               iPrintLogService.save(printLog);
               return printLog;
           default:
               return one;
       }
    }

    @Transactional(rollbackFor = Exception.class)
    public void setRecoveryItemLog(RecoveryLog setRecoveryLog, DistributeItemBo itemBo, DocDistributeLog distributeLog) {

        RecoveryLogItem recoveryLogItem = BeanUtil.toBean(distributeLog, RecoveryLogItem.class);
        recoveryLogItem.setId(null);
        recoveryLogItem.setRecoveryId(setRecoveryLog.getId());
        recoveryLogItem.setDistributeItemId(itemBo.getId());
        //防止distributeLog中的userName和nickName赋值过来 这两个字段只有在回收时才会赋值
        recoveryLogItem.setUserName(null);
        recoveryLogItem.setNickName(null);
        recoveryLogItem.setStatus("N");
        recoveryLogItem.setCreateTime(new Date());
        iRecoveryLogItemService.save(recoveryLogItem);
    }
    /**
     * 插入打印记录详情
     * @param distributeItem
     * @param distributeLog
     */
    private void setPrintItemLog(PrintLog data,DistributeItemBo distributeItem,DocDistributeLog distributeLog) {
        /*无论是对于分发(新增和修订)还是增发 都增加distributeLog.getNums()条数据就够了 因为如果之前有数据
        distributeLog.getNums()=增发的份数 则插入distributeLog.getNums()条数据
        如果之前没有数据 则说明是新增或者修订或者增发给了新部门 也是要插入distributeLog.getNums()条数据
        */
        PrintLogItem printLogItem = BeanUtil.toBean(distributeLog, PrintLogItem.class);
        printLogItem.setId(null);
        printLogItem.setNums(0);
        printLogItem.setPrintId(data.getId());
        printLogItem.setPrintSource(distributeLog.getChangeType());
        printLogItem.setDistributeId(distributeLog.getId());
        printLogItem.setDistributeItemId(distributeItem.getId());
        iPrintLogItemService.save(printLogItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByDocId(String docId) {
        this.baseMapper.updateByDocId(docId);
    }

}
