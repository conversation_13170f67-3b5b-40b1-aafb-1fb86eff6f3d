package com.rzdata.process.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.DocLinkLog;
import com.rzdata.process.domain.bo.DocLinkLogBo;
import com.rzdata.process.domain.vo.DocLinkLogVo;
import com.rzdata.process.enums.LinkTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * 文件关联记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-06
 */
public interface IDocLinkLogService extends IServicePlus<DocLinkLog, DocLinkLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocLinkLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocLinkLogVo> queryPageList(DocLinkLogBo bo);

	/**
	 * 查询列表
	 */
	List<DocLinkLogVo> queryList(DocLinkLogBo bo);

	/**
	 * 根据新增业务对象插入文件关联记录
	 * @param bo 文件关联记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocLinkLogBo bo);

	/**
	 * 根据编辑业务对象修改文件关联记录
	 * @param bo 文件关联记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocLinkLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);


	/**
	 * 查出文件的关联记录
	 * @param docId
	 * @param linkCode
	 * @param versionId
	 * @return
	 */
	DocLinkLogVo queryLinkVo(String docId,String linkCode,String versionId);


	/**
	 * 查出文件的关联记录(当前生效)
	 * @param docId
	 * @param versionId
	 * @param linkType DOC正文 APPENDIX附件 RECORD记录 REF_DOC关联其他文件
	 * @return
	 */
	List<DocLinkLogVo> queryDocLinkVo(String docId, String versionId, LinkTypeEnum linkType);
}
