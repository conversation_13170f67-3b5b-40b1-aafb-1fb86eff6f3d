package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.model.PageResultModel;
import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.blueland.bpmclient.model.SearchQuery;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.eventbus.StartModifyApplyEvent;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.ModifyApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyBo;
import com.rzdata.process.domain.bo.ReviewApplyItemBo;
import com.rzdata.process.domain.bo.WorkflowApplyLogBo;
import com.rzdata.process.domain.vo.ReviewApplyItemVo;
import com.rzdata.process.domain.vo.ReviewApplyVo;
import com.rzdata.process.domain.vo.StandardVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.*;
import com.rzdata.process.mapper.ReviewApplyMapper;
import com.rzdata.process.service.*;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CreateNoBo;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.WorkflowService;
import com.xxl.job.core.context.XxlJobHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

/**
 * 文件复审申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class ReviewApplyServiceImpl extends ServicePlusImpl<ReviewApplyMapper, ReviewApply, ReviewApplyVo> implements IReviewApplyService {

    @Autowired
    WorkflowService workflowService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowApplyLogService iWorkflowApplyLogService;

    @Autowired
    private IGenerateIdService iGenerateIdService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    IVersionService versionServices;

    @Autowired
    IStandardService standardService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IReviewApplyItemService applyItemService;

    @Override
    public ReviewApplyVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ReviewApplyVo> queryPageList(ReviewApplyBo bo) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setPageNumber(bo.getPageNum());
        searchQuery.setPageSize(bo.getPageSize());
        searchQuery.setStatus(Integer.valueOf(bo.getStatus()));
        searchQuery.setRecUserId(SecurityUtils.getUsername());
        searchQuery.setProcDefKey(bo.getProcDefKey());
        PageResultModel pageResultModel = workflowService.findRecordList(searchQuery);
        List<String> ids = new ArrayList<>();
        List<WorkFlowInfo> workFlowInfos = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(pageResultModel) && pageResultModel.getTotalCount() > 0) {
            workFlowInfos = JSONUtil.toList(JSONUtil.toJsonStr(pageResultModel.getResult()), WorkFlowInfo.class);
            ids = workFlowInfos.stream().map(x -> x.getProcInstId()).collect(Collectors.toList());
        }
        Page<ReviewApplyVo> page = new Page<>(bo.getPageNum(), bo.getPageSize());
        Page<ReviewApplyVo> iPage = this.baseMapper.selectReviewApplyPage(page, bo, ids);
        if (ObjectUtil.isNotEmpty(iPage.getRecords()) && pageResultModel.getTotalCount() > 0) {
            Map<String, WorkFlowInfo> map = workFlowInfos.stream().collect(Collectors.toMap(x -> x.getProcInstId(), x -> x));
            for (ReviewApplyVo record : iPage.getRecords()) {
                WorkFlowInfo workFlowInfo = map.get(record.getWorkflowLogVo().getProcInstId());
                if (ObjectUtil.isNotEmpty(workFlowInfo)) {
                    record.setWorkFlowInfo(workFlowInfo);
                }
            }
        }
        return PageUtils.buildDataInfo(iPage);
    }

    @Override
    public List<ReviewApplyVo> queryList(ReviewApplyBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ReviewApply> buildQueryWrapper(ReviewApplyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReviewApply> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReviewApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyTitle()), ReviewApply::getApplyTitle, bo.getApplyTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getDocId()), ReviewApply::getDocId, bo.getDocId());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewAction()), ReviewApply::getReviewAction, bo.getReviewAction());
        lqw.eq(bo.getDeptId() != null, ReviewApply::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getUserName()), ReviewApply::getUserName, bo.getUserName());
        lqw.eq(bo.getApplyTime() != null, ReviewApply::getApplyTime, bo.getApplyTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), ReviewApply::getReason, bo.getReason());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ReviewApply::getStatus, bo.getStatus());
        return lqw;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertByBo(ReviewApplyBo bo) {
        if(StringUtils.isBlank(bo.getId())) {
            if (bo.getDocIds() != null && bo.getDocIds().size() > 0) {

                ReviewApply add = BeanUtil.toBean(bo, ReviewApply.class);
                add.setId(null);
                bo.setChangeType(ApplyTypeEnum.REVIEW.toString());
                LoginUser loginUser = SecurityUtils.getLoginUser();
                add.setId(iGenerateIdService.generateApplyId(ApplyTypeEnum.REVIEW, loginUser.getUsername()));
//                add.setApplyTitle(bo.getApplyTitle());
                validEntityBeforeSave(add);
                add.setApplyTime(new Date());
                add.setUserName(SecurityUtils.getUsername());
                add.setDeptId(SecurityUtils.getDeptId());
                add.setStatus(ProcessStatusConstants.TO_DO);
                bo.setId(add.getId());
                boolean flag = saveOrUpdate(add);

                // 添加文件复审文件清单
                Boolean itemFlag = addReviewApplyItem(bo, add.getId());

                if (flag && itemFlag) {
                    XxlJobHelper.log("成功");
                    bo.setUserName(SecurityUtils.getUsername());
                    bo.setDeptId(SecurityUtils.getDeptId());
//                bo.setDocId(docId);
                    // 开启流程 调用工作流相关接口
                    try {
                        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
                        ProcessInstanceModel processInstanceModel = workflowService.startAndSubmitProcess("文件复审申请",
                                processConfig.getProcDefKeyFSSQ(), SecurityUtils.getUsername(), SecurityUtils.getDeptId().toString(),
                                add.getId());
                        saveWorkFlowLog(bo, processInstanceModel);
                    } catch (Exception e) {
                        XxlJobHelper.log("失败" + e.getMessage() + e.getStackTrace());
                        e.printStackTrace();
                    }
                }

//            bo.getDocIds().forEach(docId -> {
//                ReviewApply add = BeanUtil.toBean(bo, ReviewApply.class);
//                add.setId(null);
//                LoginUser loginUser = SecurityUtils.getLoginUser();
//                add.setId(iGenerateIdService.generateApplyId(ApplyTypeEnum.REVIEW,loginUser.getUsername()));
//                add.setApplyTitle("文件复审申请");
//                validEntityBeforeSave(add);
//                add.setApplyTime(new Date());
//                add.setUserName(SecurityUtils.getUsername());
//                add.setDeptId(SecurityUtils.getDeptId());
//                add.setStatus(NumberConstants.ONE + "");
//                //如果size有数据
//                //遍历赋值
//                add.setDocId(docId);
//                bo.setId(add.getId());
//                boolean flag = saveOrUpdate(add);
//                if (flag) {
//                    XxlJobHelper.log("成功");
//                    bo.setUserName(SecurityUtils.getUsername());
//                    bo.setDeptId(SecurityUtils.getDeptId());
//                    bo.setDocId(docId);
//                    // 开启流程 调用工作流相关接口
//                    try {
//                        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
//                        ProcessInstanceModel processInstanceModel = workflowService.startAndSubmitProcess("文件复审申请",
//                                processConfig.getProcDefKeyFSSQ(), SecurityUtils.getUsername(), SecurityUtils.getDeptId().toString(),
//                                add.getId());
//                        saveWorkFlowLog(bo, processInstanceModel);
//                    } catch (Exception e) {
//                        XxlJobHelper.log("失败" + e.getMessage() + e.getStackTrace());
//                        e.printStackTrace();
//                    }
//                }
//            });
            }
        }else{
            ReviewApply update = BeanUtil.toBean(bo, ReviewApply.class);
            validEntityBeforeSave(update);
            updateById(update);
            // 根据复审id查询复审文件清单表
            QueryWrapper<ReviewApplyItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ReviewApplyItem::getApplyId,bo.getId());
            List<ReviewApplyItem> items = applyItemService.list(queryWrapper);
            // 删除文件复审清单
            boolean remove = applyItemService.removeByIds(items.stream().map(item -> item.getId()).collect(Collectors.toList()));
            if(remove){
                addReviewApplyItem(bo,bo.getId());
            }
        }
    }

    /**
     * 新增文件复审清单
     * @param bo    bo对象
     * @param id    复审id
     * @return      是否新增成功
     */
    private Boolean addReviewApplyItem(ReviewApplyBo bo, String id) {
        List<ReviewApplyItem> items = new ArrayList<>();
        LambdaQueryWrapper<Version> queryVersion = new LambdaQueryWrapper<>();
        queryVersion.in(Version::getDocId,bo.getDocIds());
        queryVersion.eq(Version::getStatus,NumberConstants.ONE);
        List<Version> versionList = versionServices.list(queryVersion);
        Map<String, Version> map = versionList.stream().collect(Collectors.toMap(Version::getDocId, version->version));
        Map<String, StandardVo> voMap = standardService.queryByIdList(versionList.stream().map(i->i.getStandardId()).collect(Collectors.toList()));
        for (String docId : bo.getDocIds()){
            ReviewApplyItem item = new ReviewApplyItem();
            String standardId = map.get(docId).getStandardId();
            item.setId(IdUtil.simpleUUID());
            item.setApplyId(id);
            item.setDocId(standardId);
            item.setDocClass(voMap.get(standardId).getDocClass());
            item.setVersionId(map.get(docId).getId());
            item.setVersionValue(map.get(docId).getVersionValue());
            item.setDocName(voMap.get(standardId).getDocName());
            items.add(item);
        }
        return applyItemService.saveAll(items);
    }

    /**
     * 保存申请记录
     *
     * @param bo
     * @param processInstanceModel
     */
    private void saveWorkFlowLog(ReviewApplyBo bo, ProcessInstanceModel processInstanceModel) {
        WorkflowApplyLogBo applyLogBo = new WorkflowApplyLogBo();
        applyLogBo.setId(bo.getId());
        applyLogBo.setApplyClass(ApplyTypeEnum.REVIEW.name());
        applyLogBo.setDocId(bo.getDocId());
        applyLogBo.setProcDefKey(processInstanceModel.getProcDefId());
        applyLogBo.setProcInstId(processInstanceModel.getProcInstId());
        applyLogBo.setApplyStatus(ApplyStatusEnum.PROCESSING.getCode());
        applyLogBo.setProcStatus(ProcessStatusConstants.TO_DO);
        iWorkflowApplyLogService.insertByBo(applyLogBo);
    }

    @Override
    public Boolean updateByBo(ReviewApplyBo bo) {
        ReviewApply update = BeanUtil.toBean(bo, ReviewApply.class);
        bo.setChangeType(ApplyTypeEnum.REVIEW.toString());
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 同步事件监听处理
     */
    @Subscribe
    @AllowConcurrentEvents
    protected void onProcessEvent(ProcessResultEvent event) {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        if (!Optional.ofNullable(event).map(e -> e.getModel()).map(m -> m.getWf_procDefKey()).filter(key -> key.equals(processConfig.getProcDefKeyFSSQ())).isPresent()) {
            return;
        }
        ReviewApply reviewApply = this.baseMapper.selectById(event.getApplyId());
        if (ObjectUtil.isEmpty(reviewApply)) {
            return;
        }
        reviewApply.setStatus(event.getStatus());
        reviewApply.setUpdateTime(new Date());
        // wf_actionType ADD UPDATE DISUSE
//        reviewApply.setReviewAction(event.getModel().getWf_actionType());
        reviewApply.setReviewAction(event.getApplyStatus());
        this.baseMapper.updateById(reviewApply);

        // 如果复审标准为：文件延长有效期 修改文件生效截止日期和复审日期
        if(ApplyStatusEnum.EXTENSION.getCode().equals(event.getApplyStatus())){
            updateDocVersion(reviewApply);
        }

        String status = event.getStatus();
        String actDefName = event.getModel().getWf_nextActDefName();
        if (Objects.equals(ProcessStatusConstants.TO_DONE, event.getStatus())) {
            status = ProcessStatusConstants.TO_DONE;
            event.setMsgInfo(ApplyStatusEnum.getMsg(event.getApplyStatus()));
            docMessageService.insertMessage(event, MsgTypeEnum.REVIEW);
        }
        //流程结束
        if (ProcessStatusConstants.TO_DONE.equals(event.getStatus())) {
//            startModifyApply(reviewApply);
            actDefName = "结束";
        }
        iWorkflowApplyLogService.updateStatusByBusId(event.getApplyId(), status, actDefName, event.getApplyStatus());

    }

    /**
     * 复审标准为：文件延长有效期时
     * @param reviewApply 文件复审对象
     */
    private void updateDocVersion(ReviewApply reviewApply) {
        // 修改复审文件清单
        String applyId = reviewApply.getId();
        ReviewApplyItemBo bo = new ReviewApplyItemBo();
        bo.setIds(Collections.singletonList(applyId));
        List<ReviewApplyItemVo> itemList = applyItemService.queryItemList(bo);

        List<String> versionIds = itemList.stream().map(ReviewApplyItemVo::getVersionId).collect(Collectors.toList());
        List<String> classS = itemList.stream().map(ReviewApplyItemVo::getDocClass).collect(Collectors.toList());
        QueryWrapper<Version> versionQueryWrapper = new QueryWrapper<>();
        versionQueryWrapper.lambda().in(Version::getId,versionIds);
        versionQueryWrapper.lambda().eq(Version::getStatus, Constants.ONE);
        List<Version> versionList = versionServices.list(versionQueryWrapper);
        Map<String, ReviewApplyItemVo> voMap = itemList.stream().collect(Collectors.toMap(ReviewApplyItemVo::getVersionId, i -> i));

        QueryWrapper<DocClass> docClassQueryWrapper = new QueryWrapper<>();
        docClassQueryWrapper.lambda().in(DocClass::getId,classS);
        List<DocClass> docClassList = iDocClassService.list(docClassQueryWrapper);
        for(Version version: versionList) {
            ReviewApplyItemVo itemVo = voMap.get(version.getId());
            for (DocClass docClass : docClassList) {
                // 当文件有效期和复审时间启用时才修改
                if (StringUtils.equals(itemVo.getDocClass(), docClass.getId()) && StringUtils.equals(docClass.getOpenPrescription(), "'true'")) {
                    version.setEndDate(DateUtil.offset(DateUtils.addYears(DateUtils.getNowDate(), docClass.getExpiration().intValue()), DateField.DAY_OF_MONTH,-1));
                }
                if (StringUtils.equals(itemVo.getDocClass(), docClass.getId()) && StringUtils.equals(docClass.getOpenReview(), "'true'")) {
                    version.setReviewTime(DateUtil.offset(DateUtils.addYears(DateUtils.getNowDate(), docClass.getReviewCycle().intValue()),DateField.DAY_OF_MONTH,-1));
                }
            }
        }
        versionServices.updateBatchById(versionList);
    }

    /**
     * 开启新增废流程
     */
    private void startModifyApply(ReviewApply reviewApply) {
        if (ObjectUtil.isNotEmpty(reviewApply.getReviewAction())) {
            ModifyApplyBo modifyApplyBo = BeanUtil.toBean(reviewApply, ModifyApplyBo.class);
//        modifyApplyBo.setUserName(reviewApply.getEditUserName());
            modifyApplyBo.setCreateTime(new Date());
            modifyApplyBo.setDeptId(SecurityUtils.getDeptId());
            modifyApplyBo.setUserName(SecurityUtils.getUsername());
            modifyApplyBo.setApplyTime(new Date());
            modifyApplyBo.setChangeType(reviewApply.getReviewAction());
            modifyApplyBo.setDocId(reviewApply.getDocId());
            ProcessEventBus.post(new StartModifyApplyEvent(reviewApply.getId(), ApplyTypeEnum.REVIEW.name(), modifyApplyBo));
        }
    }

    /**
     * 注册事件
     */
    @PostConstruct
    private void registerEventBus() {
        ProcessEventBus.register(this);
    }

    /**
     * 取消注册事件
     */
    @PreDestroy
    private void unregisterEventBus() {
        ProcessEventBus.unregister(this);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ReviewApply entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<ReviewApplyVo> selectUndueList(List<String> statusList) {
        return baseMapper.selectUndueList(statusList);
    }
}
