package com.rzdata.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.process.domain.PrintLogItemDetail;
import com.rzdata.process.domain.bo.PrintLogItemDetailBo;
import com.rzdata.process.domain.vo.PrintLogItemDetailVo;
import com.rzdata.process.mapper.PrintLogItemDetailMapper;
import com.rzdata.process.service.IPrintLogItemDetailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/4/7 10:51
 * @Version 1.0
 * @Description
 */
@Service
public class PrintLogItemDetailServiceImpl extends ServicePlusImpl<PrintLogItemDetailMapper, PrintLogItemDetail, PrintLogItemDetailVo> implements IPrintLogItemDetailService {

    @Override
    public TableDataInfo<PrintLogItemDetailVo> queryPageList(PrintLogItemDetailBo bo) {
        LambdaQueryWrapper<PrintLogItemDetail> query = Wrappers.lambdaQuery();
        query.eq(PrintLogItemDetail::getItemId,bo.getItemId());
        query.orderByAsc(PrintLogItemDetail::getPrintTime);
        PagePlus<PrintLogItemDetail, PrintLogItemDetailVo> result = pageVo(PageUtils.buildPagePlus(), query);
        return PageUtils.buildDataInfo(result);
    }
}
