package com.rzdata.process.service.impl;

import com.rzdata.framework.constant.DocConstants;
import com.rzdata.process.domain.bo.DocPreviewBo;
import com.rzdata.process.service.IDocCacheService;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

/**
 * 默认的文档缓存业务实现（基于 Caffeine cache）
 * 未配置Redis时，注入该Bean
 * @author: wangyang
 * @date: 2022年1月21日14:52:09
 */
@Slf4j
@Service
@ConditionalOnMissingBean(RedisDocCacheServiceImpl.class)
public class DefaultDocCacheServiceImpl implements IDocCacheService {
    /**
     *   暂时默认过期时间1天，最大数量5w
      */
    private Cache<String, DocPreviewBo> dc = Caffeine.newBuilder()
            .expireAfterWrite(DocConstants.CACHE_DURATION)
            .maximumSize(50_000)
            .build();

    @Override
    public boolean put(String documentKey, DocPreviewBo doc) {
        if (doc == null) {
            return false;
        }
        dc.put(documentKey, doc);
        return true;
    }

    @Override
    public DocPreviewBo get(String documentKey) {
        return dc.getIfPresent(documentKey);
    }

    @Override
    public void remove(String documentKey) {
        dc.invalidate(documentKey);
    }
}
