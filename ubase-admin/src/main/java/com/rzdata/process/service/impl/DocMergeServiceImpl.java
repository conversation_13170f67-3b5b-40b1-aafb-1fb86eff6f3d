package com.rzdata.process.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.*;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.service.*;
import com.rzdata.process.utils.FileMergeUtils;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.service.IDocClassMergeService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/19 10:59
 * @Version 1.0
 * @Description 文件合稿实现类
 */
@Service
@Slf4j
public class DocMergeServiceImpl implements IDocMergeService {

    @Resource
    private IBasicFileService iBasicFileService;

    @Resource
    IStandardService standardService;

    @Resource
    ISysConfigService sysConfigService;

    @Resource
    FileUploadDownloadService fileUploadService;

    @Resource
    IVersionService versionService;

    @Resource
    IDocClassService docClassService;

    @Resource
    IWorkflowLogService workflowLogService;

    @Resource
    IDocClassMergeService docClassMergeService;

    @Override
    public AjaxResult merge(DocMergeBo docMergeBo) {

        //附件文件路径list
        List<String> attachmentDocFiles;
        //基础信息
        DocMergeBasicInfoBo basicInfo = docMergeBo.getBasicInfo();
        //拿到该文件的文件设置
        DocClassVo docClassVo = docClassService.getVoById(basicInfo.getDocClass());
        String openMerge = docClassVo.getOpenMerge();
        if (ObjectUtil.equals("'false'", openMerge)) {
            return AjaxResult.error("未启用文件合稿设置!", null);
        }
        try {
            //拿到主文件文档在服务器上的路径
            String mainFilePath = FileMergeUtils.getFilePathById(basicInfo.getFileId());
            //String mainFilePath = "store\\file\\20220120103617\\合稿测试-主文件.docx";
            //拷贝一份出来操作 下面都是用这个文件来操作 不会在修改主文件
            String copyFilePath = FileMergeUtils.copyFileRename(mainFilePath, "_merge");
            //合并附件内容
            String[] attachmentDocFileId = new String[0];
            //附件(多个) 用,分割
            if (StringUtils.isNotEmpty(basicInfo.getAppendixs())) {
                attachmentDocFileId = basicInfo.getAppendixs().split(",");
            }
            //通过主文件id找到文件路径 并拿到文件实体
            attachmentDocFiles = getDocFilePath(attachmentDocFileId);
            for (String path : attachmentDocFiles) {
                if (!(path.endsWith(".doc") || path.endsWith(".docx"))) {
                    return AjaxResult.error("只支持doc/docx文件合稿", null);
                }
            }
            //先把附件的内容合并到复制的主文件中
            attachmentDocFiles.forEach(path -> {
                FileMergeUtils.mergeInsertDoc(copyFilePath, path);
            });
            //如果添加基础信息
            //if (docMergeBo.isMergeBasicInfo()) {

            String basicInfoTemplateId = docClassMergeService.getTemplateIdByDocClass(basicInfo.getDocClass(), "基础信息");
            basicInfo.setTemplateDocId(basicInfoTemplateId);
            //拿到新的赋值后的新基础信息doc文档
            //给模板文档进行占位符替换 并生成一个新的临时文件地址(也不操作模板文件)
            String basicInfoDocItemPath;
            try {
                basicInfoDocItemPath = FileMergeUtils.dealBasicInfoTemplate(basicInfo);
            }catch (FileNotFoundException e){
                e.printStackTrace();
                return AjaxResult.error(e.getMessage());
            }
            //给主文件加一个 不加一页后逐个追加
            FileMergeUtils.addSection(copyFilePath);
            FileMergeUtils.clearSection(copyFilePath);
            FileMergeUtils.mergeDoc(copyFilePath, basicInfoDocItemPath);
            FileUtils.deleteQuietly(FileUtils.getFile(basicInfoDocItemPath));
            //}


            //获取编制(起草)记录
            List<WorkflowLogVo> draftRecord = workflowLogService.queryApprovalRecord(docMergeBo.getApplyId(), "编制");
            DocMergeApprovalTemplateBo docMergeApprovalTemplateBo = new DocMergeApprovalTemplateBo();
            DocMergeApprovalInfoBo draft = new DocMergeApprovalInfoBo();
            draft.setUsername(draftRecord.get(0).getNickName());
            draft.setDateTime(DateUtil.format(draftRecord.get(0).getCreateTime(), "yyyy-MM-dd HH:mm:SS"));
            draft.setDept(draftRecord.get(0).getDeptName());
            docMergeApprovalTemplateBo.setDraft(draft);

            //审核
            List<WorkflowLogVo> approvalsRecord = workflowLogService.queryApprovalRecord(docMergeBo.getApplyId(), "审核");
            List<DocMergeApprovalInfoBo> approvals = new ArrayList();
            approvalsRecord.forEach(vo -> {
                DocMergeApprovalInfoBo approval = new DocMergeApprovalInfoBo();
                approval.setUsername(vo.getNickName());
                approval.setDept(vo.getDeptName());
                approval.setDateTime(DateUtil.format(vo.getCreateTime(), "yyyy-MM-dd HH:mm:SS"));
                approvals.add(approval);
            });
            docMergeApprovalTemplateBo.setApprovals(approvals);
            //批准
            List<WorkflowLogVo> allowsRecord = workflowLogService.queryApprovalRecord(docMergeBo.getApplyId(), "批准");
            DocMergeApprovalInfoBo allow = new DocMergeApprovalInfoBo();
            allow.setUsername(allowsRecord.get(0).getNickName());
            allow.setDateTime(DateUtil.format(allowsRecord.get(0).getCreateTime(), "yyyy-MM-dd HH:mm:SS"));
            allow.setDept(allowsRecord.get(0).getDeptName());
            docMergeApprovalTemplateBo.setAllow(allow);

            //拿到审批记录
            String templateId = docClassMergeService.getTemplateIdByDocClass(basicInfo.getDocClass(), "审批记录");
            docMergeApprovalTemplateBo.setTemplateDocId(templateId);

            //如果添加审批记录
            //if (docMergeBo.isMergeApprovalRecord()) {
            String itemDocPath;
            try {
                itemDocPath = FileMergeUtils.dealApprovalTemplate(docMergeApprovalTemplateBo);
            }catch (FileNotFoundException e){
                e.printStackTrace();
                return AjaxResult.error(e.getMessage());
            }
            //拿到新的赋值后的新基础信息doc文档
            FileMergeUtils.addSection(copyFilePath);
            FileMergeUtils.mergeDoc(copyFilePath, itemDocPath);
            //用完就把临时文件删除
            FileUtils.deleteQuietly(FileUtils.getFile(itemDocPath));
            //}

            //如果使用页眉页码模板 这个方法放在倒数第二位执行 方便格式化所有页面
            if (docMergeBo.isUseNewHeadFoot()) {
                //拿到新的赋值后的新基础信息doc文档
                String itemHeadFootDocPath = FileMergeUtils.dealHeadTemplate(docMergeBo.getHeadFootInfo());
                FileMergeUtils.removeHeadFoot(copyFilePath);
                FileMergeUtils.copyHeadFoot(copyFilePath, itemHeadFootDocPath);
                //用完就把临时文件删除
                FileUtils.deleteQuietly(FileUtils.getFile(itemHeadFootDocPath));
            }

            //添加水印
            FileMergeUtils.insertTextWatermark(copyFilePath, "严禁复制");
            //删除批注
            FileMergeUtils.clearComments(copyFilePath);
            BasicFileBo basicFileBo = new BasicFileBo();
            File file = FileUtils.getFile(copyFilePath);
            basicFileBo.setFileName(file.getName());
            basicFileBo.setFilePath(file.getParentFile().getName() + File.separator + file.getName());
            basicFileBo.setCreateTime(new Date());
            Boolean asPlatform = Boolean.valueOf(sysConfigService.selectConfigByKey(Constants.ASAS7_PLATFORM));
            if (asPlatform) {
                // 调用爱数上传附件接口
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(file.getPath());
                basicFileBo.setExternalFilePath(fileOsbeginuploadRes.getName());
                basicFileBo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                basicFileBo.setExternalRev(fileOsbeginuploadRes.getRev());
            }
            String id = iBasicFileService.insertByBo(basicFileBo);
            Standard standard = new Standard();
            standard.setId(basicInfo.getFileId());
            standard.setMergeFileId(id);
            standardService.updateById(standard);
            LambdaQueryWrapper<Version> query = new LambdaQueryWrapper();
            query.eq(Version::getDocId, basicInfo.getDocId());
            query.eq(Version::getVersionValue, basicInfo.getVersionValue());
            Version voOne = versionService.getOne(query);
            if (voOne != null) {
                voOne.setMergeFileId(id);
                versionService.updateById(voOne);
            }
            return AjaxResult.success("合稿成功", id);
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }


    @Override
    public AjaxResult mergeForRecord(DocMergeBo docMergeBo) {
        //附件文件路径list
        List<String> attachmentDocFiles;
        //基础信息
        DocMergeBasicInfoBo basicInfo = docMergeBo.getBasicInfo();
        try {
            //拿到主文件文档在服务器上的路径
            String mainFilePath = FileMergeUtils.getFilePathById(basicInfo.getFileId());
            //String mainFilePath = "store\\file\\20220120103617\\合稿测试-主文件.docx";
            //拷贝一份出来操作 下面都是用这个文件来操作 不会在修改主文件
            String copyFilePath = FileMergeUtils.copyFileRename(mainFilePath, "_merge");
            //合并附件内容
            String[] attachmentDocFileId = new String[0];
            //附件(多个) 用,分割
            if (StringUtils.isNotEmpty(basicInfo.getAppendixs())) {
                attachmentDocFileId = basicInfo.getAppendixs().split(",");
            }
            //通过主文件id找到文件路径 并拿到文件实体
            attachmentDocFiles = getDocFilePath(attachmentDocFileId);
            for (String path : attachmentDocFiles) {
                if (!(path.endsWith(".doc") || path.endsWith(".docx"))) {
                    return AjaxResult.error("只支持doc/docx文件合稿", null);
                }
            }
            //先把附件的内容合并到复制的主文件中
            FileMergeUtils.mergeDoc(copyFilePath, attachmentDocFiles);
            //如果添加基础信息
            if (docMergeBo.isMergeBasicInfo()) {
                //拿到新的赋值后的新基础信息doc文档
                //给模板文档进行占位符替换 并生成一个新的临时文件地址(也不操作模板文件)
                String basicInfoDocItemPath = FileMergeUtils.dealBasicInfoTemplate(basicInfo);
                //给主文件加一个 不加一页后逐个追加
                FileMergeUtils.addSection(copyFilePath);
                FileMergeUtils.clearSection(copyFilePath);
                FileMergeUtils.mergeDoc(copyFilePath, basicInfoDocItemPath);
                FileUtils.deleteQuietly(FileUtils.getFile(basicInfoDocItemPath));
            }
            //如果添加审批记录
            if (docMergeBo.isMergeApprovalRecord()) {
                //拿到新的赋值后的新基础信息doc文档
                String itemDocPath = FileMergeUtils.dealApprovalTemplate(docMergeBo.getApprovalRecord());
                FileMergeUtils.addSection(copyFilePath);
                FileMergeUtils.mergeDoc(copyFilePath, itemDocPath);
                //用完就把临时文件删除
                FileUtils.deleteQuietly(FileUtils.getFile(itemDocPath));
            }

            //如果使用页眉页码模板 这个方法放在倒数第二位执行 方便格式化所有页面
            if (docMergeBo.isUseNewHeadFoot()) {
                //拿到新的赋值后的新基础信息doc文档
                String itemHeadFootDocPath = FileMergeUtils.dealHeadTemplate(docMergeBo.getHeadFootInfo());
                FileMergeUtils.removeHeadFoot(copyFilePath);
                FileMergeUtils.copyHeadFoot(copyFilePath, itemHeadFootDocPath);
                //用完就把临时文件删除
                FileUtils.deleteQuietly(FileUtils.getFile(itemHeadFootDocPath));
            }

            //添加水印
            FileMergeUtils.insertTextWatermark(copyFilePath, "严禁复制");

            BasicFileBo basicFileBo = new BasicFileBo();
            File file = FileUtils.getFile(copyFilePath);
            basicFileBo.setFileName(file.getName());
            basicFileBo.setFilePath(file.getParentFile().getName() + File.separator + file.getName());
            basicFileBo.setCreateTime(new Date());
            Boolean asPlatform = Boolean.valueOf(sysConfigService.selectConfigByKey(Constants.ASAS7_PLATFORM));
            if (asPlatform) {
                // 调用爱数上传附件接口
                FileOsbeginuploadRes fileOsbeginuploadRes = fileUploadService.singleUpload(file.getPath());
                basicFileBo.setExternalFilePath(fileOsbeginuploadRes.getName());
                basicFileBo.setExternalFileId(fileOsbeginuploadRes.getDocid());
                basicFileBo.setExternalRev(fileOsbeginuploadRes.getRev());
            }
            String id = iBasicFileService.insertByBo(basicFileBo);
            Standard standard = new Standard();
            standard.setId(basicInfo.getDocId());
            standard.setMergeFileId(id);
            standardService.updateById(standard);
            LambdaQueryWrapper<Version> query = new LambdaQueryWrapper();
            query.eq(Version::getDocId, basicInfo.getDocId());
            query.eq(Version::getVersionValue, basicInfo.getVersionValue());
            Version voOne = versionService.getOne(query);
            if (voOne != null) {
                voOne.setMergeFileId(id);
                versionService.updateById(voOne);
            }
            return AjaxResult.success("合稿成功", id);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.error();
    }


    /**
     * 批量获取文件
     *
     * @param docFileIds 文件id
     * @return 文件实体
     */
    private List<String> getDocFilePath(String[] docFileIds) throws FileNotFoundException {
        ArrayList<String> pathList = new ArrayList<>();
        for (String docFileId : docFileIds) {
            pathList.add(FileMergeUtils.getFilePathById(docFileId));
        }
        return pathList;
    }
}
