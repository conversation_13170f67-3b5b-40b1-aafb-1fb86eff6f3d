package com.rzdata.process.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.*;
import com.rzdata.process.domain.bo.PrintLogItemBo;
import com.rzdata.process.domain.vo.PrintLogItemVo;
import com.rzdata.process.mapper.PrintLogItemMapper;
import com.rzdata.process.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 文件打印记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-07
 */
@Service
public class PrintLogItemServiceImpl extends ServicePlusImpl<PrintLogItemMapper, PrintLogItem, PrintLogItemVo> implements IPrintLogItemService {

    @Resource
    IPrintLogService printLogService;

    @Resource
    IPrintLogItemDetailService printLogItemDetailService;

    @Resource
    IReissueApplyService reissueApplyService;

    @Resource
    IExtraApplyService extraApplyService;

    @Override
    public PrintLogItemVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<PrintLogItem> queryPageList(PrintLogItemBo bo) {
        LambdaQueryWrapper <PrintLogItem>query = Wrappers.lambdaQuery();
        query.eq(PrintLogItem::getPrintId,bo.getPrintId());
        Page<PrintLogItem> result = page(PageUtils.buildPage(), query);
        result.getRecords().forEach( item->{
            List<ReissueApply> reissueApplyList = reissueApplyService.listByDisItemId(item.getDistributeItemId());
            item.setReissueApplyList(reissueApplyList);
            ExtraApply extraApply = extraApplyService.getById(item.getApplyId());
            item.setExtraApply(extraApply);
        });
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<PrintLogItemVo> queryList(PrintLogItemBo bo) {
        LambdaQueryWrapper<PrintLogItem> query = new LambdaQueryWrapper<>();
        query.eq(PrintLogItem::getPrintId,bo.getPrintId());
        return listVo(query);
    }

    @Override
    public String insertByBo(PrintLogItemBo bo) {
        return null;
    }

    @Override
    public Boolean updateByBo(PrintLogItemBo bo) {
        return null;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return null;
    }

    @Override
    public PrintLog selectPrintByDistId(String id) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult<Void> updatePrintStatusById(String[] ids) {
        Arrays.stream(ids).forEach(id->{
            PrintLogItem printLogItem = getById(id);
//            if (ObjectUtil.isEmpty(printLogItem)) {
//                return AjaxResult.error();
//            }
            printLogItem.setStatus("Y");

            //如果没有打印时间 说明是第一次打印 记录首次打印时间
            Date printDate = new Date();
            if (ObjectUtil.isEmpty(printLogItem.getPrintTime())) {
                printLogItem.setPrintTime(printDate);
            }

            printLogItem.setPrintUserName(SecurityUtils.getUsername());
            printLogItem.setPrintUserNickName(SecurityUtils.getNickname());
            //打印次数在原来的次数上+1
            printLogItem.setNums(printLogItem.getNums()+1);

            updateById(printLogItem);

            //还要更新printLog表的已打印数量
            PrintLog printLog = printLogService.getById(printLogItem.getPrintId());
            /*if (ObjectUtil.isEmpty(printLog)) {
                return AjaxResult.error("未找到打印记录");
            }*/
            //已打印数量+1
            printLog.setPrintedNums(printLog.getPrintedNums()+1);
            printLogService.updateById(printLog);

            //插入打印明细数据
            PrintLogItemDetail detail = new PrintLogItemDetail();
            detail.setId(null);
            detail.setItemId(id);
            detail.setPrintTime(printDate);
            detail.setUserName(SecurityUtils.getUsername());
            detail.setUserNickName(SecurityUtils.getNickname());
            detail.setPrintType(1);
            printLogItemDetailService.save(detail);
        });

        return AjaxResult.success("打印成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult reprint(String [] ids) {
        Arrays.stream(ids).forEach(id->{
            //插入重打明细数据
            PrintLogItemDetail detail = new PrintLogItemDetail();
            detail.setId(null);
            detail.setItemId(id);
            detail.setPrintTime(new Date());
            detail.setUserName(SecurityUtils.getUsername());
            detail.setUserNickName(SecurityUtils.getNickname());
            detail.setPrintType(2);
            printLogItemDetailService.save(detail);
        });
        return AjaxResult.success();
    }

}
