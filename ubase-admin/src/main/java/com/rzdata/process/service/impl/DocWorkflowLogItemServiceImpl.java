package com.rzdata.process.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DocWorkflowLogItem;
import com.rzdata.process.domain.bo.DocWorkflowLogItemBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.vo.DocWorkflowLogItemVo;
import com.rzdata.process.mapper.DocWorkflowLogItemMapper;
import com.rzdata.process.service.IDocWorkflowLogItemService;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
@Service
public class DocWorkflowLogItemServiceImpl extends ServicePlusImpl<DocWorkflowLogItemMapper, DocWorkflowLogItem, DocWorkflowLogItemVo> implements IDocWorkflowLogItemService {

    @Override
    public DocWorkflowLogItemVo queryById(Long id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocWorkflowLogItemVo> queryPageList(DocWorkflowLogItemBo bo) {
        PagePlus<DocWorkflowLogItem, DocWorkflowLogItemVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocWorkflowLogItemVo> queryList(DocWorkflowLogItemBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocWorkflowLogItem> buildQueryWrapper(DocWorkflowLogItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocWorkflowLogItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverMan()), DocWorkflowLogItem::getReceiverMan, bo.getReceiverMan());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiverOrg()), DocWorkflowLogItem::getReceiverOrg, bo.getReceiverOrg());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkflowLogId()), DocWorkflowLogItem::getWorkflowLogId, bo.getWorkflowLogId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocWorkflowLogItemBo bo) {
        DocWorkflowLogItem add = BeanUtil.toBean(bo, DocWorkflowLogItem.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocWorkflowLogItemBo bo) {
        DocWorkflowLogItem update = BeanUtil.toBean(bo, DocWorkflowLogItem.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocWorkflowLogItem entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
