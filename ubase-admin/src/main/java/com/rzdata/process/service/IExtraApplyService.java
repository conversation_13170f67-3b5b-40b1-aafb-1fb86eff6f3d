package com.rzdata.process.service;

import com.blueland.bpmclient.model.ProcessInstanceModel;
import com.rzdata.process.domain.ExtraApply;
import com.rzdata.process.domain.vo.ExtraApplyVo;
import com.rzdata.process.domain.bo.ExtraApplyBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件增发申请Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IExtraApplyService extends IServicePlus<ExtraApply, ExtraApplyVo> {
	/**
	 * 查询单个
	 * @return
	 */
	ExtraApplyVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<ExtraApplyVo> queryPageList(ExtraApplyBo bo);

	/**
	 * 查询列表
	 */
	List<ExtraApplyVo> queryList(ExtraApplyBo bo);

	/**
	 * 根据新增业务对象插入文件增发申请
	 * @param bo 文件增发申请新增业务对象
	 * @return
	 */
	ProcessInstanceModel insertByBo(ExtraApplyBo bo);

	/**
	 * 根据编辑业务对象修改文件增发申请
	 * @param bo 文件增发申请编辑业务对象
	 * @return
	 */
	Boolean updateByBo(ExtraApplyBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
