package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.process.domain.bo.ApplyRelationBo;
import com.rzdata.process.domain.vo.ApplyRelationVo;
import com.rzdata.process.domain.ApplyRelation;
import com.rzdata.process.mapper.ApplyRelationMapper;
import com.rzdata.process.service.IApplyRelationService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文件申请流程关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class ApplyRelationServiceImpl extends ServicePlusImpl<ApplyRelationMapper, ApplyRelation, ApplyRelationVo> implements IApplyRelationService {

    @Override
    public ApplyRelationVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<ApplyRelationVo> queryPageList(ApplyRelationBo bo) {
        PagePlus<ApplyRelation, ApplyRelationVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<ApplyRelationVo> queryList(ApplyRelationBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ApplyRelation> buildQueryWrapper(ApplyRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ApplyRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getApplyId()), ApplyRelation::getApplyId, bo.getApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), ApplyRelation::getApplyType, bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getRelationApplyId()), ApplyRelation::getRelationApplyId, bo.getRelationApplyId());
        lqw.eq(StringUtils.isNotBlank(bo.getRelationApplyType()), ApplyRelation::getRelationApplyType, bo.getRelationApplyType());
        lqw.eq(bo.getIsDeleted() != null, ApplyRelation::getIsDeleted, bo.getIsDeleted());
        return lqw;
    }

    @Override
    public Boolean insertByBo(ApplyRelationBo bo) {
        ApplyRelation add = BeanUtil.toBean(bo, ApplyRelation.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(ApplyRelationBo bo) {
        ApplyRelation update = BeanUtil.toBean(bo, ApplyRelation.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ApplyRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
