package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.domain.vo.VersionVo;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.enums.ProcessNoRuleEnum;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.setting.domain.CodeRule;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocClassService;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/1/12 上午10:28
 * @Version 1.0
 * @Desc 生成ID
 */
@Slf4j
@Service
public class GenerateIdServiceImpl implements IGenerateIdService {
    /**
     * 申请流程ID规则配置参数
     */
    private static final String KEY_RULE_ID_FLOW = "key.id.generator.apply";

    @Autowired
    private ICodeRuleLogService iCodeRuleLogService;
    @Autowired
    private ICodeRuleService iCodeRuleService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private IVersionService versionService;
    @Autowired
    private IDocClassService docClassService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private IModifyApplyService iModifyApplyService;
    @Override
    public String generateApplyId(ApplyTypeEnum applyTypeEnum, String userName) {
        String prefix = "";
        switch (applyTypeEnum) {
            case ADD:
                prefix = ProcessNoRuleEnum.XZ.name();
                break;
            case UPDATE:
                prefix = ProcessNoRuleEnum.XD.name();
                break;
            case DISUSE:
                prefix = ProcessNoRuleEnum.SC.name();
                break;
            case CHANGE:
                prefix = ProcessNoRuleEnum.BG.name();
                break;
            case REVIEW:
                prefix = ProcessNoRuleEnum.FS.name();
                break;
            case REISSUE:
                prefix = ProcessNoRuleEnum.BF.name();
                break;
            case EXTRA:
                prefix = ProcessNoRuleEnum.ZF.name();
                break;
            case BORROW:
                prefix = ProcessNoRuleEnum.JY.name();
                break;
        }
        String configValue = configService.selectConfigByKey(KEY_RULE_ID_FLOW);
        Map<String,Object>  bizMap = Maps.newHashMap();
        bizMap.put("userName",userName);
        bizMap.put("flowCode",prefix);
        return iCodeRuleService.generatorEncodingRule(configValue,"",bizMap).getRuleValue();
    }

    String getDocRuleId(String classId) throws Exception{
        DocClass docClass = docClassService.getById(classId);
        if(docClass==null || StrUtil.isBlank(docClass.getCodeId())){
            throw new Exception(classId+"：找不到对应的文件编号");
        }
        return docClass.getCodeId();
    }

    String getDistributeRuleId(String classId) throws Exception{
        DocClass docClass = docClassService.getById(classId);
        if(docClass==null || StrUtil.isBlank(docClass.getDistributeId())){
            throw new Exception(classId+"：找不到对应的文件分发编号");
        }
        return docClass.getDistributeId();
    }

    @Override
    public DocNoVo generateRecordDocId(ModifyApply applyPo, ModifyApplyLink applyLink) throws Exception{
        DocNoVo docNoVo = new DocNoVo();
        SysDept dept = sysDeptService.selectDeptById(applyPo.getDeptId());
        Map<String,Object> bizMap =  BeanUtil.beanToMap(dept);
        bizMap.putAll(BeanUtil.beanToMap(applyLink));
        String ruleId = getDocRuleId(applyLink.getDocClass());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(ruleId,applyLink.getId(),bizMap);
        CodeRule codeRule = iCodeRuleService.getById(ruleId);
        docNoVo.setDocId(codeRuleLog.getRuleValue());
        docNoVo.setTwo(String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue()));
        docNoVo.setOne(StrUtil.removeSuffix(docNoVo.getDocId(),docNoVo.getTwo()));
        return docNoVo;
    }

    @Override
    public String generateDistributeId(DistributeItem bo, String docClass) throws Exception{
        VersionVo versionVo = versionService.queryById(bo.getVersionId());
        Map<String,Object> bizMap =  BeanUtil.beanToMap(versionVo);
        bizMap.putAll(BeanUtil.beanToMap(bo));
        //ModifyApply modifyApply = iModifyApplyService.getById(bo.getApplyId());
        String configValue = getDistributeRuleId(docClass);
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(configValue,bo.getDocId(),bizMap);
        return codeRuleLog.getRuleValue();
    }

    @Override
    public DocNoVo generateDocId(ModifyApply modifyApply) throws Exception{
        DocNoVo docNoVo = new DocNoVo();
        SysDept dept = sysDeptService.selectDeptById(modifyApply.getDeptId());
        Map<String,Object> bizMap =  BeanUtil.beanToMap(dept);
        bizMap.putAll(BeanUtil.beanToMap(modifyApply));
        String ruleId = getDocRuleId(modifyApply.getDocClass());
        CodeRuleLog codeRuleLog = iCodeRuleService.generatorEncodingRule(ruleId,modifyApply.getId(),bizMap);
        CodeRule codeRule = iCodeRuleService.getById(ruleId);
        docNoVo.setDocId(codeRuleLog.getRuleValue());
        docNoVo.setTwo(String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue()));
        docNoVo.setOne(StrUtil.removeSuffix(docNoVo.getDocId(),docNoVo.getTwo()));
        return docNoVo;

    }
    @Override
    public boolean saveOldDocId(String buinessId,String docClass,String docId){
        try{
            String ruleId = getDocRuleId(docClass);
            QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<CodeRuleLog>(CodeRuleLog.builder().ruleId(ruleId).build());
            queryWrapper.lambda().orderByDesc(CodeRuleLog::getNumberValue);
            queryWrapper.last("limit 1");
            CodeRuleLog codeRuleLog = iCodeRuleLogService.getOne(queryWrapper);
            if(codeRuleLog==null){
                codeRuleLog = CodeRuleLog.builder().ruleId(ruleId).numberValue(1L).ruleValue(docId).build();
            }
            else{
                codeRuleLog.setId(null);
                codeRuleLog.setNumberValue(codeRuleLog.getNumberValue()+1);
                codeRuleLog.setBusinessId(buinessId);
            }
            iCodeRuleLogService.save(codeRuleLog);
            return true;
        }
        catch(Exception ex){
            log.error("saveOldDocId==="+buinessId,ex);
            return false;
        }

    }
    @Override
    public boolean checkDocIdExist(String docId){
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(docId), CodeRuleLog::getRuleValue, docId);
        return iCodeRuleLogService.count(queryWrapper) > 0 ? true : false;
    }

    @Override
    public Boolean updateDocSerialNumber(CreateNewNoBo bo) throws Exception{
        return updateSerialNumber(bo);

    }

    @Override
    public Boolean updateRecordSerialNumber(CreateNewNoBo bo) throws Exception{
        return updateSerialNumber(bo);
    }

    private Boolean updateSerialNumber(CreateNewNoBo bo)throws Exception{
        if(StrUtil.equalsIgnoreCase(bo.getNewNo(),bo.getOldNo())){
            return true;
        }
        CodeRuleLog codeRuleLog = iCodeRuleLogService.getOne(new QueryWrapper<>(CodeRuleLog.builder().ruleValue(bo.getOldNo()).businessId(bo.getBusId()).build()));
        if(codeRuleLog==null){
            throw new Exception("没有找到旧的编号");
        }
        CodeRule codeRule = iCodeRuleService.getById(codeRuleLog.getRuleId());
        try{
            long numberValue = Long.valueOf(bo.getNum());

            codeRuleLog.setNumberValue(numberValue);
            codeRuleLog.setRuleValue(bo.getNewNo());
        }
        catch (Exception ex){
            throw new Exception("流水号不是数值");
        }
        long maxValue = 9;
        if(codeRule.getNumberDigit()>1){
            maxValue = Long.valueOf(String.format("1%1$0"+(codeRule.getNumberDigit()-1)+"d",0))*10-1;
        }
        if(codeRuleLog.getNumberValue()>maxValue){
            throw new Exception("流水号超过规则约定的位数");
        }
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<CodeRuleLog>(CodeRuleLog.builder().ruleId(codeRuleLog.getRuleId()).ruleValue(bo.getNewNo()).build());
        if(iCodeRuleLogService.count(queryWrapper)>0){
            throw new Exception("编号重复");
        }

        iCodeRuleLogService.updateById(codeRuleLog);
        return true;
    }
}
