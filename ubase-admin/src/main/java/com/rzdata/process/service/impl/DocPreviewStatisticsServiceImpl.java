package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.process.domain.DocPreviewStatistics;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.Version;
import com.rzdata.process.domain.bo.DocPreviewStatisticsBo;
import com.rzdata.process.domain.vo.DocPreviewStatisticsVo;
import com.rzdata.process.enums.PreviewSourceEnum;
import com.rzdata.process.mapper.IDocPreviewStatisticsMapper;
import com.rzdata.process.service.IDocPreviewStatisticsService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.process.service.IVersionService;
import com.rzdata.system.service.ISysDeptService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-01-04
 */
@Service
public class DocPreviewStatisticsServiceImpl extends ServicePlusImpl<IDocPreviewStatisticsMapper, DocPreviewStatistics, DocPreviewStatisticsVo> implements IDocPreviewStatisticsService {

    @Resource
    private IStandardService standardService;

    @Resource
    private ISysDeptService sysDeptService;

    @Resource
    private IVersionService versionService;

    @Override
    public boolean insertByBo(DocPreviewStatisticsBo docPreviewStatisticsBo) {
        DocPreviewStatistics add = BeanUtil.toBean(docPreviewStatisticsBo, DocPreviewStatistics.class);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            docPreviewStatisticsBo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public AjaxResult insertPreviewStatist(String id, PreviewSourceEnum source) {

        //通过文件id/合稿文件id/签章文件id 找到对应的version表数据 一条
        LambdaQueryWrapper<Version> query = new LambdaQueryWrapper();
        query.eq(Version::getFileId, id).or().eq(Version::getMergeFileId, id).or().eq(Version::getEncryptFileId, id);
        Version one = versionService.getOne(query);

        //通过version存的standardId 找到对应的数据
        Standard standard = standardService.getById(one.getStandardId());
        if (standard != null) {
            DocPreviewStatisticsBo docPreviewStatisticsBo = new DocPreviewStatisticsBo();
            docPreviewStatisticsBo.setId(null);
            docPreviewStatisticsBo.setDocId(one.getDocId());
            docPreviewStatisticsBo.setDocName(standard.getDocName());
            if (ObjectUtil.isNotEmpty(one)) {
                docPreviewStatisticsBo.setVersionId(one.getId());
                docPreviewStatisticsBo.setVersionValue(one.getVersionValue());
            }
            docPreviewStatisticsBo.setUserName(standard.getUserName());
            docPreviewStatisticsBo.setPreviewUserName(SecurityUtils.getUsername());
            docPreviewStatisticsBo.setPreviewNickName(SecurityUtils.getNickname());
            docPreviewStatisticsBo.setNickName("");
            docPreviewStatisticsBo.setDeptId(standard.getDeptId());
            docPreviewStatisticsBo.setDeptName(sysDeptService.getById(standard.getDeptId()).getDeptName());
            docPreviewStatisticsBo.setFileId(id);
            docPreviewStatisticsBo.setSource(source.getValue());
            docPreviewStatisticsBo.setPreviewTime(new Date());
            insertByBo(docPreviewStatisticsBo);
            return AjaxResult.success("请求成功", null);
        } else {
            return AjaxResult.error("插入预览数据失败,未找到文档信息", null);
        }
    }

    @Override
    public AjaxResult<List<DocPreviewStatisticsVo>> previewStatist() {
        try {
            LambdaQueryWrapper<DocPreviewStatistics> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(DocPreviewStatistics::getPreviewUserName, SecurityUtils.getUsername());
            queryWrapper.orderByDesc(DocPreviewStatistics::getCreateTime);
            List<DocPreviewStatisticsVo> listVo = listVo(queryWrapper);
            return AjaxResult.success(listVo);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(null, null);
        }

    }
}
