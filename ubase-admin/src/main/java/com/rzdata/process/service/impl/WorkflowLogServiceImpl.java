package com.rzdata.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.WorkflowLog;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.WorkflowLogBo;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.mapper.WorkflowLogMapper;
import com.rzdata.process.service.IWorkflowLogService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流程审批记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
@Service
public class WorkflowLogServiceImpl extends ServicePlusImpl<WorkflowLogMapper, WorkflowLog, WorkflowLogVo> implements IWorkflowLogService {

    @Override
    public WorkflowLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<WorkflowLogVo> queryPageList(WorkflowLogBo bo) {
        PagePlus<WorkflowLog, WorkflowLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<WorkflowLogVo> queryList(WorkflowLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<WorkflowLog> buildQueryWrapper(WorkflowLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkflowLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProcInstId()), WorkflowLog::getProcInstId, bo.getProcInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), WorkflowLog::getBusinessId, bo.getBusinessId());
        lqw.like(StringUtils.isNotBlank(bo.getActDefName()), WorkflowLog::getActDefName, bo.getActDefName());
        lqw.eq(StringUtils.isNotBlank(bo.getActInstId()), WorkflowLog::getActInstId, bo.getActInstId());
        lqw.eq(StringUtils.isNotBlank(bo.getYNApprove()), WorkflowLog::getYNApprove, bo.getYNApprove());
        lqw.eq(StringUtils.isNotBlank(bo.getOpinion()), WorkflowLog::getOpinion, bo.getOpinion());
        lqw.eq(StringUtils.isNotBlank(bo.getSender()), WorkflowLog::getSender, bo.getSender());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiver()), WorkflowLog::getReceiver, bo.getReceiver());
        lqw.eq(StringUtils.isNotBlank(bo.getActStatus()), WorkflowLog::getActStatus, bo.getActStatus());
        return lqw;
    }

    @Override
    public Boolean insertByBo(WorkflowLogBo bo) {
        WorkflowLog add = BeanUtil.toBean(bo, WorkflowLog.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(WorkflowLogBo bo) {
        WorkflowLog update = BeanUtil.toBean(bo, WorkflowLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(WorkflowLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public List<WorkflowLogVo> selectLogByBusinessId(String businessId) {
        return this.baseMapper.selectLogByBusinessId(businessId);
    }

    @Override
    public String queryRecentLog(String id) {
        return this.baseMapper.queryRecentLog(id);
    }

    @Override
    public Page<WorkflowLogVo> queryToDoList(Page<WorkflowLogVo> page, ProcessWorkFlowBo processBo) {
        return this.baseMapper.queryToDoList(page,processBo);
    }

    @Override
    public List<WorkflowLogVo> queryApprovalRecord(String applyId, String defActName) {
        return this.baseMapper.queryApprovalRecord(applyId,defActName);
    }


}
