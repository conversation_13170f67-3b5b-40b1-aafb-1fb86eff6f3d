package com.rzdata.process.service;


import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.process.domain.DocPreviewStatistics;
import com.rzdata.process.domain.bo.DocPreviewStatisticsBo;
import com.rzdata.process.domain.vo.DocPreviewStatisticsVo;
import com.rzdata.process.enums.PreviewSourceEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/8 14:27
 * @Version 1.0
 * @Description 文件预览统计
 */
public interface IDocPreviewStatisticsService extends IServicePlus<DocPreviewStatistics, DocPreviewStatisticsVo> {


    boolean insertByBo(DocPreviewStatisticsBo docPreviewStatisticsBo);

    /**
     * 个人预览统计
     * @param id 文件basic_file表(可能是编制文件/合稿文件/签章文件
     * @param source 来源
     * @return
     */
    AjaxResult insertPreviewStatist(String id, PreviewSourceEnum source);

    /**
     * 查看预览统计
     * @return
     */
    AjaxResult<List<DocPreviewStatisticsVo>> previewStatist();
}
