package com.rzdata.process.service;

import com.rzdata.process.domain.BorrowApplyUser;
import com.rzdata.process.domain.vo.BorrowApplyUserVo;
import com.rzdata.process.domain.bo.BorrowApplyUserBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件借阅选择借阅人Service接口
 *
 * <AUTHOR>
 * @date 2021-12-31
 */
public interface IBorrowApplyUserService extends IServicePlus<BorrowApplyUser, BorrowApplyUserVo> {
	/**
	 * 查询单个
	 * @return
	 */
	BorrowApplyUserVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<BorrowApplyUserVo> queryPageList(BorrowApplyUserBo bo);

	/**
	 * 查询列表
	 */
	List<BorrowApplyUserVo> queryList(BorrowApplyUserBo bo);

	/**
	 * 根据新增业务对象插入文件借阅选择借阅人
	 * @param bo 文件借阅选择借阅人新增业务对象
	 * @return
	 */
	Boolean insertByBo(BorrowApplyUserBo bo);

	/**
	 * 根据编辑业务对象修改文件借阅选择借阅人
	 * @param bo 文件借阅选择借阅人编辑业务对象
	 * @return
	 */
	Boolean updateByBo(BorrowApplyUserBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
