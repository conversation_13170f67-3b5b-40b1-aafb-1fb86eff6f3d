package com.rzdata.process.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.WorkflowLog;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.WorkflowLogBo;
import com.rzdata.process.domain.vo.WorkflowLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 流程审批记录Service接口
 *
 * <AUTHOR>
 * @date 2021-12-30
 */
public interface IWorkflowLogService extends IServicePlus<WorkflowLog, WorkflowLogVo> {
	/**
	 * 查询单个
	 * @return
	 */
	WorkflowLogVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<WorkflowLogVo> queryPageList(WorkflowLogBo bo);

	/**
	 * 查询列表
	 */
	List<WorkflowLogVo> queryList(WorkflowLogBo bo);

	/**
	 * 根据新增业务对象插入流程审批记录
	 * @param bo 流程审批记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(WorkflowLogBo bo);

	/**
	 * 根据编辑业务对象修改流程审批记录
	 * @param bo 流程审批记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(WorkflowLogBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 根据业务Id获取流程记录
	 * @param businessId
	 * @return
	 */
	List<WorkflowLogVo> selectLogByBusinessId(String businessId);

	String queryRecentLog(String id);

	Page<WorkflowLogVo> queryToDoList(Page<WorkflowLogVo> page, ProcessWorkFlowBo processBo);

	/**
	 * @param applyId 流程(业务)Id
	 * @param actDefName 环节名称(编制 审批 批准)
	 * @return
	 */
	List<WorkflowLogVo> queryApprovalRecord(String applyId,String actDefName);


}
