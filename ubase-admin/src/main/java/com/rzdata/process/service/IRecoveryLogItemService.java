package com.rzdata.process.service;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.RecoveryLogItem;
import com.rzdata.process.domain.bo.RecoveryLogItemBo;
import com.rzdata.process.domain.vo.RecoveryLogItemVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件回收记录Service接口
 *
 * <AUTHOR>
 * @date 2022-01-17
 */
public interface IRecoveryLogItemService extends IServicePlus<RecoveryLogItem, RecoveryLogItemVo> {

	/**
	 * 查询单个
	 * @return
	 */
	RecoveryLogItemVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<RecoveryLogItemVo> queryPageList(RecoveryLogItemBo bo);

	/**
	 * 查询列表
	 */
	List<RecoveryLogItemVo> queryList(RecoveryLogItemBo bo);

	/**
	 * 根据新增业务对象插入文件回收记录
	 * @param bo 文件回收记录新增业务对象
	 * @return
	 */
	Boolean insertByBo(RecoveryLogItemBo bo);


	/**
	 * 根据新增业务对象插入文件回收记录
	 * @param bo 文件回收记录新增业务对象
	 * @return
	 */
	Boolean insertByBoFromDistribute(RecoveryLogItemBo bo);

	/**
	 * 根据编辑业务对象修改文件回收记录
	 * @param bo 文件回收记录编辑业务对象
	 * @return
	 */
	Boolean updateByBo(RecoveryLogItemBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);


	/**
	 * 文件回收接口
	 * @param bo
	 * @return
	 */
	AjaxResult recovery(RecoveryLogItemBo bo);
}
