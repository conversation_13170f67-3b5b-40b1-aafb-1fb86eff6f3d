package com.rzdata.process.exception;


import com.rzdata.process.enums.PreviewErrorEnum;

/**
 * 文档异常封装类
 * <AUTHOR>
 */
public final class DocumentException extends RuntimeException {
    private PreviewErrorEnum errorCode;

    public DocumentException(PreviewErrorEnum errorCode) {
        super(errorCode.getMsg());
        this.errorCode = errorCode;
    }

    public DocumentException(PreviewErrorEnum errorCode, Throwable t) {
        super(errorCode.getMsg(), t);
        this.errorCode = errorCode;
    }

    public PreviewErrorEnum getErrorCode() {
        return errorCode;
    }
}
