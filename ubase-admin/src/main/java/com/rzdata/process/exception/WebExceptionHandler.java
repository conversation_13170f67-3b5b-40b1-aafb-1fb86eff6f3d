package com.rzdata.process.exception;

import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.exception.base.BaseException;
import com.rzdata.process.enums.PreviewErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@ResponseBody
@Slf4j
public class WebExceptionHandler {
    @ExceptionHandler(DocumentException.class)
    public AjaxResult documentException(DocumentException e) {
        e.printStackTrace();
        return AjaxResult.error(e.getErrorCode().getMsg());
   }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult requestException(HttpRequestMethodNotSupportedException e) {
        e.printStackTrace();
        return AjaxResult.error(PreviewErrorEnum.UNSUPPORTED_REQUEST_METHOD.getMsg());
    }

    @ExceptionHandler(ServiceException.class)
    public AjaxResult serviceException(ServiceException e) {
        e.printStackTrace();
        return AjaxResult.error(e.getMessage());
    }

    @ExceptionHandler(BaseException.class)
    public AjaxResult baseException(BaseException e) {
        e.printStackTrace();
        return AjaxResult.error(e.getMessage());
    }
}