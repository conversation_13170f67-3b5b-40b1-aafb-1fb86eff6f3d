package com.rzdata.process.enums;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/1/13 下午2:55
 * @Version 1.0
 */
public enum YNEnum {
    /**
     * 未知
     */
    UNKNOWN,
    /**
     * 是
     */
    YES,
    /**
     * 否
     */
    NO;

    public static YNEnum toType(String type) {
        return Stream.of(YNEnum.values()).filter(v -> v.name().equalsIgnoreCase(type)).findFirst().orElse(YNEnum.UNKNOWN);
    }
}
