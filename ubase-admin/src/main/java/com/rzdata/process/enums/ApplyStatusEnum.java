package com.rzdata.process.enums;

/**
 * <AUTHOR>
 * @Date 2021/12/31 下午1:45
 * @Version 1.0
 * @Desc 工作流状态枚举
 */
public enum ApplyStatusEnum {

    /**
     * 草稿
     */
    DRAFT("draft","草稿"),
    /**
     * 审批中
     */
    PROCESSING("processing","审批中"),
    /**
     * 通过
     */
    PASS("pass","通过"),
    /**
     * 不通过
     */
    UN_PASS("un_pass","不通过"),
    /**
     * 保持现状
     */
    KEEP("keep","保持现状"),
    /**
     * 延长有效期
     */
    EXTENSION("extension","延长有效期"),
    /**
     * 文件修订
     */
    UPDATE("update","文件修订"),
    /**
     * 文件作废
     */
    DISUSE("disuse","文件作废");

    ApplyStatusEnum(String code,String status) {
        this.code = code;
        this.msg = status;
    }
    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static String getMsg(String code){
        for (ApplyStatusEnum applyStatusEnum : ApplyStatusEnum.values()){
            if(applyStatusEnum.getCode().equals(code)){
                return applyStatusEnum.getMsg();
            }
        }
        return "";
    }
}
