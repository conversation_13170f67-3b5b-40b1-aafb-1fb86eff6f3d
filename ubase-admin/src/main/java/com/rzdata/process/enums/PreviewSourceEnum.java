package com.rzdata.process.enums;


import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/3/3 14:54
 * @Version 1.0
 * @Description 预览来源:本部门 外部门 公司文件 借阅
 */
@Getter
public enum PreviewSourceEnum {
    /**
     * 未知
     */
    UNKNOWN(0),
    /**
     * 本部门
     */
    THIS_DEPT(1),
    /**
     * 外部门
     */
    OTHER_DEPT(2),
    /**
     * 公司文件
     */
    COMPANY(3),
    /**
     * 借阅
     */
    BORROW(4);

    private Integer value;

    PreviewSourceEnum(Integer value){
        this.value = value;
    }

    public static PreviewSourceEnum toSource(int source) {
        return Stream.of(PreviewSourceEnum.values()).filter(v -> v.getValue().equals(source)).findFirst().orElse(PreviewSourceEnum.UNKNOWN);
    }

}
