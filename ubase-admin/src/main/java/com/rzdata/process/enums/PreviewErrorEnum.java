package com.rzdata.process.enums;


/**
 * 错误码枚举
 * 约定 code 为 0 表示操作成功,
 * 1 或 2 等正数表示软件错误,
 * -1, -2 等负数表示系统错误.
 * @author: wangyang
 * @date: 2022年1月21日15:43:43
 */
public enum PreviewErrorEnum {
    SUCCESS("success"),
    DOC_FILE_NOT_EXISTS("文档不存在或未上传"),
    DOC_FILE_EMPTY( "目标文档是目录或空文件"),
    DOC_FILE_UNREADABLE("目标文档不可读"),
    DOC_FILE_OVERSIZE("目标文档大小超过限制"),
    DOC_FILE_TYPE_UNSUPPORTED( "目标文档格式不正确"),
    DOC_FILE_MD5_ERROR("目标文档md5校验失败"),
    DOC_FILE_MIME_ERROR("目标文档MIME检查失败"),
    DOC_FILE_NO_EXTENSION("文件路径不包含扩展名"),
    DOC_FILE_EXTENSION_NOT_MATCH("文件路径和名称后缀不匹配"),
    DOC_FILE_KEY_ERROR("目标文档key计算失败"),
    DOC_CACHE_ERROR("文档信息缓存失败"),
    DOC_CACHE_NOT_EXISTS("从缓存中获取文档信息失败"),
    UNSUPPORTED_REQUEST_METHOD("不支持的请求类型"),

    SYSTEM_UNKNOWN_ERROR("系统繁忙，请稍后再试...");

    private String msg;

    PreviewErrorEnum(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

}
