package com.rzdata.process.enums;

/**
 * <AUTHOR>
 * @Date 2022/2/12 17:12
 * @Version 1.0
 * @Description 消息类型美剧
 */
public enum MsgTypeEnum {
    /**
     * 变更申请
     */
    CHANGE(1),

    /**
     * 补发
     */
    REISSUE(2),

    /**
     * 增发
     */
    EXTRA(3),

    /**
     * 借阅
     */
    BORROW(4),

    /**
     * 复审
     */
    REVIEW(5),

    /**
     * 签收
     */
    SIGN(6),

    /**
     * 回收
     */
    RECEIVE(7),

    /**
     * 生效
     */
    VALIDITY(8),
    /**
     * 失效
     */
    INVALIDATION(9),

    /**
     * 复审提前
     */
    REVIEW_ADVANCE(10),

    /**
     * 有效期提前
     */
    VALIDITY_ADVANCE(11),

    /**
     * 终止归档
     */
    END_ARCHIVING(12);

    MsgTypeEnum(Integer type) {
        this.type = type;
    }
    private Integer type;

    public Integer getType() {
        return type;
    }
}
