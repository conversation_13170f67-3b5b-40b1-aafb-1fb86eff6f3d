package com.rzdata.process.enums;

import lombok.Getter;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/3/9 15:23
 * @Version 1.0
 * @Description
 */
@Getter
public enum ReviewResultEnum {
    /**
     * 保持现状
     */
    KEEP("保持现状"),
    /**
     * 延长有效期
     */
    EXTENSION("延长有效期"),
    /**
     * 文件修订
     */
    UPDATE("文件修订"),
    /**
     * 文件作废
     */
    DISUSE("文件作废"),

    /**
     * 未知
     */
    UNKNOWN("未知");
    ReviewResultEnum(String result){
        this.result = result;
    }
    private String result;

    public static ReviewResultEnum toEnum(String type) {
        return Stream.of(ReviewResultEnum.values()).filter(v -> v.name().equalsIgnoreCase(type)).findFirst().orElse(ReviewResultEnum.UNKNOWN);
    }
}
