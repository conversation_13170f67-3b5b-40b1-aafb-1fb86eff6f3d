package com.rzdata.process.listener.ibo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LinkLogIBo {
    @ExcelProperty(value = "主文件类型",index = 1)
    private  String  dsDocClass;
    @ExcelProperty(value = "主文件编号",index = 2)
    private  String  docId;
    @ExcelProperty(value = "主文件版本",index = 3)
    private  String  currentVersion;
    @ExcelProperty(value = "主文件名称",index = 4)
    private  String  docName;
    @ExcelProperty(value = "id",index = 5)
    private  String  id;
    @ExcelProperty(value = "关联编号",index = 6)
    private  String  linkCode;
    @ExcelProperty(value = "关联版本",index = 7)
    private  String  versionValue;
    @ExcelProperty(value = "关联文件名",index = 8)
    private  String  fileName;
    @ExcelProperty(value = "关联文件类型",index = 9)
    private  String  docClass;
    @ExcelProperty(value = "创建时间",index = 10)
    private  String  createTime;
}
