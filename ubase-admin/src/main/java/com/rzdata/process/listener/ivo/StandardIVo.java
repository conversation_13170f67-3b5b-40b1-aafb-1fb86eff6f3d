package com.rzdata.process.listener.ivo;

import com.rzdata.process.enums.LinkTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@ApiModel("历史文件初始化导入成功的返回消息")
@Data
public class StandardIVo {
    private int initFileNum = 0;
    private List<String> initFileMsg = new ArrayList<>();
    private int linkFileNum = 0;
    private List<String> linkFileMsg = new ArrayList<>();
    private int linkRecordNum = 0;
    private List<String> initRecordMsg = new ArrayList<>();

    public void initFileSum() {
        this.initFileNum ++;
    }
    public void linkFileSum() {
        this.linkFileNum ++;
    }
    public void linkRecordSum() {
        this.linkRecordNum ++;
    }

    public void initFileMsgAdd(int num, String msg){
        initFileMsg.add("第"+num+"行数据，"+msg);
    }

    public void linkFileMsgMsgAdd(String linkType,int num, String msg){
        if (linkType.equals(LinkTypeEnum.REF_DOC.name())) {
            linkFileMsg.add("第"+num+"行数据，"+msg);
        }else {
            initRecordMsg.add("第"+num+"行数据，"+msg);
        }

    }
}
