package com.rzdata.process.listener.ibo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ApiModel("导入的批量初始化历史生效文件")
@Data
public class StandardIBo {
    @ExcelProperty(value = "文件编号",index = 1)
    private  String  id;
    @ExcelProperty(value = "文件类型",index = 2)
    private  String  docClass;
    @ExcelProperty(value = "文件编号",index = 3)
    private  String  docId;
    @ExcelProperty(value = "文件版本号",index = 4)
    private  String  currentVersion;
    @ExcelProperty(value = "文件名称",index = 5)
    private  String  docName;
    @ExcelProperty(value = "文件扩展名",index = 6)
    private  String  fileType;
    @ExcelProperty(value = "文件状态",index = 7)
    private  String  status;
    @ExcelProperty(value = "生效日期",index = 8)
    private  String  startDate;
    @ExcelProperty(value = "文件有效期",index = 9)
    private  String  endDate;
    @ExcelProperty(value = "文件复审期",index = 10)
    private  String  reviewTime;
    @ExcelProperty(value = "变更类型\n" + "（新增,修订）",index = 11)
    private  String  changeType;
    @ExcelProperty(value = "变更原因",index = 12)
    private  String  changeReason;
    @ExcelProperty(value = "变更内容",index = 13)
    private  String  content;
    @ExcelProperty(value = "变更要素（人,机,料,法,环,测）",index = 14)
    private  String  changeFactor;
    @ExcelProperty(value = "编制部门",index = 15)
    private  String  deptId;
    @ExcelProperty(value = "编制人员",index = 16)
    private  String  userName;
    @ExcelProperty(value = "编制时间",index = 17)
    private  String  applyTime;
}
