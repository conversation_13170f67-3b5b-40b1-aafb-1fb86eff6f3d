package com.rzdata.job.service;

import com.rzdata.process.utils.DateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * XxlJob开发示例（Bean模式）
 * <p>
 * 开发步骤：
 * 1、任务开发：在Spring Bean实例中，开发Job方法；
 * 2、注解配置：为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
 * 3、执行日志：需要通过 "XxlJobHelper.log" 打印执行日志；
 * 4、任务结果：默认任务结果为 "成功" 状态，不需要主动设置；如有诉求，比如设置任务结果为失败，可以通过 "XxlJobHelper.handleFail/handleSuccess" 自主设置任务结果；
 *
 * <AUTHOR> 2019-12-11 21:52:51
 */
@Slf4j
@Service
public class HealthCheckService {

    /**
     * 用于服务健康检查
     */
    @XxlJob("healthCheck")
    public void dmsHealthCheck() {
        XxlJobHelper.log("任务-dmsHealthCheck 开始");
        XxlJobHelper.log("任务-dmsHealthCheck 结束"+ DateUtils.getTime());
    }


    public void init() {
        log.info("init");
    }

    public void destroy() {
        log.info("destory");
    }


}
