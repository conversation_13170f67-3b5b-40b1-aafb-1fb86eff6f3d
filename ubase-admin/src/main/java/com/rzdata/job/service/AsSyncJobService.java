package com.rzdata.job.service;

import com.alibaba.fastjson.JSONObject;
import com.rzdata.asas7.service.AsSyncOrgUserService;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.system.service.IUnifyUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 与爱数自动同步服务
 * <AUTHOR> 20231212
 */
@Slf4j
@Service
public class AsSyncJobService {

    @Resource
    AsSyncOrgUserService asSyncOrgUserService;

    @Resource
    IUnifyUserService unifyUserService;


    /**
     * 同步组织和用户入口
     *
     * @throws Exception
     */
    @XxlJob("initOrgAndUser")
    public void initOrgAndUser() throws Exception {
        XxlJobHelper.log("任务-同步组织和用户 开始");
        String inputParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(inputParam)) {
            JSONObject json = JSONObject.parseObject(inputParam);
            String topOrgName = json.getString("topOrgName");
            String parentId = json.getString("parentId");
            boolean isCheckStatus = json.getBoolean("isCheckStatus");
            boolean isRebuild = json.getBoolean("isRebuild");
            boolean isRecursion = json.getBoolean("isRecursion");
            boolean deptAsOrgFlag = json.getBoolean("deptAsOrgFlag");
            String deptAsOrg = json.getString("deptAsOrg");
            String dataBase = json.getString("dataBase");
            XxlJobHelper.log("inputParam="+inputParam);
            AjaxResult syncRes = this.asSyncOrgUserService.initOrgAndUser(topOrgName,parentId,isCheckStatus,isRebuild,isRecursion,deptAsOrgFlag,deptAsOrg,dataBase);
            XxlJobHelper.log("同步结果="+JSONObject.toJSONString(syncRes));
        } else {
            XxlJobHelper.log("无法执行，请先配置任务调度参数。");
        }
        XxlJobHelper.log("任务-同步组织和用户 结束");
    }


    /**
     * 同步组织和用户入口
     *
     * @throws Exception
     */
    @XxlJob("initUnifyUser")
    public void initUnifyUser() throws Exception {
        XxlJobHelper.log("任务-同步申石用户 开始");
        String inputParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(inputParam)) {
            boolean syncRes = this.unifyUserService.sycn();
            XxlJobHelper.log("同步结果="+JSONObject.toJSONString(syncRes));
        } else {
            XxlJobHelper.log("无法执行，请先配置任务调度参数。");
        }
        XxlJobHelper.log("任务-同步申石用户 结束");
    }

    /**
     * 同步组织和用户入口
     *
     * @throws Exception
     */
    @XxlJob("initUnifyOrg")
    public void initUnifyOrg() throws Exception {
        XxlJobHelper.log("任务-同步申石组织 开始");
        String inputParam = XxlJobHelper.getJobParam();
        if(StringUtils.isNotEmpty(inputParam)) {
            boolean syncRes = this.unifyUserService.sycn();
            XxlJobHelper.log("同步结果="+JSONObject.toJSONString(syncRes));
        } else {
            XxlJobHelper.log("无法执行，请先配置任务调度参数。");
        }
        XxlJobHelper.log("任务-同步申石组织 结束");
    }

    /**
     * 比对爱数用户数据修改状态
     *
     * @throws Exception
     */
    @XxlJob("updateUserStatus")
    public void updateUserStatus() throws Exception {
        XxlJobHelper.log("任务-比对爱数用户数据修改状态 开始");
        String userName = XxlJobHelper.getJobParam();
        String res = this.asSyncOrgUserService.updateUserStatus(userName,true);
        XxlJobHelper.log("同步结果="+res);
        XxlJobHelper.log("任务-比对爱数用户数据修改状态 结束");
    }

    /**
     * 比对爱数部门数据修改状态
     *
     * @throws Exception
     */
    @XxlJob("updateDeptStatus")
    public void updateDeptStatus() throws Exception {
        XxlJobHelper.log("任务-比对爱数部门数据修改状态 开始");
        String deptName = XxlJobHelper.getJobParam();
        String res = this.asSyncOrgUserService.updateDeptStatus(deptName,true);
        XxlJobHelper.log("同步结果="+res);
        XxlJobHelper.log("任务-比对爱数部门数据修改状态 结束");
    }


    public void init() {
        log.info("init");
    }

    public void destroy() {
        log.info("destory");
    }

}
