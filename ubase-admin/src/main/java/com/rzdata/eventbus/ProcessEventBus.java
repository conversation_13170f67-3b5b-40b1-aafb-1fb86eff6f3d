package com.rzdata.eventbus;

/**
 * <AUTHOR>
 * @Date 2022/1/3 下午9:31
 * @Version 1.0
 * @Desc 事件总线
 */
public class ProcessEventBus {
    private static final com.google.common.eventbus.EventBus eventBus = new com.google.common.eventbus.EventBus();

    public static void post(IEvent event) {
        eventBus.post(event);
    }

    public static void register(Object bean) {
        eventBus.register(bean);
    }

    public static void unregister(Object bean) {
        eventBus.unregister(bean);
    }
}
