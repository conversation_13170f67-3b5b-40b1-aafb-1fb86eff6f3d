package com.rzdata.eventbus;

import com.blueland.bpmclient.model.BpmClientInputModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/1/3 下午9:44
 * @Version 1.0
 * @Desc 流程处理事件
 */
@Data
public class ProcessResultEvent implements IEvent {
    private String applyId;
    private String status;
    private String msgInfo;
    /**
     * 申请状态 {@link com.rzdata.process.enums.ApplyStatusEnum}
     */
    private String applyStatus;
    private BpmClientInputModel model;

    private Date setupTime;

    public ProcessResultEvent(String applyId, String status, BpmClientInputModel model,String msgInfo,String applyStatus,Date setupTime) {
        this.applyId = applyId;
        this.status = status;
        this.model = model;
        this.msgInfo = msgInfo;
        this.applyStatus = applyStatus;
        this.setupTime = setupTime;
    }
}
