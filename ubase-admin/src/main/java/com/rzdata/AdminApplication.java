package com.rzdata;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * ubase启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication
@EnableTransactionManagement
public class AdminApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(AdminApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        //加载spire的授权文件
        // com.spire.license.LicenseProvider.setLicenseFile("/ubase-admin/license.elic.xml");
        System.out.println("ubase用户组织基础组件-启动成功");
    }

}
