package com.rzdata.sso.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.process.domain.Tenant;
import com.rzdata.system.domain.SysUserTenant;
import com.rzdata.system.service.ISysUserTenantService;
import com.rzdata.system.service.ITenantService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/*
 * SSO通用分发服务类
 *
 * @author: xiefc
 * @date:2023/9/13 17:13
 */
@Service
public class GeneralDistributeService {

    /**
     * 用户租户关系服务类
     */
    @Resource
    ISysUserTenantService sysUserTenantService;

    /**
     * 租户服务类
     */
    @Resource
    ITenantService tenantService;

    public String getSsoRedirectUrl(String userName) {
        String result = "";
        // 查询用户与租户关系表
        List<SysUserTenant> list =  sysUserTenantService.list(new LambdaQueryWrapper<SysUserTenant>().eq(SysUserTenant::getUserName,userName));
        if(list != null && list.size() > 0) {
            // 默认获取第一条
            String tenantId = list.get(0).getTenantId();
            // 获取对应的租户
            Tenant obj = this.tenantService.getById(tenantId);
            if(obj != null) {
                // 获取租户对应的URL
                result = obj.getUrl();
            }
        }
        return result;
    }
}
