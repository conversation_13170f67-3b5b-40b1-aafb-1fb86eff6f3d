package com.rzdata.sso.controller;

import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.rzdata.asas7.service.AsUserService;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginBody;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.sso.service.GeneralDistributeService;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.SysLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * AS单点登录验证控制器
 *
 * <AUTHOR>
 */
@Api(value = "AS单点登录验证控制器", tags = {"AS单点登录验证控制器"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/as/sso/")
@Slf4j
public class AsSsoController {

    private final SysLoginService loginService;

    private final AsUserService asUserService;

    private final GeneralDistributeService generalDisService;

    @Resource
    ISysUserService sysUserService;

    private final static String TOKEN_KEY = "token";

    private final static String JWT_SECRET = "eisoo.com";

    /**
     * 单点登录获取用户令牌（用于第三方集成DMS服务）
     *
     * @param loginBody 登录用户信息
     * @return 结果
     */
    @ApiOperation("单点登录获取用户令牌")
    @PostMapping("/login")
    public AjaxResult<Map<String, Object>> ssoLogin(@RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>(1);
        // 第三方将用户名进行JWT加密
        String userToken = loginBody.getUsername();
        try {
            // 校验用户名
            this.verifyJwt(userToken,JWT_SECRET);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage(),ajax);
        }
        DecodedJWT jwt = JWT.decode(loginBody.getUsername());
        /*
        System.out.println(jwt.getToken());
        System.out.println(jwt.getHeader());
        System.out.println(jwt.getPayload());
        System.out.println(jwt.getSignature());
        System.out.println(jwt.getExpiresAt());
         */
        // Payload
        Map<String, Claim> claims = jwt.getClaims();
        // 获取明文用户名
        String userName = claims.get("username").asString();
        //System.out.println(userName);
        // 生成DMS服务令牌
        String token = loginService.sso(userName);
        ajax.put(Constants.TOKEN, token);
        return AjaxResult.success(ajax);
    }

    /**
     * 验证JWT令牌的有效性
     *
     * @param token
     * @param secret
     * @throws UnsupportedEncodingException
     */
    private void verifyJwt(String token, String secret) throws UnsupportedEncodingException {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        //Reusable verifier instance
        JWTVerifier verifier = JWT.require(algorithm).build();
        verifier.verify(token);
    }



    /**
     * 单点登录验证爱数用户令牌
     *
     * @param request 请求信息
     * @return 结果
     */
    @ApiOperation("单点登录验证爱数用户令牌")
    @PostMapping("/check")
    @ResponseBody
    public AjaxResult ssoCheck(HttpServletRequest request, HttpServletResponse response) {
        // 获取爱数用户令牌
        String asUserToken = request.getParameter(TOKEN_KEY);
        if(StringUtils.isBlank(asUserToken)) {
            return AjaxResult.error("爱数用户令牌为空");
        }
        // 鉴定爱数用户令牌是否有效，有效则返回爱数用户信息
        AjaxResult<JSONObject> asUserRes = this.asUserService.getUserInfoByToken(asUserToken);
        try {
            if(asUserRes.getCode() == HttpStatus.HTTP_OK) {
                if(asUserRes.getData().containsKey("code")) {
                    // 爱数鉴权失败-用户令牌有问题
                    return AjaxResult.error("爱数用户令牌解析失败",asUserRes.getData().toString());
                } else {
                    String account = asUserRes.getData().getStr("account");
                    if(account.indexOf("@mehowmy.com")>0){
                        String userName = account.replace("@mehowmy.com","@mehowcy.com");
                        SysUser dbUser2 = this.sysUserService.selectUserByUserName(userName);
                        if(dbUser2!=null){
                            account = account.replace("@mehowmy.com", "@mehowcy.com");
                        }
                    }
                    // 爱数鉴权成功，则生成体系文件令牌
                    String rzUserToken = loginService.sso(account);
                    String ssoUrl = this.generalDisService.getSsoRedirectUrl(account);
                    Map<String,String> map = new HashMap<>();

                    JWTCreator.Builder builder = JWT.create();
                    builder.withClaim("username",account);
                    // 设置过期时间 5分钟之后过期
                    Date expiresAt = new Date(System.currentTimeMillis() + 5 * 60 * 1000);
                    builder.withExpiresAt(expiresAt);
                    String token= builder.sign(Algorithm.HMAC256(JWT_SECRET));
                    map.put("rzUserToken",token);
                    map.put("rzSsoUrl",ssoUrl);
                    if(StringUtils.isNotEmpty(rzUserToken) && StringUtils.isNotEmpty(ssoUrl)) {
                        // 体系文件用户令牌有效
                        response.setHeader("Authorization","Bearer "+rzUserToken);
                        return AjaxResult.success("体系文件用户令牌有效",map);
                    } else {
                        // 体系文件用户令牌无效
                        return AjaxResult.success("体系文件用户令牌生成失败",map);
                    }
                }
            } else {
                // 鉴定爱数用户令牌是否有效-异常
                return AjaxResult.success("鉴定爱数用户令牌是否有效异常",asUserRes.getMsg());
            }
        } catch (Exception e) {
            // 异常
            log.error("ssoCheck exceptio",e);
            return AjaxResult.error("ssoCheck exception",e.getMessage());
        }
    }

}
