package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 申石组织表对象 t_unify_org
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@Accessors(chain = true)
@TableName("t_unify_org")
public class TUnifyOrg extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 简称
     */
    private String sn;
    /**
     * 组织名称
     */
    private String name;
    /**
     * 上级组织
     */
    private String parentId;
    /**
     * 排序
     */
    private Long sortNum;
    /**
     * 组织类型
     */
    private String orgTypeId;
    /**
     * 简称全路径
     */
    private String snPath;
    /**
     * id全路径
     */
    private String idPath;
    /**
     * 名称全路径
     */
    private String namePath;
    /**
     * 版本
     */
    @Version
    private String version;
    /**
     * 权限id
     */
    private String authPortalId;
    /**
     * 创建人
     */
    private String optUser;
    /**
     * 
     */
    private String multiUsers;
    /**
     * 扩展属性
     */
    private String extraAttrs;
    /**
     * 
     */
    private String multiUsersString;
    /**
     * 状态：1启用，0：禁用
     */
    private String status;
    /**
     * 人员数量
     */
    private String userNum;

}
