package com.rzdata.system.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 申石用户表业务对象 t_unify_user
 *
 * <AUTHOR>
 * @date 2024-07-15
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("申石用户表业务对象")
public class UnifyUserBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 员工号
     */
    @ApiModelProperty(value = "员工号", required = true)
    @NotBlank(message = "员工号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sn;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型", required = true)
    @NotBlank(message = "用户类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userTypeId;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话", required = true)
    @NotBlank(message = "电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String telephone;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱", required = true)
    @NotBlank(message = "邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String optUser;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性", required = true)
    @NotBlank(message = "扩展属性不能为空", groups = { AddGroup.class, EditGroup.class })
    private String extraAttrs;

    /**
     * 租户
     */
    @ApiModelProperty(value = "租户", required = true)
    @NotBlank(message = "租户不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appName;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id", required = true)
    @NotBlank(message = "组织id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgId;

    /**
     * 组织号
     */
    @ApiModelProperty(value = "组织号", required = true)
    @NotBlank(message = "组织号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgSn;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称", required = true)
    @NotBlank(message = "组织名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgName;

    /**
     * 组织路径
     */
    @ApiModelProperty(value = "组织路径", required = true)
    @NotBlank(message = "组织路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgNamePath;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位", required = true)
    @NotBlank(message = "职位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String position;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标", required = true)
    @NotBlank(message = "图标不能为空", groups = { AddGroup.class, EditGroup.class })
    private String image;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型", required = true)
    @NotBlank(message = "组织类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgTypeId;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userTypeName;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String multiOrgs;

    /**
     * 职位信息
     */
    @ApiModelProperty(value = "职位信息", required = true)
    @NotBlank(message = "职位信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String positions;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型", required = true)
    @NotBlank(message = "组织类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgType;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String multiOrgsString;

    /**
     * 职位类型
     */
    @ApiModelProperty(value = "职位类型", required = true)
    @NotBlank(message = "职位类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String positionType;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String groupId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
