package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/3/10 10:47
 * @Version 1.0
 * @Description
 */
@Data
@TableName("sys_user_tenant")
public class SysUserTenant {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户表主键id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 租户表主键id
     */
    private String tenantId;

    /**
     * 租户表租户名称
     */
    private String tenantName;
}
