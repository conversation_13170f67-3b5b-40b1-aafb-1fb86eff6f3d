package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户和岗位关联 sys_user_post
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@TableName("sys_user_post")
@ApiModel("用户和岗位关联")
public class SysUserPost {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**
     * 岗位ID
     */
    @ApiModelProperty(value = "岗位ID")
    private Long postId;

}
