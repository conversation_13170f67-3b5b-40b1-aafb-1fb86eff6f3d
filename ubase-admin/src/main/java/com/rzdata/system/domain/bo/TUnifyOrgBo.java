package com.rzdata.system.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 申石组织表业务对象 t_unify_org
 *
 * <AUTHOR>
 * @date 2025-01-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("申石组织表业务对象")
public class TUnifyOrgBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 简称
     */
    @ApiModelProperty(value = "简称", required = true)
    @NotBlank(message = "简称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sn;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称", required = true)
    @NotBlank(message = "组织名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 上级组织
     */
    @ApiModelProperty(value = "上级组织", required = true)
    @NotBlank(message = "上级组织不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sortNum;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型", required = true)
    @NotBlank(message = "组织类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orgTypeId;

    /**
     * 简称全路径
     */
    @ApiModelProperty(value = "简称全路径", required = true)
    @NotBlank(message = "简称全路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String snPath;

    /**
     * id全路径
     */
    @ApiModelProperty(value = "id全路径", required = true)
    @NotBlank(message = "id全路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idPath;

    /**
     * 名称全路径
     */
    @ApiModelProperty(value = "名称全路径", required = true)
    @NotBlank(message = "名称全路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String namePath;

    /**
     * 权限id
     */
    @ApiModelProperty(value = "权限id", required = true)
    @NotBlank(message = "权限id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String authPortalId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true)
    @NotBlank(message = "创建人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String optUser;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String multiUsers;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性", required = true)
    @NotBlank(message = "扩展属性不能为空", groups = { AddGroup.class, EditGroup.class })
    private String extraAttrs;

    /**
     * 
     */
    @ApiModelProperty(value = "", required = true)
    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String multiUsersString;

    /**
     * 状态：1启用，0：禁用
     */
    @ApiModelProperty(value = "状态：1启用，0：禁用", required = true)
    @NotBlank(message = "状态：1启用，0：禁用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 人员数量
     */
    @ApiModelProperty(value = "人员数量", required = true)
    @NotBlank(message = "人员数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private String userNum;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
