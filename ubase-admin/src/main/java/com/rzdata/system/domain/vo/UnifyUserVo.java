package com.rzdata.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 申石用户表视图对象 t_unify_user
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@ApiModel("申石用户表视图对象")
@ExcelIgnoreUnannotated
public class UnifyUserVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 员工号
     */
	@ExcelProperty(value = "员工号")
	@ApiModelProperty("员工号")
	private String sn;

    /**
     * 名称
     */
	@ExcelProperty(value = "名称")
	@ApiModelProperty("名称")
	private String name;

    /**
     * 性别
     */
	@ExcelProperty(value = "性别")
	@ApiModelProperty("性别")
	private String sex;

    /**
     * 用户类型
     */
	@ExcelProperty(value = "用户类型")
	@ApiModelProperty("用户类型")
	private String userTypeId;

    /**
     * 电话
     */
	@ExcelProperty(value = "电话")
	@ApiModelProperty("电话")
	private String telephone;

    /**
     * 邮箱
     */
	@ExcelProperty(value = "邮箱")
	@ApiModelProperty("邮箱")
	private String email;

    /**
     * 状态
     */
	@ExcelProperty(value = "状态")
	@ApiModelProperty("状态")
	private String status;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String optUser;

    /**
     * 扩展属性
     */
	@ExcelProperty(value = "扩展属性")
	@ApiModelProperty("扩展属性")
	private String extraAttrs;

    /**
     * 租户
     */
	@ExcelProperty(value = "租户")
	@ApiModelProperty("租户")
	private String appName;

    /**
     * 组织id
     */
	@ExcelProperty(value = "组织id")
	@ApiModelProperty("组织id")
	private String orgId;

    /**
     * 组织号
     */
	@ExcelProperty(value = "组织号")
	@ApiModelProperty("组织号")
	private String orgSn;

    /**
     * 组织名称
     */
	@ExcelProperty(value = "组织名称")
	@ApiModelProperty("组织名称")
	private String orgName;

    /**
     * 组织路径
     */
	@ExcelProperty(value = "组织路径")
	@ApiModelProperty("组织路径")
	private String orgNamePath;

    /**
     * 职位
     */
	@ExcelProperty(value = "职位")
	@ApiModelProperty("职位")
	private String position;

    /**
     * 图标
     */
	@ExcelProperty(value = "图标")
	@ApiModelProperty("图标")
	private String image;

    /**
     * 组织类型
     */
	@ExcelProperty(value = "组织类型")
	@ApiModelProperty("组织类型")
	private String orgTypeId;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String userTypeName;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String multiOrgs;

    /**
     * 职位信息
     */
	@ExcelProperty(value = "职位信息")
	@ApiModelProperty("职位信息")
	private String positions;

    /**
     * 组织类型
     */
	@ExcelProperty(value = "组织类型")
	@ApiModelProperty("组织类型")
	private String orgType;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String multiOrgsString;

    /**
     * 职位类型
     */
	@ExcelProperty(value = "职位类型")
	@ApiModelProperty("职位类型")
	private String positionType;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String groupId;


}
