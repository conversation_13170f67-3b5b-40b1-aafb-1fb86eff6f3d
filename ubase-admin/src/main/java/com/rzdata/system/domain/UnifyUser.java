package com.rzdata.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 申石用户表对象 t_unify_user
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Data
@Accessors(chain = true)
@TableName("t_unify_user")
public class UnifyUser{

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;
    /**
     * 员工号
     */
    private String sn;
    /**
     * 名称
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 用户类型
     */
    private String userTypeId;
    /**
     * 电话
     */
    private String telephone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 状态
     */
    private String status;
    /**
     * 
     */
    private String optUser;
    /**
     * 扩展属性
     */
    private String extraAttrs;
    /**
     * 租户
     */
    private String appName;
    /**
     * 组织id
     */
    private String orgId;
    /**
     * 组织号
     */
    private String orgSn;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 组织路径
     */
    private String orgNamePath;
    /**
     * 职位
     */
    private String position;
    /**
     * 图标
     */
    private String image;
    /**
     * 组织类型
     */
    private String orgTypeId;
    /**
     * 
     */
    private String userTypeName;
    /**
     * 
     */
    private String multiOrgs;
    /**
     * 职位信息
     */
    private String positions;
    /**
     * 组织类型
     */
    private String orgType;
    /**
     * 
     */
    private String multiOrgsString;
    /**
     * 职位类型
     */
    private String positionType;
    /**
     * 
     */
    private String groupId;

}
