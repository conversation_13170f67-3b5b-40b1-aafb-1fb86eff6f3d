package com.rzdata.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 申石组织表视图对象 t_unify_org
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@ApiModel("申石组织表视图对象")
@ExcelIgnoreUnannotated
public class TUnifyOrgVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 简称
     */
	@ExcelProperty(value = "简称")
	@ApiModelProperty("简称")
	private String sn;

    /**
     * 组织名称
     */
	@ExcelProperty(value = "组织名称")
	@ApiModelProperty("组织名称")
	private String name;

    /**
     * 上级组织
     */
	@ExcelProperty(value = "上级组织")
	@ApiModelProperty("上级组织")
	private String parentId;

    /**
     * 排序
     */
	@ExcelProperty(value = "排序")
	@ApiModelProperty("排序")
	private Long sortNum;

    /**
     * 组织类型
     */
	@ExcelProperty(value = "组织类型")
	@ApiModelProperty("组织类型")
	private String orgTypeId;

    /**
     * 简称全路径
     */
	@ExcelProperty(value = "简称全路径")
	@ApiModelProperty("简称全路径")
	private String snPath;

    /**
     * id全路径
     */
	@ExcelProperty(value = "id全路径")
	@ApiModelProperty("id全路径")
	private String idPath;

    /**
     * 名称全路径
     */
	@ExcelProperty(value = "名称全路径")
	@ApiModelProperty("名称全路径")
	private String namePath;

    /**
     * 权限id
     */
	@ExcelProperty(value = "权限id")
	@ApiModelProperty("权限id")
	private String authPortalId;

    /**
     * 创建人
     */
	@ExcelProperty(value = "创建人")
	@ApiModelProperty("创建人")
	private String optUser;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String multiUsers;

    /**
     * 扩展属性
     */
	@ExcelProperty(value = "扩展属性")
	@ApiModelProperty("扩展属性")
	private String extraAttrs;

    /**
     * 
     */
	@ExcelProperty(value = "")
	@ApiModelProperty("")
	private String multiUsersString;

    /**
     * 状态：1启用，0：禁用
     */
	@ExcelProperty(value = "状态：1启用，0：禁用")
	@ApiModelProperty("状态：1启用，0：禁用")
	private String status;

    /**
     * 人员数量
     */
	@ExcelProperty(value = "人员数量")
	@ApiModelProperty("人员数量")
	private String userNum;


}
