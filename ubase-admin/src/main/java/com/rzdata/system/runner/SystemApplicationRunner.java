package com.rzdata.system.runner;

import cn.hutool.json.JSONUtil;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import com.rzdata.system.service.ISysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化 system 模块对应业务数据
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Component
public class SystemApplicationRunner implements ApplicationRunner {

    private final CustomConfig customConfig;
    private final ISysConfigService configService;
    private final ISysDictTypeService dictTypeService;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (customConfig.isCacheLazy()){
            return;
        }
        configService.loadingConfigCache();
        log.info("加载参数缓存数据成功");
        dictTypeService.loadingDictCache();
        log.info("加载字典缓存数据成功");
    }

}
