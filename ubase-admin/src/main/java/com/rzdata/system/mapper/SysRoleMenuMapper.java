package com.rzdata.system.mapper;

import com.rzdata.framework.core.domain.entity.SysRoleMenuVo;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.system.domain.SysRoleMenu;

import java.util.List;
import java.util.Map;

/**
 * 角色与菜单关联表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysRoleMenuMapper extends BaseMapperPlus<SysRoleMenu> {
    void updateScopeById(Map<String, Object> params);

    List<SysRoleMenuVo> queryDataScopeList(Long roleId);
}
