package com.rzdata.system.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.system.domain.vo.SysUserVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysUserMapper extends BaseMapperPlus<SysUser> {

    Page<SysUser> selectPageUserList(@Param("page") Page<SysUser> page, @Param("user") SysUser user);

    Page<SysUser> selectPageUserListForGroup(@Param("page") Page<SysUser> page, @Param("user") SysUser user);

    /**
     * 根据角色key查询用户列表
     *
     * @param roleKey 角色key
     * @return 用户信息集合信息
     */
    List<SysUser> selectListAllByRoleKey(String roleKey);

    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询未已配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    Page<SysUser> selectAllocatedList(@Param("page") Page<SysUser> page, @Param("user") SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    Page<SysUser> selectUnallocatedList(@Param("page") Page<SysUser> page, @Param("user") SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(String userId);

    /**
     * 通过部门id查出部门文件管理员
     * @param deptId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SysUser> selectDeptFileManagerByDeptId(String deptId);

    @InterceptorIgnore(tenantLine = "true")
    List<SysUser> selectQaAndSc();

    @InterceptorIgnore(tenantLine = "true")
    SysUser isQa(String username);

    List<SysUserVo> selectUserToAiShu();

    /**
     *  查出指定部门指定岗位指定租户的用户
     *  定时任务专用
     * @return 用户列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<SysUserVo> selectUserByDeptPost(@Param("deptIds") List<String> deptIds, @Param("postIds") List<Integer> postIds,@Param("tenantId") String tenantId);

    /**
     * 通过部门id和菜单权限代码(sys_menu的perms) 分页查询出该部门下具有该权限的用户
     * @param deptId 部门id
     * @param perms 权限代码(sys_menu的perms字段)
     * @return
     */
    Page<SysUser> selectUserByDeptIdAndPerms(@Param("page") Page<SysUser> page,@Param("deptId") String deptId, @Param("perms")String perms);
}
