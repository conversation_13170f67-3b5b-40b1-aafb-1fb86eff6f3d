package com.rzdata.system.mapper;

import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.system.domain.SysUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与角色关联表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysUserRoleMapper extends BaseMapperPlus<SysUserRole> {


    boolean addRole(@Param("userId") String userId, @Param("dataBaseList") List<String> dataBaseList,@Param("roleId") String roleId);
}
