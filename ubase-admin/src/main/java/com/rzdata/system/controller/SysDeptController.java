package com.rzdata.system.controller;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ArrayUtil;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.constant.UserConstants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.system.domain.vo.SysDeptVo;
import com.rzdata.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 部门信息
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "部门控制器", tags = {"部门管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {

    private final ISysDeptService deptService;

    /**
     * 获取部门列表
     */
    @ApiOperation("获取部门列表")
    @GetMapping("/list")
    public AjaxResult<List<SysDept>> list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return AjaxResult.success(depts);
    }


    /**
     * 获取部门列表
     */
    @ApiOperation("获取分发部门列表")
    @GetMapping("/listForDist")
    public AjaxResult<List<SysDept>> listForDist(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptListForDist(dept);
        return AjaxResult.success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @ApiOperation("查询部门列表（排除节点）")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult<List<SysDept>> excludeChild(@ApiParam("部门ID") @PathVariable(value = "deptId", required = false) String deptId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        depts.removeIf(d -> d.getDeptId().equals(deptId)
                || ArrayUtil.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return AjaxResult.success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @ApiOperation("根据部门编号获取详细信息")
    @GetMapping(value = "/{deptId}")
    public AjaxResult<SysDept> getInfo(@ApiParam("部门ID") @PathVariable String deptId) {
        deptService.checkDeptDataScope(deptId);
        return AjaxResult.success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取部门下拉树列表
     */
    @ApiOperation("获取部门下拉树列表")
    @GetMapping("/treeselect")
    public AjaxResult<List<Tree<String>>> treeselect(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return AjaxResult.success(deptService.buildDeptTreeSelect(depts));
    }

    /**
     * 加载对应角色部门列表树
     */
    @ApiOperation("加载对应角色部门列表树")
    @GetMapping(value = "/roleDeptTreeselect/{roleId}")
    public AjaxResult<Map<String, Object>> roleDeptTreeselect(@ApiParam("角色ID") @PathVariable("roleId") Long roleId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
        ajax.put("depts", deptService.buildDeptTreeSelect(depts));
        return AjaxResult.success(ajax);
    }

    /**
     * 新增部门
     */
    @ApiOperation("新增部门")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Void> add(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @ApiOperation("修改部门")
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Void> edit(@Validated @RequestBody SysDept dept) {
        if (UserConstants.NOT_UNIQUE.equals(deptService.checkDeptNameUnique(dept))) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(dept.getDeptId())) {
            return AjaxResult.error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())
                && deptService.selectNormalChildrenDeptById(dept.getDeptId()) > 0) {
            return AjaxResult.error("该部门包含未停用的子部门！");
        }
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @ApiOperation("删除部门")
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult<Void> remove(@ApiParam("部门ID串") @PathVariable String deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return AjaxResult.error("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return AjaxResult.error("部门存在用户,不允许删除");
        }
        return toAjax(deptService.deleteDeptById(deptId));
    }

    @GetMapping("/selectDeptListToAiShu")
    @ApiOperation("爱数部门同步查询接口")
    public List<SysDeptVo> selectDeptListToAiShu() {
        return deptService.selectDeptListToAiShu();
    }

    @ApiOperation("用户初始化部门表参数")
    @GetMapping("/initDeptFullPath")
    public void initDeptFullPath() {
        // 顶级节点
        SysDept topNode = this.deptService.getById("100");
        topNode.setDeptFullPathId(topNode.getDeptId()+",1");
        topNode.setDeptFullPathName(topNode.getDeptName()+",顶级组织");
        topNode.setAncestors("1,100");
        topNode.setDeptLevel(1);
        this.deptService.updateById(topNode);
        // 查询子节点
        SysDept queryNode = new SysDept();
        queryNode.setParentId(topNode.getDeptId());
        List<SysDept> subList = this.deptService.selectDeptList(queryNode);
        for(SysDept item : subList) {
            // 更新子节点
            this.initDeptFullPathRecursion(item,topNode,item.getDeptId());
        }
    }

    /**
     * 递归更新子部门全路径
     *
     * @param currNode
     * @param parentNode
     * @param secDeptId
     */
    private void initDeptFullPathRecursion(SysDept currNode,SysDept parentNode,String secDeptId) {
        // 更新当前节点
        currNode.setDeptFullPathId(currNode.getDeptId()+","+parentNode.getDeptFullPathId());
        currNode.setDeptFullPathName(currNode.getDeptName()+","+parentNode.getDeptFullPathName());
        currNode.setAncestors(parentNode.getAncestors()+","+currNode.getDeptId());
        currNode.setDeptLevel(parentNode.getDeptLevel()+1);
        currNode.setSecDeptId(secDeptId);
        this.deptService.updateById(currNode);
        // 查询子节点
        SysDept queryNode = new SysDept();
        queryNode.setParentId(currNode.getDeptId());
        List<SysDept> subList = this.deptService.selectDeptList(queryNode);
        for(SysDept item : subList) {
            // 更新子节点
            this.initDeptFullPathRecursion(item,currNode,secDeptId);
        }
    }
}
