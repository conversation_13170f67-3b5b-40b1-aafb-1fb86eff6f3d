package com.rzdata.system.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.vo.UnifyUserVo;
import com.rzdata.system.domain.bo.UnifyUserBo;
import com.rzdata.system.service.IUnifyUserService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 申石用户表Controller
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Validated
@Api(value = "申石用户表控制器", tags = {"申石用户表管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/unifyUser")
public class UnifyUserController extends BaseController {

    private final IUnifyUserService iUnifyUserService;

    /**
     * 查询申石用户表列表
     */
    @ApiOperation("查询申石用户表列表")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:list')")
    @GetMapping("/list")
    public TableDataInfo<UnifyUserVo> list(@Validated(QueryGroup.class) UnifyUserBo bo) {
        return iUnifyUserService.queryPageList(bo);
    }

    /**
     * 同步申石用户表列表
     */
    @ApiOperation("同步申石用户表列表")
    @GetMapping("/sycn")
    public Boolean sycn() {
        return iUnifyUserService.sycn();
    }

    /**
     * 导出申石用户表列表
     */
    @ApiOperation("导出申石用户表列表")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:export')")
    @Log(title = "申石用户表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated UnifyUserBo bo, HttpServletResponse response) {
        List<UnifyUserVo> list = iUnifyUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "申石用户表", UnifyUserVo.class, response);
    }

    /**
     * 获取申石用户表详细信息
     */
    @ApiOperation("获取申石用户表详细信息")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:query')")
    @GetMapping("/{id}")
    public AjaxResult<UnifyUserVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iUnifyUserService.queryById(id));
    }

    /**
     * 新增申石用户表
     */
    @ApiOperation("新增申石用户表")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:add')")
    @Log(title = "申石用户表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody UnifyUserBo bo) {
        return toAjax(iUnifyUserService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改申石用户表
     */
    @ApiOperation("修改申石用户表")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:edit')")
    @Log(title = "申石用户表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody UnifyUserBo bo) {
        return toAjax(iUnifyUserService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除申石用户表
     */
    @ApiOperation("删除申石用户表")
    @PreAuthorize("@ss.hasPermi('system:unifyUser:remove')")
    @Log(title = "申石用户表" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iUnifyUserService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
