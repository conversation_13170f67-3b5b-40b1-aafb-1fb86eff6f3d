package com.rzdata.system.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.system.domain.vo.TUnifyOrgVo;
import com.rzdata.system.domain.bo.TUnifyOrgBo;
import com.rzdata.system.service.ITUnifyOrgService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 申石组织表Controller
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Validated
@Api(value = "申石组织表控制器", tags = {"申石组织表管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/unifyOrg")
public class TUnifyOrgController extends BaseController {

    private final ITUnifyOrgService iTUnifyOrgService;

    /**
     * 查询申石组织表列表
     */
    @ApiOperation("查询申石组织表列表")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:list')")
    @GetMapping("/list")
    public TableDataInfo<TUnifyOrgVo> list(@Validated(QueryGroup.class) TUnifyOrgBo bo) {
        return iTUnifyOrgService.queryPageList(bo);
    }

    /**
     * 导出申石组织表列表
     */
    @ApiOperation("导出申石组织表列表")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:export')")
    @Log(title = "申石组织表", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated TUnifyOrgBo bo, HttpServletResponse response) {
        List<TUnifyOrgVo> list = iTUnifyOrgService.queryList(bo);
        ExcelUtil.exportExcel(list, "申石组织表", TUnifyOrgVo.class, response);
    }

    /**
     * 获取申石组织表详细信息
     */
    @ApiOperation("获取申石组织表详细信息")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:query')")
    @GetMapping("/{id}")
    public AjaxResult<TUnifyOrgVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iTUnifyOrgService.queryById(id));
    }

    /**
     * 新增申石组织表
     */
    @ApiOperation("新增申石组织表")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:add')")
    @Log(title = "申石组织表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody TUnifyOrgBo bo) {
        return toAjax(iTUnifyOrgService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改申石组织表
     */
    @ApiOperation("修改申石组织表")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:edit')")
    @Log(title = "申石组织表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody TUnifyOrgBo bo) {
        return toAjax(iTUnifyOrgService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除申石组织表
     */
    @ApiOperation("删除申石组织表")
    @PreAuthorize("@ss.hasPermi('system:unifyOrg:remove')")
    @Log(title = "申石组织表" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iTUnifyOrgService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
