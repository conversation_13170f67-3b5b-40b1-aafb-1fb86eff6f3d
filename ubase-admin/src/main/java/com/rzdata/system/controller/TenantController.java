package com.rzdata.system.controller;

import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.properties.TokenProperties;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.Tenant;
import com.rzdata.process.domain.bo.TenantBo;
import com.rzdata.process.domain.vo.TenantVo;
import com.rzdata.system.service.ITenantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 客户Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@Validated
@Api(value = "客户控制器", tags = {"客户管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/basic/tenant")
public class TenantController extends BaseController {

    @Resource
    private ITenantService iTenantService;

    @Resource
    private TokenProperties tokenProperties;


    /**
     * 查询客户列表
     */
    @ApiOperation("查询客户列表")
    //@PreAuthorize("@ss.hasPermi('basic:tenant:list')")
    @GetMapping("/list")
    public TableDataInfo<TenantVo> list(@Validated(QueryGroup.class) TenantBo bo) {
        return iTenantService.queryPageList(bo);
    }

    @ApiOperation("查询客户列表")
    @PostMapping("/listNoPage")
    public List<TenantVo> listNoPage(@RequestBody TenantBo bo) {
        return iTenantService.queryList(bo);
    }

    /**
     * 导出客户列表
     */
    @ApiOperation("导出客户列表")
    //@PreAuthorize("@ss.hasPermi('basic:tenant:export')")
    @Log(title = "客户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated TenantBo bo, HttpServletResponse response) {
        List<TenantVo> list = iTenantService.queryList(bo);
        ExcelUtil.exportExcel(list, "客户", TenantVo.class, response);
    }

    /**
     * 获取客户详细信息
     */
    @ApiOperation("获取客户详细信息")
    @GetMapping("/{id}")
    public AjaxResult<TenantVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iTenantService.queryById(id));
    }

    /**
     * 新增客户
     */
    @ApiOperation("新增客户")
    //@PreAuthorize("@ss.hasPermi('basic:tenant:add')")
    @Log(title = "客户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody TenantBo bo) {
        return toAjax(iTenantService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改客户
     */
    @ApiOperation("修改客户")
    //@PreAuthorize("@ss.hasPermi('basic:tenant:edit')")
    @Log(title = "客户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody TenantBo bo) {
        return toAjax(iTenantService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除客户
     */
    @ApiOperation("删除客户")
    //@PreAuthorize("@ss.hasPermi('basic:tenant:remove')")
    @Log(title = "客户" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iTenantService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }

    @ApiOperation("切换租户")
    @GetMapping("/cutTenant/{id}")
    public AjaxResult<Void> cutCustomer(@PathVariable("id") String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        loginUser.setTenantId(id);
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(loginUser.getLoginTime() + tokenProperties.getExpireTime() * 60 * 1000);
        // 获取登录用户token
        String userKey = Constants.LOGIN_TOKEN_KEY + loginUser.getToken();
        // 根据用户token 更新用户基础信息
        SpringUtils.getBean(RedisCache.class).setCacheObject(userKey, loginUser, tokenProperties.getExpireTime(), TimeUnit.MINUTES);
        return AjaxResult.success();
    }

    @ApiOperation("获取租户信息 根据登录用户")
    @PostMapping("/selectTenantList")
    public AjaxResult<List<Tenant>> selectCustomerList(@RequestBody TenantBo bo) {
        return AjaxResult.success(iTenantService.selectTenantList(bo));
    }
}
