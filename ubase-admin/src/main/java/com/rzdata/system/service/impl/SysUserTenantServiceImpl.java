package com.rzdata.system.service.impl;

import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.system.domain.SysUserTenant;
import com.rzdata.system.domain.vo.SysUserTenantVo;
import com.rzdata.system.mapper.SysUserTenantMapper;
import com.rzdata.system.service.ISysUserTenantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/3/10 10:52
 * @Version 1.0
 * @Description
 */
@Service
public class SysUserTenantServiceImpl extends ServicePlusImpl<SysUserTenantMapper, SysUserTenant, SysUserTenantVo> implements ISysUserTenantService {
    @Override
    public List<String> selectTenantListByUserId(String userId) {
        return this.baseMapper.selectTenantListByUserId(userId);
        //LambdaQueryWrapper<SysUserTenant> queryWrapper = new LambdaQueryWrapper<>();
        //queryWrapper.eq(SysUserTenant::getUserId,userId);
        //return listVo(queryWrapper);
    }
}
