package com.rzdata.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rzdata.framework.aspectj.DataScopeAspect;
import com.rzdata.framework.core.domain.entity.SysRoleMenuVo;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.system.domain.SysRoleMenu;
import com.rzdata.system.mapper.SysRoleMenuMapper;
import com.rzdata.system.service.ISysRoleMenuService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 角色 业务层处理
 *
 * <AUTHOR> Li
 */
@Service
public class SysRoleMenuServiceImpl extends
        ServicePlusImpl<SysRoleMenuMapper, SysRoleMenu, SysRoleMenuVo> implements ISysRoleMenuService {
    @Override
    public String queryDataScope(Long roleId, Long buttonId) {
        SysRoleMenu sysRoleMenu = this.baseMapper.selectOne(new LambdaQueryWrapper<SysRoleMenu>().
                eq(SysRoleMenu::getRoleId, roleId).eq(SysRoleMenu::getMenuId, buttonId));
        String perms = DataScopeAspect.DATA_SCOPE_ALL;

        if(sysRoleMenu!=null) {
            perms = sysRoleMenu.getPerms();
        }
        return perms;
    }

    @Override
    public void updateMenuScope(Map<String,Object> params) {
        this.getBaseMapper().updateScopeById(params);
    }

    @Override
    public List<SysRoleMenuVo> queryDataScopeList(Long roleId) {
       return this.getBaseMapper().queryDataScopeList(roleId);
    }


}
