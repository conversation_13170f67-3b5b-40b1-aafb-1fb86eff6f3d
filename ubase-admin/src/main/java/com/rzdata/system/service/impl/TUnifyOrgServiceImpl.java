package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.rzdata.config.OaProperties;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.system.domain.UnifyUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.system.domain.bo.TUnifyOrgBo;
import com.rzdata.system.domain.vo.TUnifyOrgVo;
import com.rzdata.system.domain.TUnifyOrg;
import com.rzdata.system.mapper.TUnifyOrgMapper;
import com.rzdata.system.service.ITUnifyOrgService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 申石组织表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@Service
public class TUnifyOrgServiceImpl extends ServicePlusImpl<TUnifyOrgMapper, TUnifyOrg, TUnifyOrgVo> implements ITUnifyOrgService {

    @Resource
    private OaProperties oaProperties;

    @Override
    public TUnifyOrgVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<TUnifyOrgVo> queryPageList(TUnifyOrgBo bo) {
        PagePlus<TUnifyOrg, TUnifyOrgVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<TUnifyOrgVo> queryList(TUnifyOrgBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<TUnifyOrg> buildQueryWrapper(TUnifyOrgBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TUnifyOrg> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), TUnifyOrg::getSn, bo.getSn());
        lqw.like(StringUtils.isNotBlank(bo.getName()), TUnifyOrg::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getParentId()), TUnifyOrg::getParentId, bo.getParentId());
        lqw.eq(bo.getSortNum() != null, TUnifyOrg::getSortNum, bo.getSortNum());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgTypeId()), TUnifyOrg::getOrgTypeId, bo.getOrgTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getSnPath()), TUnifyOrg::getSnPath, bo.getSnPath());
        lqw.eq(StringUtils.isNotBlank(bo.getIdPath()), TUnifyOrg::getIdPath, bo.getIdPath());
        lqw.eq(StringUtils.isNotBlank(bo.getNamePath()), TUnifyOrg::getNamePath, bo.getNamePath());
        lqw.eq(StringUtils.isNotBlank(bo.getAuthPortalId()), TUnifyOrg::getAuthPortalId, bo.getAuthPortalId());
        lqw.eq(StringUtils.isNotBlank(bo.getOptUser()), TUnifyOrg::getOptUser, bo.getOptUser());
        lqw.eq(StringUtils.isNotBlank(bo.getMultiUsers()), TUnifyOrg::getMultiUsers, bo.getMultiUsers());
        lqw.eq(StringUtils.isNotBlank(bo.getExtraAttrs()), TUnifyOrg::getExtraAttrs, bo.getExtraAttrs());
        lqw.eq(StringUtils.isNotBlank(bo.getMultiUsersString()), TUnifyOrg::getMultiUsersString, bo.getMultiUsersString());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TUnifyOrg::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getUserNum()), TUnifyOrg::getUserNum, bo.getUserNum());
        return lqw;
    }

    @Override
    public Boolean insertByBo(TUnifyOrgBo bo) {
        TUnifyOrg add = BeanUtil.toBean(bo, TUnifyOrg.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(TUnifyOrgBo bo) {
        TUnifyOrg update = BeanUtil.toBean(bo, TUnifyOrg.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(TUnifyOrg entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    /**
     * 同步申石用户表
     * @return
     */
    @Override
    public Boolean sycn(){
        //登录申石系统，获取Token
        String tokenUrl = oaProperties.getSycn().getUrl() + "/api/sys/getToken";
        log.error("申石登录地址：{}",tokenUrl);
        Map<String,Object> paramMap = new HashMap<String,Object>();
        paramMap.put("username",oaProperties.getSycn().getUsername());
        paramMap.put("password",oaProperties.getSycn().getPassword());
        log.error("参数:{}",paramMap.toString());
        String result = HttpRequest.post(tokenUrl)
                .body(JSONUtil.toJsonStr(paramMap))
                .header("Content-Type", "application/json").execute().body();
        log.error("获得access_token:{}",result);
        JSONObject json = JSONUtil.parseObj(result);

        //判断是否登录成功
        if("true".equals(json.get("success").toString())){
            String token = json.get("msg").toString();

            //请求用户参数组装
            Integer pageSize = 1000;
            Integer page = 1;
            String orgInfoUrl = oaProperties.getSycn().getUrl() + "/api/org/getList";
            Map<String,Object> orgParamMap = new HashMap<String,Object>();
            orgParamMap.put("orgSn","");
            orgParamMap.put("appSn",oaProperties.getSycn().getClient());
            orgParamMap.put("page",page);
            orgParamMap.put("pageSize",pageSize);
            orgParamMap.put("startTime","");
            log.error("请求组织列表{}",orgParamMap.toString());

            // 请求用户列表
            JSONObject orgInfoJson = this.getOrgList(orgInfoUrl,orgParamMap,token);
            if(orgInfoJson == null){
                log.error("请求申石用户失败");
                return false;
            }

            //获取列表，判断用户是否在第一页
            String data = orgInfoJson.get("data").toString();
            Integer totalCount =Integer.valueOf(orgInfoJson.get("totalCount").toString());
            Integer totalPage = totalCount / pageSize;
            if (totalCount % pageSize != 0) {
                totalPage++;
            }
            log.error("总共{}页,{}条",totalPage,totalCount);
            List<TUnifyOrg> orgList = JSON.parseObject(data, new TypeReference<List<TUnifyOrg>>(){});
            page++;
            for(int i=page;i<=totalPage;i++){
                orgParamMap.put("page",i);
                JSONObject nixtUserInfoJson = this.getOrgList(orgInfoUrl,orgParamMap,token);
                if(nixtUserInfoJson == null){
                    log.error("请求申石组织失败");
                    continue;
                }
                log.error("第{}页",i);
                String nextData = nixtUserInfoJson.get("data").toString();
                List<TUnifyOrg> nextOrgList = JSON.parseObject(nextData, new TypeReference<List<TUnifyOrg>>(){});
                orgList.addAll(nextOrgList);
            }
            this.saveOrUpdateBatch(orgList);
        }
        return true;
    }

    private JSONObject getOrgList(String orgUrl,Map<String,Object> orgParamMap,String token){
        // 请求用户列表
        String orgInfoResult = HttpRequest.post(orgUrl)
                .body(JSONUtil.toJsonStr(orgParamMap))
                .header("uim-login-user-id", token).execute().body();
        JSONObject userInfoJson = JSONUtil.parseObj(orgInfoResult);
        if(!"true".equals(userInfoJson.get("success").toString())){
            log.error("请求错误:{}",orgInfoResult);
            return null;
        }
        return userInfoJson;
    }

}
