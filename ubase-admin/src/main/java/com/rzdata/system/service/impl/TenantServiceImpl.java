package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Tenant;
import com.rzdata.process.domain.bo.TenantBo;
import com.rzdata.process.domain.vo.TenantVo;
import com.rzdata.process.mapper.TenantMapper;
import com.rzdata.system.service.ISysUserService;
import com.rzdata.system.service.ISysUserTenantService;
import com.rzdata.system.service.ITenantService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 客户Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@Service
public class TenantServiceImpl extends ServicePlusImpl<TenantMapper, Tenant, TenantVo> implements ITenantService {

    @Resource
    ISysUserTenantService sysUserTenantService;

    @Resource
    ISysUserService sysUserService;

    @Value("${document.server.loginUrl}")
    private String url;

    @Override
    public TenantVo queryById(String id){
        TenantVo vo = getVoById(id);
        return vo;
    }

    @Override
    public TableDataInfo<TenantVo> queryPageList(TenantBo bo) {
        PagePlus<Tenant, TenantVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<TenantVo> queryList(TenantBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Tenant> buildQueryWrapper(TenantBo bo) {
        //Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Tenant> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTenantName()), Tenant::getTenantName, bo.getTenantName());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), Tenant::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContact()), Tenant::getContact, bo.getContact());
        lqw.eq(StringUtils.isNotBlank(bo.getContactTel()), Tenant::getContactTel, bo.getContactTel());
        if (ObjectUtil.isNotEmpty(bo.getSearchValue())) {
            lqw.like(Tenant:: getTenantName, bo.getSearchValue()).or().like(Tenant:: getContactTel, bo.getSearchValue())
                    .or().like(Tenant:: getContact, bo.getSearchValue());
        }
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TenantBo bo) {
        Tenant add = BeanUtil.toBean(bo, Tenant.class);
        boolean flag = save(add);
        if (flag) {
            //List<SysUser> list = sysUserService.list();
            //list.forEach(sysUser -> {
            //    SysUserTenant sysUserTenant = new SysUserTenant();
            //    sysUserTenant.setId(null);
            //    sysUserTenant.setTenantId(add.getId());
            //    sysUserTenant.setUserName(sysUser.getUserName());
            //    sysUserTenant.setUserId(sysUser.getUserId());
            //    sysUserTenantService.save(sysUserTenant);
//
            //});
            bo.setUrl(url+"?"+"tenantId="+add.getId());
            bo.setId(add.getId());
            updateByBo(bo);
        }
        return flag;
    }

   // private SysDept FindSysDept (String tenantId) {
   //     QueryWrapper<SysDept> queryWrapper = new QueryWrapper<>();
   //     queryWrapper.lambda().eq(SysDept:: getStatus, NumberConstants.ZERO);
   //     queryWrapper.lambda().eq(SysDept:: getDelFlag, NumberConstants.ZERO);
   //     queryWrapper.lambda().eq(SysDept:: getTenantId, tenantId);
   //     return iSysDeptService.getOne(queryWrapper);
   // }

    /**
     * 新增部门
     */
    //private void saveOrUpdateDeptInfo(String deptName, String tenantId) {
    //    SysDept sysDept = FindSysDept(tenantId);
    //    if (ObjectUtil.isEmpty(sysDept)) {
    //        sysDept = new SysDept();
    //        sysDept.setTenantId(tenantId);
    //        sysDept.setDeptName(deptName);
    //        sysDept.setParentId(NumberConstants.HUNDRED.longValue());
    //        sysDept.setStatus(NumberConstants.ZERO+"");
    //        sysDept.setDelFlag(NumberConstants.ZERO+"");
    //        sysDept.setAncestors(NumberConstants.ZERO+Constants.COMMA+NumberConstants.HUNDRED);
    //        sysDept.setDeptType(Constants.DEPT_TYPE_ORG);
    //        iSysDeptService.saveOrUpdate(sysDept);
    //    } else {
    //        sysDept.setDeptName(deptName);
    //        iSysDeptService.updateById(sysDept);
    //    }
    //}


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TenantBo bo) {
        Tenant update = BeanUtil.toBean(bo, Tenant.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Tenant entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        /*if (ObjectUtil.isNotEmpty(ids)) {
            for (String id : ids) {
                QueryWrapper<SysDept> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(SysDept:: getStatus, NumberConstants.ZERO);
                queryWrapper.lambda().eq(SysDept:: getDelFlag, NumberConstants.ZERO);
                queryWrapper.lambda().eq(SysDept:: getTenantId, id);
                List<SysDept> list = iSysDeptService.list(queryWrapper);
                for (SysDept sysDept:list) {
                    if (ObjectUtil.isNotEmpty(sysDept)) {
                        iSysDeptService.deleteDeptById(sysDept.getDeptId());
                    }
                }
            }
        }*/
        return removeByIds(ids);
    }

    /**
     * 根据用户账号查询他所属的客户列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<Tenant> selectTenantByUser(String userId) {
        return this.baseMapper.selectTenantByUser(userId);
    }

    @Override
    public List<Tenant> selectTenantList(TenantBo bo) {
        if (SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            return this.baseMapper.selectList(buildQueryWrapper(bo));
        } else {
            return this.baseMapper.selectTenantByUser(SecurityUtils.getUserId()+"");
        }
    }
}
