package com.rzdata.system.service;

import com.rzdata.system.domain.TUnifyOrg;
import com.rzdata.system.domain.vo.TUnifyOrgVo;
import com.rzdata.system.domain.bo.TUnifyOrgBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 申石组织表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface ITUnifyOrgService extends IServicePlus<TUnifyOrg, TUnifyOrgVo> {
	/**
	 * 查询单个
	 * @return
	 */
	TUnifyOrgVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<TUnifyOrgVo> queryPageList(TUnifyOrgBo bo);

	/**
	 * 查询列表
	 */
	List<TUnifyOrgVo> queryList(TUnifyOrgBo bo);

	/**
	 * 根据新增业务对象插入申石组织表
	 * @param bo 申石组织表新增业务对象
	 * @return
	 */
	Boolean insertByBo(TUnifyOrgBo bo);

	/**
	 * 根据编辑业务对象修改申石组织表
	 * @param bo 申石组织表编辑业务对象
	 * @return
	 */
	Boolean updateByBo(TUnifyOrgBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 同步申石用户表
	 * @return
	 */
	Boolean sycn();
}
