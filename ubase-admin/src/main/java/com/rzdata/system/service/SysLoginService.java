package com.rzdata.system.service;

import cn.hutool.core.util.ObjectUtil;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.core.domain.model.LoginUser;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.core.service.LogininforService;
import com.rzdata.framework.core.service.TokenService;
import com.rzdata.framework.enums.ApiTypeEnum;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.exception.user.CaptchaException;
import com.rzdata.framework.exception.user.CaptchaExpireException;
import com.rzdata.framework.exception.user.UserPasswordNotMatchException;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.MessageUtils;
import com.rzdata.framework.utils.ServletUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.spring.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 登录校验方法
 *
 * <AUTHOR> Li
 */
@Service
public class SysLoginService {

    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private LogininforService asyncService;

    /**
     * 权限服务类
     */
    @Autowired
    SysPermissionService permissionService;

    /**
     * 部门服务类
     */
    @Autowired
    ISysDeptService sysDeptService;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @param tenantId 租户id
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid, String tenantId, ApiTypeEnum type) {
        HttpServletRequest request = ServletUtils.getRequest();
        boolean captchaOnOff = configService.selectCaptchaOnOff();
        // 验证码开关 只有网页端才需要验证码判断
        if (captchaOnOff && ObjectUtil.equals(ApiTypeEnum.PC,type)) {
            validateCaptcha(username, code, uuid, request);
        }
        if(!("admin".equals(username) || "dmsadmin".equals(username))) {
            // 用户组件只允许admin或者dmsadmin登录
            throw new UserPasswordNotMatchException();
        }
        // 用户验证
        Authentication authentication;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match"), request);
                throw new UserPasswordNotMatchException();
            } else {
                asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage(), request);
                throw new ServiceException(e.getMessage());
            }
        }
        asyncService.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success"), request);
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (ObjectUtil.isNotEmpty(tenantId)&&ObjectUtil.isEmpty(loginUser.getTenantId())){
            loginUser.setTenantId(tenantId);
        }
        recordLoginInfo(loginUser.getUserId(), username);
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 单点登录验证
     *
     * @param username 用户名
     * @return 结果
     */
    public String sso(String username ) {
        String result = "";
        HttpServletRequest request = ServletUtils.getRequest();
        SysUser userObj = this.userService.selectUserByUserName(username);
        if(userObj != null) {
            if("0".equals(userObj.getStatus())) {
                // 账号启用的情况下
                asyncService.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")+" "+request.getParameter("client"), request);
                recordLoginInfo(userObj.getUserId(), username);
                // 用户挂靠的组织对象
                SysDept deptObj = this.sysDeptService.getById(userObj.getDeptId());
                // 用户所属的二级部门对象
                SysDept secDeptObj = null;
                if(StringUtils.isNotEmpty(deptObj.getSecDeptId())) {
                    secDeptObj = this.sysDeptService.getById(deptObj.getSecDeptId());
                }
                LoginUser loginUser = new LoginUser(userObj.getUserId(), userObj.getDeptId(),deptObj.getDeptName(),
                        secDeptObj != null ? secDeptObj.getDeptId() : null,
                        secDeptObj != null ? secDeptObj.getDeptName() : null,
                        userObj.getUserName(),userObj.getPassword(), permissionService.getMenuPermission(userObj),
                        userObj.getDept().getDeptCode(), userObj.getNickName(),userObj.getTenantId());
                result = tokenService.createToken(loginUser);
            }
        }
        // 生成token
        return result;
    }


    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid, HttpServletRequest request) {
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = SpringUtils.getBean(RedisCache.class).getCacheObject(verifyKey);
        SpringUtils.getBean(RedisCache.class).deleteObject(verifyKey);
        if (captcha == null) {
            asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire"), request);
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            asyncService.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error"), request);
            throw new CaptchaException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(String userId, String username) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ServletUtils.getClientIP());
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(username);
        userService.updateUserProfile(sysUser);
    }
}
