package com.rzdata.system.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.process.domain.Tenant;
import com.rzdata.process.domain.bo.TenantBo;
import com.rzdata.process.domain.vo.TenantVo;

import java.util.Collection;
import java.util.List;

/**
 * 客户Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ITenantService extends IServicePlus<Tenant, TenantVo> {
	/**
	 * 查询单个
	 * @return
	 */
	TenantVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<TenantVo> queryPageList(TenantBo bo);

	/**
	 * 查询列表
	 */
	List<TenantVo> queryList(TenantBo bo);

	/**
	 * 根据新增业务对象插入客户
	 * @param bo 客户新增业务对象
	 * @return
	 */
	Boolean insertByBo(TenantBo bo);

	/**
	 * 根据编辑业务对象修改客户
	 * @param bo 客户编辑业务对象
	 * @return
	 */
	Boolean updateByBo(TenantBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 根据用户账号查询他所属的客户列表
	 * @param userId
	 * @return
	 */
	List<Tenant> selectTenantByUser(String userId);

	/**
	 * 获取租户信息 根据登录用户
	 * @param bo
	 * @return
	 */
	List<Tenant> selectTenantList(TenantBo bo);

}
