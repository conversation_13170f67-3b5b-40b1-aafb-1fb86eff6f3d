package com.rzdata.system.service;

import com.rzdata.system.domain.UnifyUser;
import com.rzdata.system.domain.vo.UnifyUserVo;
import com.rzdata.system.domain.bo.UnifyUserBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 申石用户表Service接口
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IUnifyUserService extends IServicePlus<UnifyUser, UnifyUserVo> {
	/**
	 * 查询单个
	 * @return
	 */
	UnifyUserVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<UnifyUserVo> queryPageList(UnifyUserBo bo);

	/**
	 * 查询列表
	 */
	List<UnifyUserVo> queryList(UnifyUserBo bo);

	/**
	 * 根据新增业务对象插入申石用户表
	 * @param bo 申石用户表新增业务对象
	 * @return
	 */
	Boolean insertByBo(UnifyUserBo bo);

	/**
	 * 同步申石用户表
	 * @return
	 */
	Boolean sycn();

	/**
	 * 根据编辑业务对象修改申石用户表
	 * @param bo 申石用户表编辑业务对象
	 * @return
	 */
	Boolean updateByBo(UnifyUserBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
