package com.rzdata.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.blueland.bpmclient.BpmClient;
import com.blueland.bpmclient.model.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.rzdata.config.CustomConfig;
import com.rzdata.config.ProcessConfig;
import com.rzdata.eventbus.ProcessEventBus;
import com.rzdata.eventbus.ProcessResultEvent;
import com.rzdata.framework.constant.ProcessStatusConstants;
import com.rzdata.framework.core.service.UserService;
import com.rzdata.framework.enums.ApiTypeEnum;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.framework.utils.UrlUtils;
import com.rzdata.process.domain.DocWorkflowLogItem;
import com.rzdata.process.domain.WorkFlowInfo;
import com.rzdata.process.domain.bo.BpmClientInputModelBo;
import com.rzdata.process.domain.bo.ProcessBo;
import com.rzdata.process.domain.bo.ProcessWorkFlowBo;
import com.rzdata.process.domain.bo.WorkflowLogBo;
import com.rzdata.process.domain.vo.WorkFlowPageVo;
import com.rzdata.process.domain.vo.WorkflowLogVo;
import com.rzdata.process.enums.ApplyStatusEnum;
import com.rzdata.process.mapper.*;
import com.rzdata.process.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

import static com.rzdata.framework.constant.Constants.PROCESS_KEY;

@Slf4j
@Service("workflowService")
public class WorkflowService {
    private static int FLOW_STATUS_ERROR = 0;// 异常
    private static int FLOW_STATUS_UNREAD = 1;// 待办
    private static int FLOW_STATUS_READ = 2;// 已办
    private static int FLOW_STATUS_FINISH = 3;// 办结
    private static String END_ACT_NAME = "结束";
    /**
     * 是否从通讯录选择下一步人员
     */
    private static String SHOW_TXL_SELECT = "show_txl_select";
    @Autowired
    private CustomConfig customConfig;

//	@Autowired
//	private ProcessConfig processConfig;

    @Autowired
    private UserService userService;

    @Autowired
    ISysConfigService configService;

    @Autowired
    IWorkflowLogService iWorkflowLogService;

    @Autowired
    IDocMessageService iDocMessageService;

    @Autowired
    ReviewApplyMapper reviewApplyMapper;

    @Autowired
    ExtraApplyMapper extraApplyMapper;

    @Autowired
    DocChangeApplyMapper changeApplyMapper;

    @Autowired
    ModifyApplyMapper modifyApplyMapper;

    @Autowired
    BorrowApplyMapper borrowApplyMapper;

    @Autowired
    ReissueApplyMapper reissueApplyMapper;

    @Autowired
    IWorkflowApplyLogService workflowApplyLogService;

    @Autowired
    IDocMessageService docMessageService;

    @Autowired
    private IDocWorkflowLogItemService docWorkflowLogItemService;

    @Autowired
    private IReviewApplyItemService applyItemService;

    private final static String CUR_ACT_DEF_KEY = "curActDef";
    private final static String CUR_ACT_INST_KEY = "curActInst";
    private final static String PROC_INST_KEY = "procInst";
    private final static String PROC_DEF_KEY = "procDef";
    private final static String ACT_OPPOWERS = "actOppowers";// 下一环节显示按钮

    public enum ENUM_ACTION {
        /**
         * // 暂存
         */
        save,
        /**
         * // 提交
         */
        submit,
        /**
         * // 作废
         */
        cancel,
        /**
         * // 转派
         */
        transfer,
        /**
         * // 退回拟稿人
         */
        backtostart,
        /**
         * // 退回上一步
         */
        backtoprev,
        /**
         * // 撤回
         */
        back,
        /**
         * // 驳回
         */
        reject,
    }

    ;


    BpmClient getBpmClient() {
        WorkflowConfig workflowConfig = new WorkflowConfig();
        workflowConfig.setTenantId(customConfig.getBpmTenantId());
        workflowConfig.setBaseURL(customConfig.getWorkflowServiceUrl());
        BpmClient bpmClient = new BpmClient(workflowConfig);
        return bpmClient;
    }

    /**
     * 开启并提交流程
     *
     * @param title      标题
     * @param procDefKey 流程key
     * @param userId     用户id
     * @param deptId     用户部门Id
     * @param businessId 业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel startAndSubmitProcess(String title, String procDefKey, String userId, String deptId, String businessId) throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        // 1、根据key 获取流程信息
        ProcessDefinitionModel key = getProcessDefinitionModel(procDefKey);
        if(ObjectUtil.isNotEmpty(key)) {
            //2、 获取第一个环节的定义
            ActivityDefinitionModel activity = getStartactDef(key.getProcDefId());
            // 获取下一环节流程定义
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setProcDefId(key.getProcDefId());
            searchQuery.setCurActDefId(activity.getActDefId());
            List<ActivityDefinitionModel> nextList =  getNextActs(searchQuery);
            if (ObjectUtil.isNotEmpty(nextList)) {
                // 获取下一环节人员定义
                SearchQuery searchQuery1 = new SearchQuery();
//                String userId = SecurityUtils.getLoginUser().getUsername();
                searchQuery1.setUserId(userId);
                searchQuery1.setUserOrgId(deptId);
                searchQuery1.setCurActDefId(activity.getActDefId());
                searchQuery1.setDestActDefId(nextList.get(0).getActDefId());
                searchQuery1.setProcDefId(key.getProcDefId());
                List<ActivityResourceModel> userList =  getNextActUsers(searchQuery1);
                userList = userList.stream().filter(x -> x.getType().equals("USER")).collect(Collectors.toList());
                // 提交流程
                BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
                bpmClientInputModel.setWf_procTitle(title);
                bpmClientInputModel.setWf_procDefKey(procDefKey);
                bpmClientInputModel.setWf_sendUserId(userId);
                bpmClientInputModel.setWf_sendUserOrgId(deptId);
                bpmClientInputModel.setWf_businessKey(businessId);
                String url = processConfig.getUniteworkUrl() + "?businessKey=" + businessId;
                bpmClientInputModel.setWf_uniteworkUrl(url);
                bpmClientInputModel.setWf_nextActDefId(nextList.get(0).getProcDefId());
                bpmClientInputModel.setWf_nextActDefName(nextList.get(0).getActDefName());
                bpmClientInputModel.setWf_procDefId(key.getProcDefId());
                List<Map<String, Object>> usersList = new ArrayList<>();
                if (ObjectUtil.isNotEmpty(userList)) {
//                    userList = CollectionUtil.page(1, 10, userList);
                    for (ActivityResourceModel user : userList) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("receiveUserId", user.getId());
//                        map.put("receiveUserOrgId", user.getParentId());
                        usersList.add(map);
                    }

//                    bpmClientInputModel.setWf_receiver(userList.get(0).getId());
                } else {
                    throw new ServerException("请配置环节处理人");
                }
                bpmClientInputModel.setWf_receivers(usersList);
                bpmClientInputModel.setWf_sendUserCompanyId(deptId);
                ProcessInstanceModel processInst = new ProcessInstanceModel();
                processInst.setProcInstId(activity.getProcDefId());
//                processInst.setTopProcInstId(activity.getProcDefId());
                bpmClientInputModel.setWf_curActDefName(activity.getActDefName());
                saveProcLog(bpmClientInputModel, processInst, ENUM_ACTION.save.name(), userId, "");
                bpmClientInputModel.setWf_curActDefName(null);
                if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                    bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                }
                BpmClient bpmClient = getBpmClient();
                processInst = bpmClient.submitFlowInstance(bpmClientInputModel);
                return processInst;
//                        processProcInst(bpmClientInputModel, ENUM_ACTION.submit.name());
            } else {
                throw new ServerException("查询下一流程环节出错");
            }
        } else {
            throw new ServerException("查询流程出错");
        }
    }


    /**
     * 开启流程
     *
     * @param title      标题
     * @param procDefKey 流程key
     * @param userId     用户id
     * @param deptId     用户部门Id
     * @param businessId 业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel startProcess(String title, String procDefKey, String userId, String deptId, String businessId) throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        BpmClientInputModelBo bpmClientInputModelBo = new BpmClientInputModelBo();
        BpmClientInputModel bpmClientInputModel = new BpmClientInputModel();
        bpmClientInputModel.setWf_procTitle(title);
        bpmClientInputModel.setWf_procDefKey(procDefKey);
        bpmClientInputModel.setWf_sendUserId(userId);
        bpmClientInputModel.setWf_sendUserOrgId(deptId);
        bpmClientInputModel.setWf_businessKey(businessId);
        bpmClientInputModel.setWf_uniteworkUrl(processConfig.getUniteworkUrl());
        bpmClientInputModelBo.setModel(bpmClientInputModel);
        bpmClientInputModelBo.setApplyStatus(ApplyStatusEnum.DRAFT.getCode());
        ProcessInstanceModel processInst = processProcInst(bpmClientInputModelBo, ENUM_ACTION.save.name());
        return processInst;
    }

    /**
     * 下一步流程
     *
     * @param bpmClientInputModelBo
     * @param businessId          业务id
     * @return
     * @throws Exception
     */
    public ProcessInstanceModel nextExecute(BpmClientInputModelBo bpmClientInputModelBo, String businessId) throws Exception {
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        bpmClientInputModelBo.getModel().setWf_businessKey(businessId);
        bpmClientInputModelBo.getModel().setWf_uniteworkUrl(processConfig.getUniteworkUrl());
        return processProcInst(bpmClientInputModelBo, ENUM_ACTION.submit.name());
    }


    /**
     * 保存流程日志
     * @param bpmClientInputModel
     * @param processInst
     * @param action
     */
    private void saveProcLog(BpmClientInputModel bpmClientInputModel, ProcessInstanceModel processInst,
                             String action, String userName, String applyStatus) {
        WorkflowLogBo bo = new WorkflowLogBo();
        bo.setProcInstId(processInst.getProcInstId());
        bo.setBusinessId(bpmClientInputModel.getWf_businessKey());
        bo.setActDefName(ObjectUtil.isNotEmpty(bpmClientInputModel.getWf_curActDefName()) ?bpmClientInputModel.getWf_curActDefName() : bpmClientInputModel.getWf_nextActDefName());
        if (ObjectUtil.isEmpty(bo.getActDefName())){
            if (ObjectUtil.isNotEmpty(processInst.getNextActInsts())) {
                bo.setActDefName(processInst.getNextActInsts().get(0).getPrevActDefName());
            }
        }
        bo.setActInstId(bpmClientInputModel.getWf_curActInstId());
        if (ObjectUtil.isEmpty(bo.getActInstId())){
            if (ObjectUtil.isNotEmpty(processInst.getNextActInsts())) {
                bo.setActInstId(processInst.getNextActInsts().get(0).getProcInstId());
            }
        }
        bo.setYNApprove(ENUM_ACTION.submit.name().equals(action) || ENUM_ACTION.save.name().equals(action) ? "1" : "0");
        bo.setOpinion(bpmClientInputModel.getWf_curComment());
        bo.setSender(userName);
        bo.setReceiver(JSONUtil.toJsonStr(bpmClientInputModel.getWf_receivers()));
        bo.setActStatus(action);
        bo.setProcDefKey(bpmClientInputModel.getWf_procDefKey());
        if (ObjectUtil.isNotEmpty(bo.getActDefName())) {
           if (bo.getActDefName().contains("审核")) {
               bo.setActName("部门审核意见");
           } else if (bo.getActDefName().contains("部门会审")) {
               bo.setActName("相关部门审核");
           } else if (bo.getActDefName().contains("批准")) {
               bo.setActName("领导批准结论");
           } else if (bo.getActDefName().contains("培训")) {
               bo.setActName("培训说明");
           } else if (bo.getActDefName().contains("归档")) {
               bo.setActName("生效说明");
           } else if (bo.getActDefName().contains("驳回")) {
               bo.setActName("驳回结论");
           }
        }
        iWorkflowLogService.insertByBo(bo);

        //设置接收人和接收组织
        List<Map<String,Object>> receivers = (List<Map<String,Object>>)JSONUtil.parse(bpmClientInputModel.getWf_receivers());
        if(receivers!=null && receivers.size() > 0) {
            DocWorkflowLogItem docWorkflowLogItem;
            for (Map<String, Object> receiver : receivers) {
                docWorkflowLogItem = new DocWorkflowLogItem();
                docWorkflowLogItem.setWorkflowLogId(bo.getId());
                docWorkflowLogItem.setReceiverMan((String)receiver.get("receiveUserId"));
                docWorkflowLogItem.setReceiverOrg((String)receiver.get("receiveUserOrgId"));

                this.docWorkflowLogItemService.save(docWorkflowLogItem);
            }
        }

        // 判断环节是否为结束
        if(ObjectUtil.isNotEmpty(bpmClientInputModel.getWf_curActDefType()) && "endEvent".equals(bpmClientInputModel.getWf_curActDefType())) {
            WorkflowLogBo bo2 = new WorkflowLogBo();
            bo2.setId(null);
            bo2.setBusinessId(bpmClientInputModel.getWf_businessKey());
            bo2.setProcInstId(processInst.getProcInstId());
            bo2.setActDefName("结束");
            bo2.setActStatus("end");
            bo2.setProcDefKey(bpmClientInputModel.getWf_procDefKey());
            bo2.setCreateTime(new Date());
            iWorkflowLogService.insertByBo(bo2);
            //insertMessage(bpmClientInputModel);
        }
    }

    /**
     * 处理流程实例
     *
     * @param action 处理操作类型
     * @return
     */
    public ProcessInstanceModel processProcInst(BpmClientInputModelBo bpmClientInputModelBo, String action)
            throws Exception {
        ProcessInstanceModel processInst = new ProcessInstanceModel();
        BpmClientInputModel bpmClientInputModel = bpmClientInputModelBo.getModel();
        String url = bpmClientInputModel.getWf_uniteworkUrl() + "?businessKey=" + bpmClientInputModel.getWf_businessKey();
        bpmClientInputModel.setWf_uniteworkUrl(url);
        BpmClient bpmClient = getBpmClient();
        String status = ProcessStatusConstants.TO_DO;
            switch (ENUM_ACTION.valueOf(action)) {
                case cancel:// 作废
                    processInst = bpmClient.cancelProcInst(bpmClientInputModel);
                    status = ProcessStatusConstants.TO_DONE;
                    try {
                        iDocMessageService.insertMessageEndArchiving(bpmClientInputModel.getWf_businessKey());
                    } catch (ServerException exception) {
                        exception.printStackTrace();
                    }
                    //status = ProcessStatusConstants.CLOSED+"";
                    break;
                case backtostart:// 返回拟稿人
                    processInst = bpmClient.callBackStart(bpmClientInputModel);
                    break;
                case backtoprev:// 返回上一步
                    processInst = bpmClient.backToPrev(bpmClientInputModel);
                    break;
                case back:// 撤回
                    processInst = bpmClient.callBack(bpmClientInputModel);
                    break;
                case transfer:// 转派
                    processInst = bpmClient.transfer(bpmClientInputModel);
                    break;
                case reject:// 驳回
                    processInst = bpmClient.rejectFlow(bpmClientInputModel);
                    break;
                case save:
                    if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                        bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                    }
                    if(StringUtils.isNotBlank(bpmClientInputModelBo.getApplyStatus())) {
                        status = ProcessStatusConstants.TO_DRAFT;
                    }
                    processInst = bpmClient.saveFlowInstance(bpmClientInputModel);
                    break;
                case submit:
                    boolean close = ObjectUtil.isNotEmpty(bpmClientInputModel.getWf_curActDefType()) && "endEvent".equals(bpmClientInputModel.getWf_curActDefType());
                    if (StrUtil.isBlank(bpmClientInputModel.getWf_businessKey())) {
                        bpmClientInputModel.setWf_businessKey(IdUtil.simpleUUID());
                    }
                    if (bpmClientInputModel.getWf_procTitle() == null) {
                        bpmClientInputModel.setWf_procTitle("结束");
                    }
                    processInst = bpmClient.submitFlowInstance(bpmClientInputModel);
                    // 判断环节是否为结束
                    if(close) {
                        status = ProcessStatusConstants.TO_DONE;

                    }
                    break;
                default:
                    break;
            }

            saveProcLog(bpmClientInputModel, processInst, action, SecurityUtils.getUsername(), bpmClientInputModelBo.getApplyStatus());
            //流程事件
            //流程处理完后，会调用此注解（@AllowConcurrentEvents）相关方法
            ProcessEventBus.post(new ProcessResultEvent(bpmClientInputModel.getWf_businessKey(), status, bpmClientInputModel,"",bpmClientInputModelBo.getApplyStatus(),bpmClientInputModelBo.getSetupTime()));

        return processInst;
    }

    /**
     * 获取流程实例完整信息，包含流程实例、当前环节实例、流程定义等信息
     *
     * @return
     */
    public HashMap<String, Object> getFLowInfo(SearchQuery searchQuery) throws Exception {
        // TODO Auto-generated method stub
        HashMap<String, Object> returnDataMap = Maps.newHashMap();
        BpmClient bpmClient = getBpmClient();
        JSONObject jo = new JSONObject();
        try {

            jo = getFlowInfo(searchQuery);
            if (jo == null) {
                return null;
            }
            if (!jo.containsKey(CUR_ACT_DEF_KEY) || !jo.containsKey(CUR_ACT_INST_KEY) || !jo.containsKey(PROC_INST_KEY)
                    || !jo.containsKey(PROC_DEF_KEY)) {
                throw new Exception("返回的数据不完整");
            }
            returnDataMap.put(CUR_ACT_DEF_KEY, bpmClient.parserToMap(jo.getJSONObject(CUR_ACT_DEF_KEY)));
            returnDataMap.put(CUR_ACT_INST_KEY, bpmClient.parserToMap(jo.getJSONObject(CUR_ACT_INST_KEY)));
            returnDataMap.put(PROC_INST_KEY, bpmClient.parserToMap(jo.getJSONObject(PROC_INST_KEY)));
            returnDataMap.put(PROC_DEF_KEY, bpmClient.parserToMap(jo.getJSONObject(PROC_DEF_KEY)));
            returnDataMap.put(ACT_OPPOWERS, jo.getJSONArray(ACT_OPPOWERS));
            /**
             * JSONArray ja_log = jo.getJSONArray("procLogs"); ArrayList<HashMap<String,
             * Object>> list_log = Lists.newArrayList(); for (int i = 0; i < ja_log.size();
             * i++) { HashMap<String, Object> commentMap = Maps.newHashMap();
             * commentMap.put("actDefName",
             * ja_log.getJSONObject(i).getString("prevActDefName"));
             * commentMap.put("commentTime", ja_log.getJSONObject(i).get("startTime"));
             * commentMap.put("actDefId", ja_log.getJSONObject(i).get("prevActDefId"));
             * String orgId = ja_log.getJSONObject(i).getString("sendOrgId"); PubOrgEntity
             * deptEntry = pubOrgDao.get(orgId); String userName =
             * String.format("(%s-%s)%s", deptEntry.getCompanyName(),
             * deptEntry.getOrgName(), ja_log.getJSONObject(i).getString("sendUserName"));
             * commentMap.put("commentUserName", userName); commentMap.put("userName",
             * ja_log.getJSONObject(i).getString("sendUserName"));
             * commentMap.put("commentFullMessage",
             * ja_log.getJSONObject(i).get("commentFullMessage")); list_log.add(commentMap);
             * } returnDataMap.put("procLogs", list_log);
             */
            // returnDataMap.put("transferLog",
            // projectCommentEntryMapper.findCommentListByParams(params));//获取流程实例转派审批日志
        } catch (Exception ex) {
            log.error("getFLowInfo==============" + JSON.toJSONString(searchQuery), ex);
        }
        return returnDataMap;
    }

    JSONObject getFlowInfo(SearchQuery searchQuery) {
        String returnData = "";
        BpmClient bpmClient = getBpmClient();
        try {
            switch (searchQuery.getStatus()) {
                case 1:
                    returnData = bpmClient.getToDoFlowInfo(searchQuery);
                    break;
                case 2:
                    returnData = bpmClient.getYibanFlowInfo(searchQuery);
                    break;
                case 3:
                    returnData = bpmClient.getBanJieFlowInfo(searchQuery);
                    break;
                default:
                    break;
            }
            return JSON.parseObject(returnData);
        } catch (Exception ex) {
            log.error("=======流程平台返回信息:" + JSON.toJSONString(searchQuery), ex);
        }
        return null;
    }

    /**
     * 获取流程实例精简信息
     *
     * @return
     */
    public HashMap<String, String> getSimpleFLowInfo(SearchQuery searchQuery) {
        HashMap<String, String> returnDataMap = Maps.newHashMap();
        try {

            JSONObject jo = getFlowInfo(searchQuery);
            returnDataMap.put("curActDefName", jo.getJSONObject("curActInst").getString("actDefName"));
            returnDataMap.put("curActDefId", jo.getJSONObject("curActInst").getString("actDefId"));
            returnDataMap.put("curActInstId", jo.getJSONObject("curActInst").getString("actInstId"));
            returnDataMap.put("procInstId", jo.getJSONObject("curActInst").getString("procInstId"));
            returnDataMap.put("businessKey", jo.getJSONObject("procInst").getString("businessKey"));
        } catch (Exception ex) {
            log.error("getFLowInfo==============", ex);
        }
        return returnDataMap;
    }

    /**
     * 获取流程实例日志
     *
     * @param bpmClientInputModel
     * @return
     */
    public HashMap<String, Object> getProcInstLog(BpmClientInputModel bpmClientInputModel) {
        HashMap<String, Object> returnDataMap = Maps.newHashMap();
        List<Map<String, Object>> procLog;
        BpmClient bpmClient = getBpmClient();
        try {
            if (StrUtil.isNotBlank(bpmClientInputModel.getWf_procInstId())) {
                String returnData = bpmClient.getProcInstLog(bpmClientInputModel.getWf_procInstId());
                Map<String, Object> procInstLog = JSONObject.parseObject(returnData, Map.class);
                if (null != procInstLog && procInstLog.containsKey("procLogs")) {
                    // 获取日志实例
                    procLog = (List<Map<String, Object>>) procInstLog.get("procLogs");
                    procLog = dealEchoMap(procLog);
                    JSONObject jo = JSON.parseObject(returnData);
                    returnDataMap.put("procInst", bpmClient.parserToMap(jo.getJSONObject("procInst")));
                    returnDataMap.put("procLogs", procLog);
                }
            }
        } catch (Exception ex) {
            log.error("getProcInstLog==============" + JSON.toJSONString(bpmClientInputModel), ex);
        }
        return returnDataMap;
    }

    /**
     * 去掉相关部门重复数据
     *
     * @param procLog
     * @return
     */
    private List<Map<String, Object>> dealEchoMap(List<Map<String, Object>> procLog) {
        List<Map<String, Object>> newList = new ArrayList<Map<String, Object>>();
        // 去重
        for (int i = 0; i < procLog.size(); i++) {
            Map<String, Object> oldMap = procLog.get(i);
            if (newList.size() > 0) {
                boolean isContain = false;
                for (int j = 0; j < newList.size(); j++) {
                    Map<String, Object> newMap = newList.get(j);
                    if (newMap.get("prevActInstId").equals(oldMap.get("prevActInstId"))) {
                        for (String k : oldMap.keySet()) {
                            newMap.put(k, oldMap.get(k));
                        }
                        isContain = true;
                        break;
                    }
                }

                if (!isContain) {
                    newList.add(oldMap);
                }

            } else {
                newList.add(oldMap);
            }
        }
        return newList;

    }

    /**
     * 查询待办列表
     *
     * @param searchQuery
     * @return
     */
    public PageResultModel findRecordList(SearchQuery searchQuery) {
        // TODO Auto-generated method stub
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getRecordList(searchQuery);
        } catch (Exception ex) {
            log.error("findRecordList===" + JSON.toJSONString(searchQuery), ex);
        }
        return null;
    }

    /**
     * 查询待办列表
     *
     * @param processBo
     * @param type 类型 1= PC端 2=移动端
     * @return
     */
    public WorkFlowPageVo findToDoList(ProcessBo processBo, ApiTypeEnum type) {
        JSONObject jsonObject = JSONObject.parseObject(processBo.toJSONString());
        BpmClient bpmClient = getBpmClient();
        SearchQuery searchQuery = new SearchQuery();
        WorkFlowPageVo pageVo = new WorkFlowPageVo();
        try {
            searchQuery.setRecUserId(SecurityUtils.getUsername());
            searchQuery.setStatus(processBo.getStatus());
            searchQuery.setPageNumber(processBo.getPageNumber());
            searchQuery.setPageSize(processBo.getPageSize());
            //操作类型
            searchQuery.setProcDefKey(processBo.getProcDefKey());

            PageResultModel pageResultModel = bpmClient.getRecordList(searchQuery);
            if (ObjectUtil.isNotEmpty(pageResultModel) && pageResultModel.getTotalCount() > 0) {
                // 获取详情信息
                List<WorkFlowInfo> workFlowInfos = JSONUtil.toList(JSONUtil.toJsonStr(pageResultModel.getResult()), WorkFlowInfo.class);
                List<WorkFlowInfo> infos = handleBusinessData(workFlowInfos, processBo);

                if (processBo.getHaveData() > 0) {
                    pageVo.setPageSize(pageResultModel.getPageSize());
                    pageVo.setPageNumber(pageResultModel.getThisPageNumber());
                    pageVo.setTotalCount(StringUtils.isBlank(jsonObject.getString("docName")) ? pageResultModel.getTotalCount() : infos.size());
                    // 获取详情信息
                    switch (processBo.getStatus()) {
                        case 1:
                            pageVo.setResult(infos.stream().sorted(Comparator.comparing(WorkFlowInfo::getSendTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()));
                            break;
                        case 2:
                        case 3:
                            pageVo.setResult(infos.stream().sorted(Comparator.comparing(WorkFlowInfo::getDealTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed()).collect(Collectors.toList()));
                            break;
                        default:
                            break;
                    }
                } else {
                    pageVo.setPageSize(processBo.getPageSize());
                    pageVo.setPageNumber(0);
                    pageVo.setTotalCount(0);
                    pageResultModel.setTotalCount(0);
                    pageVo.setResult(new ArrayList<>());
                }
            } else {
                pageVo.setPageSize(processBo.getPageSize());
                pageVo.setPageNumber(processBo.getPageNumber());
                pageVo.setTotalCount(0);
            }
            //如果是移动端请求且查询待办 过滤掉培训待办(因为培训待办需要上传文件,移动端无法上传文件)
            if (ObjectUtil.equal(ApiTypeEnum.MOBILE,type)&&processBo.getStatus()==1){
                List<WorkFlowInfo> result = pageVo.getResult();
                int afterTotal = result.size();
                result = result.stream().filter(workFlowInfo -> !ObjectUtil.equal("peixun",workFlowInfo.getCurActDefId())).collect(Collectors.toList());
                int beforeTotal = result.size();
                pageVo.setResult(result);
                pageVo.setTotalCount(processBo.getHaveData()-(afterTotal-beforeTotal));
            }
            return pageVo;
        } catch (Exception ex) {
            log.error("findRecordList===" + JSON.toJSONString(searchQuery), ex);
        }
        return null;
    }

    /**
     * 查询待办列表,重新实现的版本
     *
     * @param processBo
     * @param type 类型 1= PC端 2=移动端
     * @return
     */
    public WorkFlowPageVo findToDoList2(ProcessWorkFlowBo processBo, ApiTypeEnum type) throws Exception{
        WorkFlowPageVo resultPageVo = new WorkFlowPageVo();

        Page<WorkflowLogVo> page = new Page<>(processBo.getPageNumber(), processBo.getPageSize());
        page = this.iWorkflowLogService.queryToDoList(page,processBo);

        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        List<Map<String, Object>> bussList = new ArrayList<>();

        if(page!=null && page.getRecords()!=null&&page.getRecords().size() > 0) {
            //首先遍历查询出所有的业务数据
            for (WorkflowLogVo workflowLog : page.getRecords()) {
                String key = workflowLog.getProcDefKey();
                String businessId = workflowLog.getBusinessId();
                List<String> businessIds = Arrays.asList(businessId);

                if (Objects.equals(key, processConfig.getProcDefKeyFSSQ())) {
                    // 文件复审申请
                    bussList.addAll(reviewApplyMapper.selectReviewApply4New(processBo, businessIds));
                } else if (Objects.equals(key, processConfig.getProcDefKeyZFSQ())) {
                    // 文件增发申请
                    bussList.addAll(extraApplyMapper.selectExtraApplyList4New(processBo, businessIds));
                } else if (Objects.equals(key, processConfig.getProcDefKeyBGSQ())) {
                    // 文件变更申请
                    bussList.addAll(changeApplyMapper.selectChangeApplyList4New(processBo, businessIds));
                } else if (Objects.equals(key, processConfig.getProcDefKeyDISUSE()) || Objects.equals(key, processConfig.getProcDefKeyUPDATE()) || Objects.equals(key, processConfig.getProcDefKeyADD())) {
                    // 文件增修废申请
                    bussList.addAll(modifyApplyMapper.selectModifyApplyList4New(processBo, businessIds));
                } else if (Objects.equals(key, processConfig.getProcDefKeyJYSQ())) {
                    // 文件借阅申请
                    bussList.addAll(borrowApplyMapper.selectBorrowApplyList4New(processBo, businessIds));
                } else if (Objects.equals(key, processConfig.getProcDefKeyBFSQ())) {
                    // 文件补发申请
                    bussList.addAll(reissueApplyMapper.selectReissueApplyList4New(processBo, businessIds));
                }
            }

            BpmClient bpmClient = getBpmClient();
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setRecUserId(SecurityUtils.getUsername());
            searchQuery.setStatus(1);

            if (ObjectUtil.isNotEmpty(bussList) && bussList.size() > 0) {
                Map<String, Object> map = bussList.stream().collect(Collectors.toMap(x -> x.get("id").toString().toUpperCase(), x -> x, (next, last) -> next));

                List<WorkFlowInfo> workFlowInfoList = new ArrayList<>();
                WorkFlowInfo workFlowInfo;
                //再次遍历赋值业务数据逻辑
                for (WorkflowLogVo workflowLog : page.getRecords()) {
                    if(map.get(workflowLog.getBusinessId())!=null) {
                        workFlowInfo = new WorkFlowInfo();
                        Map<String,String> mapValue  = (Map<String,String>)map.get(workflowLog.getBusinessId());

                        workFlowInfo.setBusinessData(mapValue);

                       // searchQuery.setProcInstId(workflowLog.getProcDefKey());
                        searchQuery.setActInstId(workflowLog.getActInstId());
                        //流程定义名称
                        PageResultModel pageResultModel = bpmClient.getRecordList(searchQuery);
                        List<Map<String, Object>> workFlowResult = pageResultModel.getResult();

                        // workFlowInfo.setProcDefName(recordbyPorcInstId);

                        workFlowInfoList.add(workFlowInfo);
                    }
                }

                resultPageVo.setResult(workFlowInfoList);
            }
        }

        resultPageVo.setPageSize(processBo.getPageSize());
        resultPageVo.setPageNumber(processBo.getPageNumber());
        resultPageVo.setTotalCount(new Long(page.getTotal()).intValue());
        return resultPageVo;
    }

    /**
     * 根据procDefKey 处理业务数据
     * @param workFlowInfos
     */
    private List<WorkFlowInfo> handleBusinessData(List<WorkFlowInfo> workFlowInfos, ProcessBo bo) {
        // 获取流程实例idProcessBo
//        List<String> procInstIds = workFlowInfos.stream().map(x -> x.getProcInstId()).collect(Collectors.toList());
//        List<String> businessIds = new ArrayList<>();
//        for (WorkFlowInfo workFlowInfo : workFlowInfos) {
//            businessIds.add(UrlUtils.getParam(workFlowInfo.getUrl(), "businesskey"));
//        }
        // 获取流程定义key
//        List<String> procDefKey  = workFlowInfos.stream().map(x -> x.getProcDefKey()).collect(Collectors.toList());
        ProcessConfig processConfig = JSONUtil.toBean(configService.selectConfigByKey(PROCESS_KEY), ProcessConfig.class);
        List<Map<String, Object>> bussList = new ArrayList<>();

        List<String> reveiwIds = new ArrayList<>();
        List<String> extraIds = new ArrayList<>();
        List<String> changeIds = new ArrayList<>();
        List<String> modifyIds = new ArrayList<>();
        List<String> borrowIds = new ArrayList<>();
        List<String> reissueIds = new ArrayList<>();
        for (WorkFlowInfo workFlowInfo : workFlowInfos) {
//            List<String> businessIds = Collections.singletonList(UrlUtils.getParam(workFlowInfo.getUrl(), "businesskey"));
            String businessIds = UrlUtils.getParam(workFlowInfo.getUrl(), "businesskey");
            String key = workFlowInfo.getProcDefKey();
            if (Objects.equals(key, processConfig.getProcDefKeyFSSQ())) {
                // 文件复审申请
//                List<Map<String, Object>> list = reviewApplyMapper.selectReviewApply(bo, businessIds);
//                bussList.addAll(list);
                reveiwIds.add(businessIds);
            } else if (Objects.equals(key, processConfig.getProcDefKeyZFSQ())){
                extraIds.add(businessIds);
                // 文件增发申请
//                bussList.addAll(extraApplyMapper.selectExtraApplyList(bo, businessIds));
            } else if (Objects.equals(key, processConfig.getProcDefKeyBGSQ())){
                changeIds.add(businessIds);
                // 文件变更申请
//                bussList.addAll(changeApplyMapper.selectChangeApplyList(bo, businessIds));
            } else if (StringUtils.contains(key, processConfig.getProcDefKeyDISUSE()) || StringUtils.contains(key, processConfig.getProcDefKeyUPDATE()) || StringUtils.contains(key, processConfig.getProcDefKeyADD())){
                // 文件增修废申请
                modifyIds.add(businessIds);
//                bussList.addAll(modifyApplyMapper.selectModifyApplyList(bo, businessIds));
            } else if (Objects.equals(key, processConfig.getProcDefKeyJYSQ())){
                borrowIds.add(businessIds);
                // 文件借阅申请
//                bussList.addAll(borrowApplyMapper.selectBorrowApplyList(bo, businessIds));
            } else if (Objects.equals(key, processConfig.getProcDefKeyBFSQ())){
                reissueIds.add(businessIds);
                // 文件补发申请
//                bussList.addAll(reissueApplyMapper.selectReissueApplyList(bo, businessIds));
            }
        }
        List<WorkFlowInfo> newList = new ArrayList<>();
        return newList;
    }

    /**
     * 获取下一环节定义
     *
     * @param searchQuery
     * @return
     */
    public List<ActivityDefinitionModel> getNextActs(SearchQuery searchQuery) {
        BpmClient bpmClient = getBpmClient();
        List<ActivityDefinitionModel> actDefList = Lists.newArrayList();
        try {
            List<ActivityDefinitionModel> nextacts = bpmClient.getNextacts(searchQuery);

            for (ActivityDefinitionModel activityDefinitionModel : nextacts) {
                activityDefinitionModel.setShowTxlSelect("N");
                // 流程平台如果有输出线名称 返回当前环节名称自动为输出线名称 由于其他业务系统已经使用 该处做特殊处理 （不读输出线名称）
                /*
                 * if (StrUtil.isNotBlank(activityDefinitionModel.getDescription())) {
                 * // 描述名称 为流程平台返回的当前环节名称
                 * activityDefinitionModel.setActDefName(activityDefinitionModel.getDescription(
                 * ));
                 * }
                 */
                if (END_ACT_NAME.equalsIgnoreCase(activityDefinitionModel.getActDefName())) {
                    actDefList.add(activityDefinitionModel);
                    continue;
                }
                List<ExtAttributeModel> extAttrlist = bpmClient.getActDefExtAttrs(searchQuery.getProcDefId(),
                        activityDefinitionModel.getActDefId());
                for (ExtAttributeModel extMap : extAttrlist) {
                    // 根据流程环节自定义标签判断是否显示“通讯录选择人员”按钮
                    if (SHOW_TXL_SELECT.equalsIgnoreCase(extMap.getObjKey().trim())) {
                        activityDefinitionModel.setShowTxlSelect(extMap.getObjValue());
                        break;
                    }
                }
                actDefList.add(activityDefinitionModel);
            }

        } catch (Exception e) {
            log.error("获取下一步环节定义列表失败,参数:" + JSON.toJSONString(searchQuery), e);
        }

        return actDefList;
    }

    /**
     * 获取下一环节用户信息
     *
     * @param searchQuery
     * @return
     */
    public List<ActivityResourceModel> getNextActUsers(SearchQuery searchQuery) {
        BpmClient bpmClient = getBpmClient();
        List<ActivityResourceModel> nextactUsers = Lists.newArrayList();
        try {
            nextactUsers = bpmClient.getNextactUsers(searchQuery);
        } catch (Exception e) {
            log.error("获取下一步环节用户列表失败,参数:" + JSON.toJSONString(searchQuery), e);
        }
        return nextactUsers;
    }


    public List<ExtAttributeModel> getExtAttributeModel(String procDefId, String actDefId) {
        BpmClient bpmClient = getBpmClient();
        List<ExtAttributeModel> list = Lists.newArrayList();
        try {
            list = bpmClient.getActDefExtAttrs(procDefId, actDefId);
        } catch (Exception e) {
            log.error("获取扩展属性失败", e);
        }
        return list;
    }

    /**
     * 判断下一环节是否支持自动提交
     *
     * @param searchQuery
     * @return
     */
    public String checkAutoCommit(SearchQuery searchQuery) throws Exception {

        BpmClient bpmClient = getBpmClient();

        try {
            String actDefId = "";
            String procDefId = "";
            // 待办提交
            if (StrUtil.isNotBlank(searchQuery.getCurActInstId())) {
                ActivitiInstanceModel activitiInstanceModel = bpmClient
                        .getActivityInstance(searchQuery.getCurActInstId());
                actDefId = activitiInstanceModel.getActDefId();
                procDefId = activitiInstanceModel.getProcDefId();
            } else { // 新建提交
                ActivityDefinitionModel activityDefinitionModel = bpmClient.getActDef(searchQuery.getProcDefId(),
                        searchQuery.getCurActDefId());
                actDefId = activityDefinitionModel.getActDefId();
                procDefId = activityDefinitionModel.getProcDefId();
            }
            List<ActivityDefinitionModel> netxtActDefList = bpmClient.getNextacts(searchQuery);
            if (netxtActDefList != null && netxtActDefList.size() == 1) {
                ActivityDefinitionModel netxtActDef = netxtActDefList.get(0);
                ActivityDefinitionModel actDef = bpmClient.getActDef(procDefId, actDefId);
                if (("auto".equalsIgnoreCase(actDef.getJumpType()) || netxtActDef.isNotSelectReceiver()
                        || "endEvent".equals(netxtActDef.getActDefType()))) {
                    return netxtActDef.getActDefId();
                }
            }
        } catch (Exception ex) {
            log.error("checkAutoCommit==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }

        return "";
    }

    /**
     * 获取第一个环节定义信息
     *
     * @param procDefId
     * @return
     */
    public ActivityDefinitionModel getStartactDef(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getStartactDef(procDefId);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getStartactDef==" + procDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 获取环节定义信息
     * @param procDefId 流程定义Id
     * @param actDefId 环节定义Id
     * @return
     * @throws Exception
     */
    public ActivityDefinitionModel getActDef(String procDefId,String actDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getActDef(procDefId, actDefId);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getActDef==" + procDefId+"===="+actDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 获取环节定义信息
     * @param procDefId 流程定义Id
     * @return
     * @throws Exception
     */
    public List<ActivityDefinitionModel> getActDef(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getActDef(procDefId);
        } catch (Exception ex) {
            log.error("getActDef==" + procDefId, ex);
            throw new Exception(ex);
        }
    }

    /**
     * 根据流程定义Key获取流程定义信息
     *
     * @param procDefKey
     * @return
     */
    public ProcessDefinitionModel getProcessDefinitionModel(String procDefKey) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getProcessDef(procDefKey);
        } catch (Exception ex) {
            // TODO Auto-generated catch block
            log.error("getProcessDefinitionModel==" + procDefKey, ex);
            throw new Exception(ex);
        }
    }

    public String getHistasklogurl(String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            Map<String, String> param = bpmClient.getHistoryUrl(procInstId);
            return param.get("url");
        } catch (Exception ex) {
            log.error("getHistasklogurl==" + ex);
            throw new Exception(ex);
        }
    }

    public String getImageUrl(String procDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            Map<String, String> param = bpmClient.getImageUrl(procDefId);
            return param.get("url");
        } catch (Exception ex) {
            log.error("getImageUrl==" + ex);
            throw new Exception(ex);
        }
    }

    /**
     * 查询流程实例状态
     *
     * @param procInstId
     * @param curActInstId
     * @return
     */
    public int getFlowStatus(String procInstId, String curActInstId) {
        BpmClient bpmClient = getBpmClient();
        try {
            ActivitiInstanceModel actInstMap = bpmClient.getActivityInstance(curActInstId);
            if (actInstMap != null && actInstMap.getFinishTime() != null && actInstMap.getFinishTime().length() > 0) {
                ProcessInstanceModel procInst = bpmClient.getProcessInstById(procInstId);
                if (procInst != null && procInst.getFinishTime() != null) {
                    return FLOW_STATUS_FINISH;
                }
                return FLOW_STATUS_READ;
            }
            return FLOW_STATUS_UNREAD;
        } catch (Exception ex) {
            log.error("getFlowStatus===procInstId=" + procInstId + ",curActInstId==" + curActInstId, ex);
        }
        return FLOW_STATUS_ERROR;
    }

    /**
     * 查询流程实例状态,并判断当前用户是否有权限打开待办
     *
     * @param procInstId
     * @param curActInstId
     * @param userId
     * @return
     */
    public ProcessInstanceModel getProcessInstance(String procInstId, String curActInstId,
                                                   String userId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        ProcessInstanceModel procInst = null;
        ActivitiInstanceModel actInst = bpmClient.getActivityInstance(curActInstId);
        if (actInst == null || !StrUtil.equals(actInst.getReceiverUserId(), userId)) {
            throw new Exception("没有找到环节实例");
        }
        procInst = bpmClient.getProcessInstById(procInstId);
        procInst.setCurrentActivitiInstance(actInst);
        Map<String, String> formUrlMap = getFlowActForm(procInst.getProcDefId(), actInst.getActDefId());
        if (formUrlMap.containsKey("formUrl")) {
            procInst.setFormUrl(formUrlMap.get("formUrl"));
        }
        procInst.setProcInstState(FLOW_STATUS_ERROR + "");
        if (actInst.getFinishTime() == null || actInst.getFinishTime().length() == 0) {
            procInst.setProcInstState(FLOW_STATUS_UNREAD + "");
            return procInst;
        }
        if (procInst.getFinishTime() != null) {
            procInst.setProcInstState(FLOW_STATUS_FINISH + "");
        } else {
            procInst.setProcInstState(FLOW_STATUS_READ + "");
        }
        return procInst;
    }

    public Map<String, String> getFlowActForm(String procDefId, String actDefId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        Map<String, String> result = Maps.newHashMap();
        try {
            ActivityDefinitionModel act = bpmClient.getActDef(procDefId, actDefId);
            result.put("formUrl", act.getPageUrl());
            result.put("appFormUrl", act.getMportalUrl());
        } catch (Exception ex) {
            log.error("getFlowActForm==" + ex);
            throw new Exception(ex);
        }
        return result;
    }

    public Map<String, Object> getProcessInstById(String procInstId) {
        BpmClient bpmClient = getBpmClient();
        ProcessInstanceModel procInst = null;
        Map<String, Object> result = Maps.newHashMap();
        try {
            procInst = bpmClient.getProcessInstById(procInstId);
            result = BeanUtil.beanToMap(procInst);
            Map<String, Object> curActInfo = this.getCurActInfo(procInstId);
            result.put("curActInstId", curActInfo.get("curActInstId").toString());
            result.put("curActDefId", curActInfo.get("curActDefId").toString());
            result.put("curActDefName", curActInfo.get("curActDefName").toString());
            result.put("prevActDefId", curActInfo.get("prevActDefId").toString());

            result.put("sendUserId", curActInfo.get("receiveUserId").toString());
            result.put("sendUserOrgId", curActInfo.get("receiveOrgId").toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return result;
    }

    private Map<String, Object> getCurActInfo(String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        Map<String, Object> result = new HashMap<String, Object>();
        // 根据流程实例id获取流程实例日志
        String logStr = bpmClient.getProcInstLog(procInstId);
        List<Map<String, Object>> procLog = new ArrayList<Map<String, Object>>();
        Map<String, Object> procInstLog = JSONObject.parseObject(logStr, Map.class);
        if (null != procInstLog && procInstLog.containsKey("procLogs")) {
            // 获取日志实例
            procLog = (List<Map<String, Object>>) procInstLog.get("procLogs");
            for (Map<String, Object> map : procLog) {
                if (null == map.get("endTime")) {
                    if ("pms_zhsqlc-03".equals(map.get("prevActDefId"))) {
                        result = map;
                    } else {
                        result = map;
                        break;
                    }
                }
            }
        }
        return result;
    }

    public Map<String, Object> procInstInfoAndStatus(String procInstId) {
        Map<String, Object> procInst = this.getProcessInstById(procInstId);
        int status = 0;
        try {
            status = this.getFlowStatus(procInstId, procInst.get("curActInstId").toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        procInst.put("status", status + "");
        return procInst;
    }

    public Map<String, Object> checkAutoJump(SearchQuery searchQuery) throws Exception {
        BpmClient bpmClient = getBpmClient();
        ActivityDefinitionModel actDef = new ActivityDefinitionModel();
        try {
            String actDefId = "";
            String procDefId = "";
            // 待办提交
            if (StrUtil.isNotBlank(searchQuery.getCurActInstId())) {
                ActivitiInstanceModel activitiInstanceModel = bpmClient
                        .getActivityInstance(searchQuery.getCurActInstId());
                actDefId = activitiInstanceModel.getActDefId();
                procDefId = activitiInstanceModel.getProcDefId();
                actDef = bpmClient.getActDef(procDefId, actDefId);
            } else { // 新建提交
                actDef = bpmClient.getActDef(searchQuery.getProcDefId(), searchQuery.getCurActDefId());
            }
            Map<String, Object> result = new HashMap<String, Object>();
            if ("auto".equalsIgnoreCase(actDef.getJumpType())) {
                result.put("flag", true);
            } else {
                result.put("flag", false);
            }
            return result;
        } catch (Exception ex) {
            log.error("checkAutoCommit==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }
    }

    public Map<String, Integer> getRecordCount(SearchQuery searchQuery) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getRecordCount(searchQuery);
        } catch (Exception ex) {
            log.error("getRecordCount==" + JSON.toJSONString(searchQuery), ex);
            throw new Exception(ex);
        }
    }

    /**
     * 根据父流程实例id获取该流程的子流程，从而处理子流程。
     *
     * @param procInstId 父流程实例id
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getSubprocess(String status, String procInstId) throws Exception {
        BpmClient bpmClient = getBpmClient();
        try {
            return bpmClient.getSubprocess(status, procInstId);
        } catch (Exception ex) {
            log.error("getSubprocess==" + procInstId, ex);
            throw new Exception(ex);
        }
    }
}
