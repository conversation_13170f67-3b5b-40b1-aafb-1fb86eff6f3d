package com.rzdata.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rzdata.framework.core.domain.entity.SysRoleMenuVo;
import com.rzdata.framework.core.service.SysRoleMenuService;
import com.rzdata.system.domain.SysRoleMenu;

import java.util.List;
import java.util.Map;

public interface ISysRoleMenuService  extends IService<SysRoleMenu>, SysRoleMenuService {
    void updateMenuScope(Map<String,Object> params);

    List<SysRoleMenuVo> queryDataScopeList(Long roleId);

}
