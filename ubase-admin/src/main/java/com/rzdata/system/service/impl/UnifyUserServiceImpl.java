package com.rzdata.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.rzdata.config.OaProperties;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.system.domain.bo.UnifyUserBo;
import com.rzdata.system.domain.vo.UnifyUserVo;
import com.rzdata.system.domain.UnifyUser;
import com.rzdata.system.mapper.UnifyUserMapper;
import com.rzdata.system.service.IUnifyUserService;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 申石用户表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Slf4j
@Service
public class UnifyUserServiceImpl extends ServicePlusImpl<UnifyUserMapper, UnifyUser, UnifyUserVo> implements IUnifyUserService {

    @Resource
    private OaProperties oaProperties;
    @Override
    public UnifyUserVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<UnifyUserVo> queryPageList(UnifyUserBo bo) {
        PagePlus<UnifyUser, UnifyUserVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<UnifyUserVo> queryList(UnifyUserBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<UnifyUser> buildQueryWrapper(UnifyUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UnifyUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), UnifyUser::getSn, bo.getSn());
        lqw.like(StringUtils.isNotBlank(bo.getName()), UnifyUser::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), UnifyUser::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getUserTypeId()), UnifyUser::getUserTypeId, bo.getUserTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getTelephone()), UnifyUser::getTelephone, bo.getTelephone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), UnifyUser::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), UnifyUser::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getOptUser()), UnifyUser::getOptUser, bo.getOptUser());
        lqw.eq(StringUtils.isNotBlank(bo.getExtraAttrs()), UnifyUser::getExtraAttrs, bo.getExtraAttrs());
        lqw.like(StringUtils.isNotBlank(bo.getAppName()), UnifyUser::getAppName, bo.getAppName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgId()), UnifyUser::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgSn()), UnifyUser::getOrgSn, bo.getOrgSn());
        lqw.like(StringUtils.isNotBlank(bo.getOrgName()), UnifyUser::getOrgName, bo.getOrgName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgNamePath()), UnifyUser::getOrgNamePath, bo.getOrgNamePath());
        lqw.eq(StringUtils.isNotBlank(bo.getPosition()), UnifyUser::getPosition, bo.getPosition());
        lqw.eq(StringUtils.isNotBlank(bo.getImage()), UnifyUser::getImage, bo.getImage());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgTypeId()), UnifyUser::getOrgTypeId, bo.getOrgTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getUserTypeName()), UnifyUser::getUserTypeName, bo.getUserTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getMultiOrgs()), UnifyUser::getMultiOrgs, bo.getMultiOrgs());
        lqw.eq(StringUtils.isNotBlank(bo.getPositions()), UnifyUser::getPositions, bo.getPositions());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgType()), UnifyUser::getOrgType, bo.getOrgType());
        lqw.eq(StringUtils.isNotBlank(bo.getMultiOrgsString()), UnifyUser::getMultiOrgsString, bo.getMultiOrgsString());
        lqw.eq(StringUtils.isNotBlank(bo.getPositionType()), UnifyUser::getPositionType, bo.getPositionType());
        lqw.eq(StringUtils.isNotBlank(bo.getGroupId()), UnifyUser::getGroupId, bo.getGroupId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(UnifyUserBo bo) {
        UnifyUser add = BeanUtil.toBean(bo, UnifyUser.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 同步申石用户表
     * @return
     */
    @Override
    public Boolean sycn(){
        //登录申石系统，获取Token
        String tokenUrl = oaProperties.getSycn().getUrl() + "/api/sys/getToken";
        log.error("申石登录地址：{}",tokenUrl);
        Map<String,Object> paramMap = new HashMap<String,Object>();
        paramMap.put("username",oaProperties.getSycn().getUsername());
        paramMap.put("password",oaProperties.getSycn().getPassword());
        log.error("参数:{}",paramMap.toString());
        String result = HttpRequest.post(tokenUrl)
                .body(JSONUtil.toJsonStr(paramMap))
                .header("Content-Type", "application/json").execute().body();
        log.error("获得access_token:{}",result);
        JSONObject json = JSONUtil.parseObj(result);

        //判断是否登录成功
        if("true".equals(json.get("success").toString())){
            String token = json.get("msg").toString();

            //请求用户参数组装
            Integer pageSize = 1000;
            Integer page = 1;
            String userInfoUrl = oaProperties.getSycn().getUrl() + "/api/acct/getUserInfo";
            Map<String,Object> userParamMap = new HashMap<String,Object>();
            userParamMap.put("orgSn","");
            userParamMap.put("appSn",oaProperties.getSycn().getClient());
            userParamMap.put("page",page);
            userParamMap.put("pageSize",pageSize);
            userParamMap.put("startTime","");
            log.error("请求用户列表{}",userParamMap.toString());

            // 请求用户列表
            JSONObject userInfoJson = this.getUserList(userInfoUrl,userParamMap,token);
            if(userInfoJson == null){
                log.error("请求申石用户失败");
                return false;
            }

            //获取列表，判断用户是否在第一页
            String data = userInfoJson.get("data").toString();
            Integer totalCount =Integer.valueOf(userInfoJson.get("totalCount").toString());
            Integer totalPage = totalCount / pageSize;
            if (totalCount % pageSize != 0) {
                totalPage++;
            }
            log.error("总共{}页,{}条",totalPage,totalCount);
            List<UnifyUser> userList = JSON.parseObject(data, new TypeReference<List<UnifyUser>>(){});
            page++;
            for(int i=page;i<=totalPage;i++){
                userParamMap.put("page",i);
                JSONObject nixtUserInfoJson = this.getUserList(userInfoUrl,userParamMap,token);
                if(nixtUserInfoJson == null){
                    log.error("请求申石用户失败");
                    continue;
                }
                log.error("第{}页",i);
                String nextData = nixtUserInfoJson.get("data").toString();
                List<UnifyUser> nextUserList = JSON.parseObject(nextData, new TypeReference<List<UnifyUser>>(){});
                userList.addAll(nextUserList);
            }
            this.saveOrUpdateBatch(userList);
        }
        return true;
    }

    private JSONObject getUserList(String userInfoUrl,Map<String,Object> userParamMap,String token){
        // 请求用户列表
        String userInfoResult = HttpRequest.post(userInfoUrl)
                .body(JSONUtil.toJsonStr(userParamMap))
                .header("uim-login-user-id", token).execute().body();
        JSONObject userInfoJson = JSONUtil.parseObj(userInfoResult);
        if(!"true".equals(userInfoJson.get("success").toString())){
            log.error("请求错误:{}",userInfoResult);
            return null;
        }
        return userInfoJson;
    }

    @Override
    public Boolean updateByBo(UnifyUserBo bo) {
        UnifyUser update = BeanUtil.toBean(bo, UnifyUser.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(UnifyUser entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
