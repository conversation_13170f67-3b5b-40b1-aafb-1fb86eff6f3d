package com.rzdata.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * xxljob配置类
 *
 * <AUTHOR> Li
 */
@Data
//@Component
//@ConfigurationProperties(prefix = "process")
public class ProcessConfig {

    private String uniteworkUrl;

    /**
     * 文件复审申请
     */
    private String procDefKeyFSSQ;

    /**
     * 文件增发申请
     */
    private String procDefKeyZFSQ;

    /**
     * 文件变更申请
     */
    private String procDefKeyBGSQ;

    /**
     * 文件作废
     */
    private String procDefKeyDISUSE;

    /**
     * 文件修订
     */
    private String procDefKeyUPDATE;

    /**
     * 文件新增
     */
    private String procDefKeyADD;

    /**
     * 文件借阅申请
     */
    private String procDefKeyJYSQ;

    /**
     * 文件补发申请
     */
    private String procDefKeyBFSQ;
}
