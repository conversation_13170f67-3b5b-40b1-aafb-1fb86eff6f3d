package com.rzdata.config;

import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 *
 * <AUTHOR> Li
 */

@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "rzdata")
public class CustomConfig {

    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;
    /**
     * 流程平台下应用租户id
     */
    private String bpmTenantId;

    /**
     * 流程服务接口地址
     */
    private String workflowServiceUrl;
    /**
     * 缓存懒加载
     */
    private boolean cacheLazy;

    /**
     * 获取地址开关
     */
    @Getter
    private static boolean addressEnabled;

    public void setAddressEnabled(boolean addressEnabled) {
        CustomConfig.addressEnabled = addressEnabled;
    }

}
