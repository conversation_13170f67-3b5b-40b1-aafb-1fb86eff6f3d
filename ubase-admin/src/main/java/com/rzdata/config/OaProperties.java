package com.rzdata.config;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Configuration
@ConfigurationProperties(prefix = "oa")
public class OaProperties {
    private SycnProp sycn;
    @Data
    public static class SycnProp{
        /**
         * oa 的host
         */
        private String url;
        /**
         * oa client code
         */
        private String client;
        /**
         * 账号
         */
        private String username;
        /**
         * 密码
         */
        private String password;
    }
}
