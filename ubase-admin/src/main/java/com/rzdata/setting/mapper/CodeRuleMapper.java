package com.rzdata.setting.mapper;

import com.rzdata.setting.domain.CodeRule;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 编号规则Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Mapper
public interface CodeRuleMapper extends BaseMapperPlus<CodeRule> {

    String selectDeptCodeByName(@Param("name") String name);

    Date selectEffectDate(@Param("versionId") String versionId);

}
