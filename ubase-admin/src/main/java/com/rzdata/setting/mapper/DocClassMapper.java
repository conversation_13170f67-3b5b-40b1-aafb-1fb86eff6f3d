package com.rzdata.setting.mapper;

import com.rzdata.setting.domain.DocClass;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 文件类型Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface DocClassMapper extends BaseMapperPlus<DocClass> {

    /**
     * 通过查出这个版本的文件在这个分发部门是否已经全部打印
     * @param versionId 版本
     * @param docId 文档id
     * @param deptId 打印部门
     * @return 0表示还没有全部打印 1表示已经全部打印了
     */
    Integer validateZf(@Param("versionId") String versionId,@Param("docId") String docId,@Param("deptId") String deptId);
}
