package com.rzdata.setting.mapper;

import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;

import java.util.HashMap;
import java.util.List;

/**
 * 文件分类设置-节点明细设置Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface DocClassFlowNodeDetailMapper extends BaseMapperPlus<DocClassFlowNodeDetail> {


    /**
     * 根据流程KEY和流程环节，获取环节权限明细配置
     *
     * @param map
     * @return
     */
    public List<DocClassFlowNodeDetail> queryList(HashMap map);
}
