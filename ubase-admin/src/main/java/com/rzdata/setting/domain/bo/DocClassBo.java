package com.rzdata.setting.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 文件类型业务对象 basic_doc_class
 *
 * <AUTHOR>
 * @date 2021-12-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件类型业务对象")
public class DocClassBo extends BaseEntity {

    /**
     * 分类代码
     */
    @ApiModelProperty(value = "分类代码", required = true)
//    @NotBlank(message = "分类代码不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称", required = true)
//    @NotBlank(message = "分类名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String className;

    /**
     * 上级分类
     */
    @ApiModelProperty(value = "上级分类", required = true)
//    @NotBlank(message = "上级分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentClassId;

    /**
     * 分类状态
     */
    @ApiModelProperty(value = "分类状态", required = true)
//    @NotBlank(message = "分类状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String classStatus;

    /**
     * 分类层级
     */
    @ApiModelProperty(value = "分类层级", required = true)
//    @NotBlank(message = "分类层级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String classLevel;

    /**
     * 文件有效期
     */
    @ApiModelProperty(value = "文件有效期", required = true)
//    @NotNull(message = "文件有效期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long expiration;

    /**
     * 文件复审周期
     */
    @ApiModelProperty(value = "文件复审周期", required = true)
//    @NotNull(message = "文件复审周期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reviewCycle;

    /**
     * 签收类型，个人，部门
     */
    @ApiModelProperty(value = "签收类型，个人，部门", required = true)
//    @NotBlank(message = "签收类型，个人，部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String checkType;

    /**
     * 签章类型，个人、部门、公司
     */
    @ApiModelProperty(value = "签章类型，个人、部门、公司", required = true)
//    @NotBlank(message = "签章类型，个人、部门、公司不能为空", groups = { AddGroup.class, EditGroup.class })
    private String encryptType;

    /**
     * 是否纸质打印
     */
    @ApiModelProperty(value = "是否纸质打印", required = true)
//    @NotBlank(message = "是否纸质打印不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yNPrint;

    /**
     * 是否纸质回收
     */
    @ApiModelProperty(value = "是否纸质回收", required = true)
//    @NotBlank(message = "是否纸质回收不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yNRecyle;

    /**
     * 合稿设置
     */
    @ApiModelProperty(value = "合稿设置", required = true)
//    @NotBlank(message = "合稿设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mergeDocConfig;

    /**
     * 编号设置
     */
    @ApiModelProperty(value = "编号设置", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String codeId;

    /**
     * 是否启用文件编号
     */
    @ApiModelProperty(value = "是否启用文件编号", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yNOpenCode;

    /**
     * 是否应用到子文件类型
     */
    @ApiModelProperty(value = "是否应用到子文件类型", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String yNApplyCode;

    /**
     * 文件分发号规则
     */
    @ApiModelProperty(value = "文件分发号规则", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String distributeId;

    /**
     * 是否启用文件分发号设置
     */
    @ApiModelProperty(value = "是否启用文件分发号设置", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openDistribute;

    /**
     * 文件分发号设置是否应用到子类型
     */
    @ApiModelProperty(value = "文件分发号设置是否应用到子类型", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyDistribute;

    /**
     * 文件时效设置是否应用到子类型
     */
    @ApiModelProperty(value = "文件时效设置是否应用到子类型", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyPrescription;

    private String openReview;
    private String applyReview;

    private String openMerge;
    private String applyMerge;
    /**
     * 签章类型
     */
    private String signatureType;
    /**
     *
     */
    private String openSignature;
    /**
     *
     */
    private String applySignature;
    /**
     * 是否启用文件时效设置
     */
    @ApiModelProperty(value = "是否启用文件时效设置", required = true)
//    @NotBlank(message = "编号设置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openPrescription;

    /**
     * 文件模板
     */
    @ApiModelProperty(value = "文件模板", required = true)
//    @NotBlank(message = "文件模板不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("文件合稿设置信息")
    private List<DocClassMergeBo> docClassMergeBoList;

    @ApiModelProperty("文件签章设置信息")
    private List<DocClassSignatureBo> docClassSignatureBoList;
}
