package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 签章管理对象 basic_signature
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@Data
@Accessors(chain = true)
@TableName("basic_signature")
public class Signature{

    private static final long serialVersionUID=1L;

    /**
     * 签章管理ID
     */
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 签章名称
     */
    private String signatureName;
    /**
     * 签章模板
     */
    private String fileId;
    /**
     * 状态
     */
    private String signatureStatus;
    /**
     * 文件名称
     */
    private String templateName;

    /**
     * 逻辑删除标志 0=正常 2=已删除
     */
    @TableLogic
    private Integer deleteFlag;

    private Date createTime;

    private Date updateTime;

}
