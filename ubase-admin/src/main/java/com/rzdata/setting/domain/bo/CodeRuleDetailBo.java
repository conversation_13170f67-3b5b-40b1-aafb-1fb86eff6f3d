package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 编号规则明细业务对象 basic_code_rule_detail
 *
 * <AUTHOR>
 * @date 2021-12-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编号规则明细业务对象")
public class CodeRuleDetailBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 规则主键
     */
    @ApiModelProperty(value = "规则主键", required = true)
    @NotBlank(message = "规则主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleId;

    /**
     * 规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM
     */
    @ApiModelProperty(value = "规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM", required = true)
    @NotBlank(message = "规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleType;

    /**
     * 规则值，对应规则类型，如YYYY，其中流水号的值为空
     */
    @ApiModelProperty(value = "规则值，对应规则类型，如YYYY，其中流水号的值为空", required = true)
    @NotBlank(message = "规则值，对应规则类型，如YYYY，其中流水号的值为空不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleValue;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号", required = true)
    @NotNull(message = "排序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderBy;

    /**
     * 分隔符
     */
    @ApiModelProperty(value = "分隔符")
    private String slicerValue;

    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
