package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 编号规则明细对象 basic_code_rule_detail
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@Accessors(chain = true)
@TableName("basic_code_rule_detail")
public class CodeRuleDetail {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则主键
     */
    private String ruleId;
    /**
     * 规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM
     */
    private String ruleType;
    /**
     * 规则值，对应规则类型，如YYYY，其中流水号的值为空
     */
    private String ruleValue;
    /**
     * 排序号
     */
    private Long orderBy;

    /**
     * 分隔符
     */
    private String slicerValue;

}
