package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.vo.DocClassFlowNodeDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 文件分类设置-流程节点设置业务对象 basic_doc_class_flow_node
 *
 * <AUTHOR>
 * @date 2023-01-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分类设置-流程节点设置业务对象")
public class DocClassFlowNodeBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 流程设置主键;basic_doc_flow主键id
     */
    @ApiModelProperty(value = "流程设置主键;basic_doc_flow主键id", required = true)
    @NotBlank(message = "流程设置主键;basic_doc_flow主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flowId;

    /**
     * 节点编码
     */
    @ApiModelProperty(value = "节点编码", required = true)
    @NotBlank(message = "节点编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nodeCode;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称", required = true)
    @NotBlank(message = "节点名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nodeName;

    /**
     * 页面展示模式;只读view、编辑edit、其他other
     */
    @ApiModelProperty(value = "页面展示模式;只读view、编辑edit、其他other", required = true)
    @NotBlank(message = "页面展示模式;只读view、编辑edit、其他other不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pageMode;

    /**
     * 附件展示模式;只读view、编辑edit、其他other
     */
    @ApiModelProperty(value = "附件展示模式;只读view、编辑edit、其他other", required = true)
    @NotBlank(message = "附件展示模式;只读view、编辑edit、其他other不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appendixMode;

    /**
     * 模块展示集合;展示多个模块用半角逗号分隔
     */
    @ApiModelProperty(value = "模块展示集合;展示多个模块用半角逗号分隔", required = true)
    @NotBlank(message = "模块展示集合;展示多个模块用半角逗号分隔不能为空", groups = { AddGroup.class, EditGroup.class })
    private String moduleConfig;

    /**
     * 是否删除;是Y 否N
     */
    @ApiModelProperty(value = "是否删除;是Y 否N", required = true)
    @NotBlank(message = "是否删除;是Y 否N不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deleteFlag;

    /**
     * 序号;数字越小，越靠前
     */
    @ApiModelProperty(value = "序号;数字越小，越靠前", required = true)
    @NotNull(message = "序号;数字越小，越靠前不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("环节权限明细清单")
    private List<DocClassFlowNodeDetailBo> nodeDetailList;
}
