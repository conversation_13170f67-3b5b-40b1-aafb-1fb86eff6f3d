package com.rzdata.setting.domain.bo;

import com.rzdata.process.enums.MergeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/14 15:47
 * @Version 1.0
 * @Description
 */
@Data
public class DocClassMergeBo {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id",required = true)
    private String id;
    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型",required = true)
    private String docClass;

    /**
     * 合稿要素名称
     */
    @ApiModelProperty(value = "合稿要素名称",required = true)
    private String mergeFactorName;

    /**
     * 合稿要素-是否选择 0=未选择 1=已选择
     */
    @ApiModelProperty(value = "合稿要素-是否选择 0=未选择 1=已选择",required = true)
    private Integer mergeFactorUse;

    /**
     * 样式名称
     */
    //@ApiModelProperty(value = "样式名称",required = true)
    //private String codraftName;

    /**
     * 样式id(basic_codraft主键id)
     */
    @ApiModelProperty(value = "样式id(basic_codraft主键id)",required = true)
    private String codraftId;

    /**
     * 是否另起一页 0=否 1=是 用于合稿要素一
     */
    @ApiModelProperty(value = "是否另起一页 0=否 1=是",required = true)
    private Integer anotherPage;

    /**
     * 用于合稿要素二
     * 应用范围 1=除首页 2=除末页 3=全文应用 4=仅首页
     */
    @ApiModelProperty(value = "1=除首页 2=除末页 3=全文应用 4=仅首页",required = true)
    private Integer rangeValue;

    /**
     * 类型:生效文件(VALID)/失效文件(INVALID)
     */
    @ApiModelProperty(value = "类型:生效文件(VALID)/失效文件(INVALID)",required = true)
    private MergeTypeEnum type;

    /**
     * 1=合稿要素一 2=合稿要素二 3=合稿要素三.....
     */
    @ApiModelProperty(value = "1=合稿要素一 2=合稿要素二 3=合稿要素三.....",required = true)
    private Integer mergeFactor;

    /**
     * 是否是默认值 是默认值则不可以删除 0=否 1=是
     */
    private Integer defaultFlag;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private Date updateTime;
}
