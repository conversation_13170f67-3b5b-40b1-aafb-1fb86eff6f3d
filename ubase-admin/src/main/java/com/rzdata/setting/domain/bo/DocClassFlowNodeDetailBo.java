package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 文件分类设置-节点明细设置业务对象 basic_doc_class_flow_node_detail
 *
 * <AUTHOR>
 * @date 2023-01-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分类设置-节点明细设置业务对象")
public class DocClassFlowNodeDetailBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 流程节点主键;basic_doc_flow_node主键id
     */
    @ApiModelProperty(value = "流程节点主键;basic_doc_flow_node主键id", required = true)
    @NotBlank(message = "流程节点主键;basic_doc_flow_node主键id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nodeId;

    /**
     * 权限类型;btn按钮、oper操作
     */
    @ApiModelProperty(value = "权限类型;btn按钮、oper操作", required = true)
    @NotBlank(message = "权限类型;btn按钮、oper操作不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 权限编码
     */
    @ApiModelProperty(value = "权限编码", required = true)
    @NotBlank(message = "权限编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 权限名称
     */
    @ApiModelProperty(value = "权限名称", required = true)
    @NotBlank(message = "权限名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 调用方法名称
     */
    @ApiModelProperty(value = "调用方法名称", required = true)
    @NotBlank(message = "调用方法名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String funName;

    /**
     * 调用方法条件;调用方法前置条件
     */
    @ApiModelProperty(value = "调用方法条件;调用方法前置条件", required = true)
    @NotBlank(message = "调用方法条件;调用方法前置条件不能为空", groups = { AddGroup.class, EditGroup.class })
    private String funCondition;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
