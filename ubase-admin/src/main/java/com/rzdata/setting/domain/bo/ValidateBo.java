package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编号规则明细业务对象")
public class ValidateBo extends BaseEntity {
    @NotNull
    private String applyId;
    @NotNull
    private String versionId;
    @NotNull
    private String docId;
    @NotNull
    private String disIds;
}
