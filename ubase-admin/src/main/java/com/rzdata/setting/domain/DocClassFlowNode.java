package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件分类设置-流程节点设置对象 basic_doc_class_flow_node
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_flow_node")
public class DocClassFlowNode extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 流程设置主键;basic_doc_flow主键id
     */
    private String flowId;
    /**
     * 节点编码
     */
    private String nodeCode;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 页面展示模式;只读view、编辑edit、其他other
     */
    private String pageMode;
    /**
     * 附件展示模式;只读view、编辑edit、其他other
     */
    private String appendixMode;
    /**
     * 模块展示集合;展示多个模块用半角逗号分隔
     */
    private String moduleConfig;
    /**
     * 是否删除;是Y 否N
     */
    private String deleteFlag;
    /**
     * 序号;数字越小，越靠前
     */
    private Long sort;

}
