package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 签章管理业务对象 basic_signature
 *
 * <AUTHOR>
 * @date 2022-03-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("签章管理业务对象")
public class SignatureBo extends BaseEntity {

    /**
     * 签章管理ID
     */
    @ApiModelProperty(value = "签章管理ID")
    private String id;

    /**
     * 签章名称
     */
    @ApiModelProperty(value = "签章名称", required = true)
   // @NotBlank(message = "签章名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signatureName;

    /**
     * 签章模板
     */
    @ApiModelProperty(value = "签章模板", required = true)
    @NotBlank(message = "签章模板不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    //@NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signatureStatus;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称", required = true)
    //@NotBlank(message = "文件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateName;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
