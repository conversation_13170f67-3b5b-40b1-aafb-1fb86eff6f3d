package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 文件分类设置-流程设置业务对象 basic_doc_class_flow
 *
 * <AUTHOR>
 * @date 2023-01-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("文件分类设置-流程设置业务对象")
public class DocClassFlowBo extends BaseEntity {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true)
    @NotBlank(message = "主键id不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 文件类型;basic_doc_class表主键ID
     */
    @ApiModelProperty(value = "文件类型;basic_doc_class表主键ID", required = true)
    @NotBlank(message = "文件类型;basic_doc_class表主键ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docClass;

    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
    @ApiModelProperty(value = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc", required = true)
    @NotBlank(message = "业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizType;

    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称", required = true)
    @NotBlank(message = "业务名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bizName;

    /**
     * 流程KEY;对应流程KEY
     */
    @ApiModelProperty(value = "流程KEY;对应流程KEY", required = true)
    @NotBlank(message = "流程KEY;对应流程KEY不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flowKey;

    /**
     * 流程名称;对应流程名称
     */
    @ApiModelProperty(value = "流程名称;对应流程名称", required = true)
    @NotBlank(message = "流程名称;对应流程名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String flowName;

    /**
     * 是否生效;Y生效 N不生效
     */
    @ApiModelProperty(value = "是否生效;Y生效 N不生效", required = true)
    @NotBlank(message = "是否生效;Y生效 N不生效不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openFlag;

    /**
     * 是否应用子分类;Y应用 N不应用
     */
    @ApiModelProperty(value = "是否应用子分类;Y应用 N不应用", required = true)
    @NotBlank(message = "是否应用子分类;Y应用 N不应用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyFlag;

    /**
     * 审批时效性
     */
    @ApiModelProperty(value = "审批时效性", required = true)
    @NotNull(message = "审批时效性不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dealTime;

    /**
     * 审批时效性单位
     */
    @ApiModelProperty(value = "审批时效性单位", required = true)
    @NotBlank(message = "审批时效性单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dealTimeUnit;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

    @ApiModelProperty("流程节点清单")
    private List<DocClassFlowNodeBo> nodeList;

}
