package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 合稿管理视图对象 basic_codraft
 *
 * <AUTHOR>
 * @date 2022-03-02
 */
@Data
@ApiModel("合稿管理视图对象")
@ExcelIgnoreUnannotated
public class CodraftVo {

	private static final long serialVersionUID = 1L;

    /**
     * 合稿管理ID
     */
	@ExcelProperty(value = "合稿管理ID")
	@ApiModelProperty("合稿管理ID")
	private String id;

    /**
     * 合稿要素名称
     */
	@ExcelProperty(value = "合稿要素名称")
	@ApiModelProperty("合稿要素名称")
	private String codraftName;

    /**
     * 合稿模板
     */
	@ExcelProperty(value = "合稿模板")
	@ApiModelProperty("合稿模板")
	private String fileId;

	/**
	 * 模板名称
	 */
	@ExcelProperty(value = "模板名称")
	@ApiModelProperty("模板名称")
	private String templateName;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
	@ApiModelProperty("状态")
	private String codraftStatus;


}
