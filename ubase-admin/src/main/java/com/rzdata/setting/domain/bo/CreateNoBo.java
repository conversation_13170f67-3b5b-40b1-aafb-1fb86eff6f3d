package com.rzdata.setting.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @auther xcy
 * @create 2021-12-28 10:51
 */
@Data
public class CreateNoBo {

    @ApiModelProperty("规则id")
    private String ruleId;

    @ApiModelProperty("业务主键id 可空")
    private String busId;

    @ApiModelProperty("旧编号 可空")
    private String oldNo;

    @ApiModelProperty("新编码 手填 可空")
    private String newNo;

    @ApiModelProperty("固定字符串")
    private String str;

    @ApiModelProperty("编号类型1 文件 2 流程")
    private Integer type;

    @ApiModelProperty("操作用户")
    private String userName;

    @ApiModelProperty("版本编号")
    private String versionId;

}
