package com.rzdata.setting.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/31 11:41
 * @Version 1.0
 * @Description
 */
@Data
public class DocClassSignatureVo {
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private int sort;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型", required = true)
    private String docClass;

    /**
     * 签章要素名称
     */
    @ApiModelProperty(value = "签章要素名称", required = true)
    private String signatureFactorName;

    /**
     * 是否选择 0=未选择 1=已选择
     */
    @ApiModelProperty(value = "是否选择 0=未选择 1=已选择", required = true)
    private Integer signatureFactorUse;

    @ApiModelProperty(value = "样式id", required = true)
    private String signatureId;

    /**
     * 用于合稿要素二
     * 应用范围 1=除首页 2=除末页 3=全文应用 4=仅首页
     */
    @ApiModelProperty(value = "1=除首页 2=除末页 3=全文应用 4=仅首页", required = true)
    private Integer rangeValue;

    /**
     * 是否是默认值 是默认值则不可以删除 0=否 1=是
     */
    @ApiModelProperty(value = "是否是默认值 是默认值则不可以删除 0=否 1=是", required = true)
    private Integer defaultFlag;

    @ApiModelProperty(value = "生成时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
}
