package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 签章管理视图对象 basic_signature
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@Data
@ApiModel("签章管理视图对象")
@ExcelIgnoreUnannotated
public class SignatureVo {

	private static final long serialVersionUID = 1L;

    /**
     * 签章管理ID
     */
	@ExcelProperty(value = "签章管理ID")
	@ApiModelProperty("签章管理ID")
	private String id;

    /**
     * 签章名称
     */
	@ExcelProperty(value = "签章名称")
	@ApiModelProperty("签章名称")
	private String signatureName;

    /**
     * 签章模板
     */
	@ExcelProperty(value = "签章模板")
	@ApiModelProperty("签章模板")
	private String fileId;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
	@ApiModelProperty("状态")
	private String signatureStatus;

    /**
     * 文件名称
     */
	@ExcelProperty(value = "文件名称")
	@ApiModelProperty("文件名称")
	private String templateName;


}
