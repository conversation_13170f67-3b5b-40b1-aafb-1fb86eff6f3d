package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;


/**
 * 文件分类设置-流程设置对象 basic_doc_class_flow
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@Accessors(chain = true)
@TableName("basic_doc_class_flow")
public class DocClassFlow extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 文件类型;basic_doc_class表主键ID
     */
    private String docClass;
    /**
     * 业务类型;文件新增add_doc、文件修订update_doc、文件作废disuse_doc
     */
    private String bizType;
    /**
     * 业务名称
     */
    private String bizName;
    /**
     * 流程KEY;对应流程KEY
     */
    private String flowKey;
    /**
     * 流程名称;对应流程名称
     */
    private String flowName;
    /**
     * 是否生效;Y生效 N不生效
     */
    private String openFlag;
    /**
     * 是否应用子分类;Y应用 N不应用
     */
    private String applyFlag;
    /**
     * 审批时效性
     */
    private Long dealTime;
    /**
     * 审批时效性单位
     */
    private String dealTimeUnit;

}
