package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.setting.domain.CodeRuleDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;
import java.util.List;


/**
 * 编号规则业务对象 basic_code_rule
 *
 * <AUTHOR>
 * @date 2021-12-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编号规则业务对象")
public class CodeRuleBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称", required = true)
    @NotBlank(message = "规则名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleName;

    /**
     * 流水号重置周期，不重置：NO、年：Y、月：M、日：D
     */
    @ApiModelProperty(value = "流水号重置周期，不重置：NO、年：Y、月：M、日：D", required = true)
    @NotBlank(message = "流水号重置周期，不重置：NO、年：Y、月：M、日：D不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resetCycle;

    /**
     * 流水号初始值
     */
    @ApiModelProperty(value = "流水号初始值", required = true)
    @NotNull(message = "流水号初始值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long numberInitValue;

    /**
     * 流水号位数
     */
    @ApiModelProperty(value = "流水号位数", required = true)
    @NotNull(message = "流水号位数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer numberDigit;

    @ApiModelProperty("规则详情集合")
    private List<CodeRuleDetailBo> ruleDetailList;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
