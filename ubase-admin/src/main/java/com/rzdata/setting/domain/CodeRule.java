package com.rzdata.setting.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.rzdata.framework.core.domain.BaseEntity;

import java.util.Date;


/**
 * 编号规则对象 basic_code_rule
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@Accessors(chain = true)
@TableName("basic_code_rule")
public class CodeRule {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 流水号重置周期，不重置：NO、年：Y、月：M、日：D
     */
    private String resetCycle;
    /**
     * 流水号初始值
     */
    private Long numberInitValue;
    /**
     * 流水号位数
     */
    private Integer numberDigit;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
