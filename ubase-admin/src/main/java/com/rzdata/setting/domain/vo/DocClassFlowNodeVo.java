package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 文件分类设置-流程节点设置视图对象 basic_doc_class_flow_node
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@ApiModel("文件分类设置-流程节点设置视图对象")
@ExcelIgnoreUnannotated
public class DocClassFlowNodeVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 流程设置主键;basic_doc_flow主键id
     */
	@ExcelProperty(value = "流程设置主键;basic_doc_flow主键id")
	@ApiModelProperty("流程设置主键;basic_doc_flow主键id")
	private String flowId;

    /**
     * 节点编码
     */
	@ExcelProperty(value = "节点编码")
	@ApiModelProperty("节点编码")
	private String nodeCode;

    /**
     * 节点名称
     */
	@ExcelProperty(value = "节点名称")
	@ApiModelProperty("节点名称")
	private String nodeName;

    /**
     * 页面展示模式;只读view、编辑edit、其他other
     */
	@ExcelProperty(value = "页面展示模式;只读view、编辑edit、其他other")
	@ApiModelProperty("页面展示模式;只读view、编辑edit、其他other")
	private String pageMode;

    /**
     * 附件展示模式;只读view、编辑edit、其他other
     */
	@ExcelProperty(value = "附件展示模式;只读view、编辑edit、其他other")
	@ApiModelProperty("附件展示模式;只读view、编辑edit、其他other")
	private String appendixMode;

    /**
     * 模块展示集合;展示多个模块用半角逗号分隔
     */
	@ExcelProperty(value = "模块展示集合;展示多个模块用半角逗号分隔")
	@ApiModelProperty("模块展示集合;展示多个模块用半角逗号分隔")
	private String moduleConfig;

    /**
     * 是否删除;是Y 否N
     */
	@ExcelProperty(value = "是否删除;是Y 否N")
	@ApiModelProperty("是否删除;是Y 否N")
	private String deleteFlag;

    /**
     * 序号;数字越小，越靠前
     */
	@ExcelProperty(value = "序号;数字越小，越靠前")
	@ApiModelProperty("序号;数字越小，越靠前")
	private Long sort;

	@ApiModelProperty("环节权限明细清单")
	private List<DocClassFlowNodeDetailVo> nodeDetailList;


}
