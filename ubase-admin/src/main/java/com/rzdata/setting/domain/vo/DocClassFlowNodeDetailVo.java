package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 文件分类设置-节点明细设置视图对象 basic_doc_class_flow_node_detail
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Data
@ApiModel("文件分类设置-节点明细设置视图对象")
@ExcelIgnoreUnannotated
public class DocClassFlowNodeDetailVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 租户id
     */
	@ExcelProperty(value = "租户id")
	@ApiModelProperty("租户id")
	private String tenantId;

    /**
     * 流程节点主键;basic_doc_flow_node主键id
     */
	@ExcelProperty(value = "流程节点主键;basic_doc_flow_node主键id")
	@ApiModelProperty("流程节点主键;basic_doc_flow_node主键id")
	private String nodeId;

    /**
     * 权限类型;btn按钮、oper操作
     */
	@ExcelProperty(value = "权限类型;btn按钮、oper操作")
	@ApiModelProperty("权限类型;btn按钮、oper操作")
	private String type;

    /**
     * 权限编码
     */
	@ExcelProperty(value = "权限编码")
	@ApiModelProperty("权限编码")
	private String code;

    /**
     * 权限名称
     */
	@ExcelProperty(value = "权限名称")
	@ApiModelProperty("权限名称")
	private String name;

    /**
     * 调用方法名称
     */
	@ExcelProperty(value = "调用方法名称")
	@ApiModelProperty("调用方法名称")
	private String funName;

    /**
     * 调用方法条件;调用方法前置条件
     */
	@ExcelProperty(value = "调用方法条件;调用方法前置条件")
	@ApiModelProperty("调用方法条件;调用方法前置条件")
	private String funCondition;

    /**
     * 排序
     */
	@ExcelProperty(value = "排序")
	@ApiModelProperty("排序")
	private Long sort;

    /**
     * 备注
     */
	@ExcelProperty(value = "备注")
	@ApiModelProperty("备注")
	private String remark;


}
