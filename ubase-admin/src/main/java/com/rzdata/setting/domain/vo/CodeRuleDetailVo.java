package com.rzdata.setting.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import com.rzdata.framework.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 编号规则明细视图对象 basic_code_rule_detail
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
@ApiModel("编号规则明细视图对象")
@ExcelIgnoreUnannotated
public class CodeRuleDetailVo {

	private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@ExcelProperty(value = "主键")
	@ApiModelProperty("主键")
	private String id;

    /**
     * 规则主键
     */
	@ExcelProperty(value = "规则主键")
	@ApiModelProperty("规则主键")
	private String ruleId;

    /**
     * 规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM
     */
	@ExcelProperty(value = "规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM")
	@ApiModelProperty("规则类型，提交日期：DATE、固定字符：STR、表单字段：FD、流水号：SNUM")
	private String ruleType;

    /**
     * 规则值，对应规则类型，如YYYY，其中流水号的值为空
     */
	@ExcelProperty(value = "规则值，对应规则类型，如YYYY，其中流水号的值为空")
	@ApiModelProperty("规则值，对应规则类型，如YYYY，其中流水号的值为空")
	private String ruleValue;

    /**
     * 排序号
     */
	@ExcelProperty(value = "排序号")
	@ApiModelProperty("排序号")
	private Long orderBy;

	@ApiModelProperty(value = "分隔符")
	private String slicerValue;


}
