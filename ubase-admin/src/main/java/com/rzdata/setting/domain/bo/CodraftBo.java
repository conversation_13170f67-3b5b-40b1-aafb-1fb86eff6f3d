package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 合稿管理业务对象 basic_codraft
 *
 * <AUTHOR>
 * @date 2022-03-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("合稿管理业务对象")
public class CodraftBo extends BaseEntity {

    /**
     * 合稿管理ID
     */
    @ApiModelProperty(value = "合稿管理ID", required = true)
//    @NotNull(message = "合稿管理ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 合稿要素名称
     */
    @ApiModelProperty(value = "合稿要素名称", required = true)
   // @NotBlank(message = "合稿要素名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String codraftName;

    /**
     * 合稿模板
     */
    @ApiModelProperty(value = "合稿模板", required = true)
    @NotBlank(message = "合稿模板不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fileId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String codraftStatus;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
