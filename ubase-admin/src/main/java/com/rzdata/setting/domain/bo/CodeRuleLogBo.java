package com.rzdata.setting.domain.bo;

import com.rzdata.framework.core.domain.BaseEntity;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 编号规则日志业务对象 basic_code_rule_log
 *
 * <AUTHOR>
 * @date 2021-12-27
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("编号规则日志业务对象")
public class CodeRuleLogBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotBlank(message = "主键不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 规则主键
     */
    @ApiModelProperty(value = "规则主键", required = true)
    @NotBlank(message = "规则主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleId;

    /**
     * 流水值
     */
    @ApiModelProperty(value = "流水值", required = true)
    @NotNull(message = "流水值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long numberValue;

    /**
     * 规则生成值
     */
    @ApiModelProperty(value = "规则生成值", required = true)
    @NotBlank(message = "规则生成值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleValue;

    /**
     * 规则使用业务主键
     */
    @ApiModelProperty(value = "规则使用业务主键", required = true)
    @NotBlank(message = "规则使用业务主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessId;


    /**
     * 分页大小
     */
    @ApiModelProperty("分页大小")
    private Integer pageSize;

    /**
     * 当前页数
     */
    @ApiModelProperty("当前页数")
    private Integer pageNum;

    /**
     * 排序列
     */
    @ApiModelProperty("排序列")
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    @ApiModelProperty(value = "排序的方向", example = "asc,desc")
    private String isAsc;

}
