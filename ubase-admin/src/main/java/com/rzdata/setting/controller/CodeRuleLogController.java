package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.CodeRuleLogVo;
import com.rzdata.setting.domain.bo.CodeRuleLogBo;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 编号规则日志Controller
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Validated
@Api(value = "编号规则日志控制器", tags = {"编号规则日志管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/codeRuleLog")
public class CodeRuleLogController extends BaseController {

    private final ICodeRuleLogService iCodeRuleLogService;

    /**
     * 查询编号规则日志列表
     */
    @ApiOperation("查询编号规则日志列表")
    @GetMapping("/list")
    public TableDataInfo<CodeRuleLogVo> list(@Validated(QueryGroup.class) CodeRuleLogBo bo) {
        return iCodeRuleLogService.queryPageList(bo);
    }

    /**
     * 导出编号规则日志列表
     */
    @ApiOperation("导出编号规则日志列表")
    @Log(title = "编号规则日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated CodeRuleLogBo bo, HttpServletResponse response) {
        List<CodeRuleLogVo> list = iCodeRuleLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "编号规则日志", CodeRuleLogVo.class, response);
    }

    /**
     * 获取编号规则日志详细信息
     */
    @ApiOperation("获取编号规则日志详细信息")
    @GetMapping("/{id}")
    public AjaxResult<CodeRuleLogVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iCodeRuleLogService.queryById(id));
    }

    /**
     * 新增编号规则日志
     */
    @ApiOperation("新增编号规则日志")
    @Log(title = "编号规则日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody CodeRuleLogBo bo) {
        return toAjax(iCodeRuleLogService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改编号规则日志
     */
    @ApiOperation("修改编号规则日志")
    @Log(title = "编号规则日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody CodeRuleLogBo bo) {
        return toAjax(iCodeRuleLogService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除编号规则日志
     */
    @ApiOperation("删除编号规则日志")
    @Log(title = "编号规则日志" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iCodeRuleLogService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
