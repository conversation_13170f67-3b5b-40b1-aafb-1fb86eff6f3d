package com.rzdata.setting.controller;

import java.util.List;
import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.service.IDocClassFlowService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.rzdata.framework.annotation.RepeatSubmit;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.controller.BaseController;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.validate.AddGroup;
import com.rzdata.framework.core.validate.EditGroup;
import com.rzdata.framework.core.validate.QueryGroup;
import com.rzdata.framework.enums.BusinessType;
import com.rzdata.framework.utils.poi.ExcelUtil;
import com.rzdata.setting.domain.vo.DocClassFlowNodeDetailVo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;
import com.rzdata.framework.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 文件分类设置-节点明细设置Controller
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Validated
@Api(value = "文件分类设置-节点明细设置控制器", tags = {"文件分类设置-节点明细设置管理"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/setting/docClassFlowNodeDetail")
public class DocClassFlowNodeDetailController extends BaseController {

    private final IDocClassFlowNodeDetailService iDocClassFlowNodeDetailService;

    @Autowired
    private IDocClassFlowService iDocClassFlowService;


    /**
     * 根据文件分类、业务类型和环节编码，获取环节授权清单
     *
     * @param docClass
     * @param bizType
     * @param nodeCode
     * @return
     */
    @ApiOperation("根据文件分类、业务类型和环节编码，获取环节授权清单")
    @GetMapping("/getAuth/{docClass}/{bizType}/{nodeCode}")
    public AjaxResult<List<DocClassFlowNodeDetail>> getInfo(@ApiParam("文件分类")
                                              @NotNull(message = "文件分类不能为空")
                                              @PathVariable("docClass") String docClass,
                                                            @ApiParam("业务类型")
                                                            @NotNull(message = "业务类型不能为空")
                                                            @PathVariable("bizType") String bizType,
                                              @ApiParam("流程节点编码")
                                              @NotNull(message = "流程节点编码不能为空")
                                              @PathVariable("nodeCode") String nodeCode) {
        List<DocClassFlowNodeDetail> result = null;
        DocClassFlowVo flowObj = iDocClassFlowService.getByUpDocClassAndBizType(docClass,bizType,"");
        if(flowObj != null) {
            // 已经找到相应的文件分类的流程配置--> 再获取环节授权清单
            result = iDocClassFlowNodeDetailService.queryList(flowObj.getId(),nodeCode);
        }
        return AjaxResult.success(result);
    }


    /**
     * 查询文件分类设置-节点明细设置列表
     */
    @ApiOperation("查询文件分类设置-节点明细设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:list')")
    @GetMapping("/list")
    public TableDataInfo<DocClassFlowNodeDetailVo> list(@Validated(QueryGroup.class) DocClassFlowNodeDetailBo bo) {
        return iDocClassFlowNodeDetailService.queryPageList(bo);
    }

    /**
     * 导出文件分类设置-节点明细设置列表
     */
    @ApiOperation("导出文件分类设置-节点明细设置列表")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:export')")
    @Log(title = "文件分类设置-节点明细设置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(@Validated DocClassFlowNodeDetailBo bo, HttpServletResponse response) {
        List<DocClassFlowNodeDetailVo> list = iDocClassFlowNodeDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件分类设置-节点明细设置", DocClassFlowNodeDetailVo.class, response);
    }

    /**
     * 获取文件分类设置-节点明细设置详细信息
     */
    @ApiOperation("获取文件分类设置-节点明细设置详细信息")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:query')")
    @GetMapping("/{id}")
    public AjaxResult<DocClassFlowNodeDetailVo> getInfo(@ApiParam("主键")
                                                  @NotNull(message = "主键不能为空")
                                                  @PathVariable("id") String id) {
        return AjaxResult.success(iDocClassFlowNodeDetailService.queryById(id));
    }

    /**
     * 新增文件分类设置-节点明细设置
     */
    @ApiOperation("新增文件分类设置-节点明细设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:add')")
    @Log(title = "文件分类设置-节点明细设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public AjaxResult<Void> add(@Validated(AddGroup.class) @RequestBody DocClassFlowNodeDetailBo bo) {
        return toAjax(iDocClassFlowNodeDetailService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改文件分类设置-节点明细设置
     */
    @ApiOperation("修改文件分类设置-节点明细设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:edit')")
    @Log(title = "文件分类设置-节点明细设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public AjaxResult<Void> edit(@Validated(EditGroup.class) @RequestBody DocClassFlowNodeDetailBo bo) {
        return toAjax(iDocClassFlowNodeDetailService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除文件分类设置-节点明细设置
     */
    @ApiOperation("删除文件分类设置-节点明细设置")
    @PreAuthorize("@ss.hasPermi('setting:docClassFlowNodeDetail:remove')")
    @Log(title = "文件分类设置-节点明细设置" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] ids) {
        return toAjax(iDocClassFlowNodeDetailService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
