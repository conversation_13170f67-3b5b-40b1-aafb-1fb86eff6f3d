package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.CodeRuleLogBo;
import com.rzdata.setting.domain.vo.CodeRuleLogVo;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.mapper.CodeRuleLogMapper;
import com.rzdata.setting.service.ICodeRuleLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 编号规则日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class CodeRuleLogServiceImpl extends ServicePlusImpl<CodeRuleLogMapper, CodeRuleLog, CodeRuleLogVo> implements ICodeRuleLogService {

    @Override
    public CodeRuleLogVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<CodeRuleLogVo> queryPageList(CodeRuleLogBo bo) {
        PagePlus<CodeRuleLog, CodeRuleLogVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleLogVo> queryList(CodeRuleLogBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRuleLog> buildQueryWrapper(CodeRuleLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRuleLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), CodeRuleLog::getRuleId, bo.getRuleId());
        lqw.eq(bo.getNumberValue() != null, CodeRuleLog::getNumberValue, bo.getNumberValue());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleValue()), CodeRuleLog::getRuleValue, bo.getRuleValue());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessId()), CodeRuleLog::getBusinessId, bo.getBusinessId());
        return lqw;
    }

    @Override
    public Boolean insertByBo(CodeRuleLogBo bo) {
        CodeRuleLog add = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(CodeRuleLogBo bo) {
        CodeRuleLog update = BeanUtil.toBean(bo, CodeRuleLog.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRuleLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public CodeRuleLog selectLogByValue(String ruleId, String businessId, String ruleValue, Long numberValue) {
        return this.baseMapper.selectLogByValue(ruleId, businessId, ruleValue, numberValue);
    }


}
