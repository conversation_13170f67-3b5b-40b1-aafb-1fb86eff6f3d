package com.rzdata.setting.service;

import cn.hutool.core.lang.tree.Tree;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.rmi.ServerException;
import java.util.Collection;
import java.util.List;

/**
 * 文件类型Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface IDocClassService extends IServicePlus<DocClass, DocClassVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassVo> queryPageList(DocClassBo bo);

	/**
	 * 构建前端所需要下拉树结构
	 */
	List<Tree<String>> buildTreeSelect(List<DocClassVo> docClassList);

	/**
	 * 查询列表
	 */
	List<DocClassVo> queryList(DocClassBo bo);

	/**
	 * 根据新增业务对象插入文件类型
	 * @param bo 文件类型新增业务对象
	 * @return
	 */
	String insertByBo(DocClassBo bo) throws ServerException;

	/**
	 * 根据编辑业务对象修改文件类型
	 * @param bo 文件类型编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	/**
	 * 启/禁用 1：启用，0：禁用
	 * @param id
	 * @param status
	 */
	void revDisable(String id, String status);

	Integer validateZf(String versionId,String docId);
}
