package com.rzdata.setting.service;

import com.rzdata.setting.domain.CodeRuleDetail;
import com.rzdata.setting.domain.vo.CodeRuleDetailVo;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 编号规则明细Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface ICodeRuleDetailService extends IServicePlus<CodeRuleDetail, CodeRuleDetailVo> {
	/**
	 * 查询单个
	 * @return
	 */
	CodeRuleDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<CodeRuleDetailVo> queryPageList(CodeRuleDetailBo bo);

	/**
	 * 查询列表
	 */
	List<CodeRuleDetailVo> queryList(CodeRuleDetailBo bo);

	/**
	 * 根据新增业务对象插入编号规则明细
	 * @param bo 编号规则明细新增业务对象
	 * @return
	 */
	Boolean insertByBo(CodeRuleDetailBo bo);

	/**
	 * 根据编辑业务对象修改编号规则明细
	 * @param bo 编号规则明细编辑业务对象
	 * @return
	 */
	Boolean updateByBo(CodeRuleDetailBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
