package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.blueland.bpmclient.model.ActivityDefinitionModel;
import com.blueland.bpmclient.model.ProcessDefinitionModel;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeDetailVo;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.service.IDocClassFlowNodeDetailService;
import com.rzdata.setting.service.IDocClassFlowService;
import com.rzdata.system.service.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.DocClassFlowNodeBo;
import com.rzdata.setting.domain.vo.DocClassFlowNodeVo;
import com.rzdata.setting.domain.DocClassFlowNode;
import com.rzdata.setting.mapper.DocClassFlowNodeMapper;
import com.rzdata.setting.service.IDocClassFlowNodeService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件分类设置-流程节点设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Slf4j
@Service
public class DocClassFlowNodeServiceImpl extends ServicePlusImpl<DocClassFlowNodeMapper, DocClassFlowNode, DocClassFlowNodeVo> implements IDocClassFlowNodeService {

    @Resource
    private IDocClassFlowService iDocClassFlowService;

    @Resource
    private WorkflowService workflowService;

    @Resource
    private IDocClassFlowNodeDetailService classFlowNodeDetailService;


    @Override
    public DocClassFlowNodeVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassFlowNodeVo> queryPageList(DocClassFlowNodeBo bo) {
        PagePlus<DocClassFlowNode, DocClassFlowNodeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassFlowNodeVo> queryList(DocClassFlowNodeBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassFlowNode> buildQueryWrapper(DocClassFlowNodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClassFlowNode> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), DocClassFlowNode::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getFlowId()), DocClassFlowNode::getFlowId, bo.getFlowId());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeCode()), DocClassFlowNode::getNodeCode, bo.getNodeCode());
        lqw.like(StringUtils.isNotBlank(bo.getNodeName()), DocClassFlowNode::getNodeName, bo.getNodeName());
        lqw.eq(StringUtils.isNotBlank(bo.getPageMode()), DocClassFlowNode::getPageMode, bo.getPageMode());
        lqw.eq(StringUtils.isNotBlank(bo.getAppendixMode()), DocClassFlowNode::getAppendixMode, bo.getAppendixMode());
        lqw.eq(StringUtils.isNotBlank(bo.getModuleConfig()), DocClassFlowNode::getModuleConfig, bo.getModuleConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getDeleteFlag()), DocClassFlowNode::getDeleteFlag, bo.getDeleteFlag());
        lqw.eq(bo.getSort() != null, DocClassFlowNode::getSort, bo.getSort());
        lqw.orderByAsc(DocClassFlowNode::getSort);
        return lqw;
    }

    @Override
    public Boolean insertByBo(DocClassFlowNodeBo bo) {
        DocClassFlowNode add = BeanUtil.toBean(bo, DocClassFlowNode.class);
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(DocClassFlowNodeBo bo) {
        DocClassFlowNode update = BeanUtil.toBean(bo, DocClassFlowNode.class);
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClassFlowNode entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public AjaxResult<List<DocClassFlowNodeVo>> syncFlowPlatNodeList(String docClass, String bizType, String flowKey, String flowId) {
        List<DocClassFlowNodeVo> result = new ArrayList<>();
        try {
            ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(flowKey);
            if(processDefinitionModel != null) {
                List<ActivityDefinitionModel> defModelList = workflowService.getActDef(processDefinitionModel.getProcDefId());
                if(defModelList != null && defModelList.size() > 0) { // 流程平台模型，有配置环节
                    // 构造流程节点清单
                    boolean isAppendAuthList = StringUtils.isNotEmpty(flowId) ? true : false;
                    for(ActivityDefinitionModel defModel : defModelList) {
                        if("endEvent".equals(defModel.getActDefType())) {
                            continue; // 结束节点，忽略
                        }
                        DocClassFlowNodeVo newNodeObj = new DocClassFlowNodeVo();
                        newNodeObj.setTenantId("1");
                        newNodeObj.setNodeCode(defModel.getActDefId());
                        newNodeObj.setNodeName(defModel.getActDefName());
                        newNodeObj.setDeleteFlag("N");
                        newNodeObj.setSort(defModel.getActDefOrder().longValue());
                        if(isAppendAuthList) {
                            DocClassFlowNodeVo nodeObj = this.getVoOne(new QueryWrapper<>(new DocClassFlowNode().setFlowId(flowId).setNodeCode(defModel.getActDefId())));
                            if(nodeObj != null) {
                                List<DocClassFlowNodeDetailVo> nodeDetailList = this.classFlowNodeDetailService.listVo(new QueryWrapper<>(new DocClassFlowNodeDetail().setNodeId(nodeObj.getId())));
                                newNodeObj.setNodeDetailList(nodeDetailList);
                            }
                        }
                        result.add(newNodeObj);
                    }
                }
            }
        } catch (Exception e) {
            log.error("syncFlowPlatNodeList异常",e);
        }
        return AjaxResult.success(result);
    }

    @Override
    public Map<String,String> compareFlowPlatNodeList(String flowKey, String flowId) {
        Map<String,String> result = new HashMap<>(); // 默认没有变更
        String delNodes = "";
        String addNodes = "";
        try {
            ProcessDefinitionModel processDefinitionModel = workflowService.getProcessDefinitionModel(flowKey);
            if(processDefinitionModel != null) {
                List<ActivityDefinitionModel> defModelList = workflowService.getActDef(processDefinitionModel.getProcDefId());
                // 构造和保存分类流程节点清单
                if(defModelList != null && defModelList.size() > 0) {
                    List<DocClassFlowNode> nodeList = this.list(new QueryWrapper<>(new DocClassFlowNode().setFlowId(flowId)));
                    // 判断流程平台的环节，是否在现有分类流程环节中可以找到
                    for(ActivityDefinitionModel defModel : defModelList) {
                        if("endEvent".equals(defModel.getActDefType())) {
                            continue;
                        }
                        List<DocClassFlowNode> existList = nodeList.stream().filter(s->s.getNodeCode().equals(defModel.getActDefId())).collect(Collectors.toList());
                        if(existList == null || existList.size() == 0) { // 未找到
                            if(StringUtils.isNotEmpty(addNodes)) {
                                addNodes += ",";
                            }
                            addNodes += defModel.getActDefName(); // 待新增-节点名称
                        }
                    }
                    // 判断体系文件分类流程配置的环节，是否在现有流程平台模型环节中可以找到
                    for(DocClassFlowNode nodeObj : nodeList) {
                        List<ActivityDefinitionModel> existList = defModelList.stream().filter(s->s.getActDefId().equals(nodeObj.getNodeCode())).collect(Collectors.toList());
                        if(existList == null || existList.size() == 0) { // 未找到
                            if(StringUtils.isNotEmpty(delNodes)) {
                                delNodes += ",";
                            }
                            delNodes += nodeObj.getNodeName(); // 待删除-节点名称
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("compareFlowPlatNodeList异常",e);
        }
        result.put("addNodes",addNodes);
        result.put("delNodes",delNodes);
        return result;
    }


    @Override
    public void removeFlowNodeCascade(String flowId) {
        List<DocClassFlowNode> nodeList = this.list(new QueryWrapper<>(new DocClassFlowNode().setFlowId(flowId)));
        if(nodeList != null) {
            for(DocClassFlowNode nodeObj : nodeList) {
                // 删除流程环节下所有的权限按钮配置
                this.classFlowNodeDetailService.remove(new QueryWrapper<>(new DocClassFlowNodeDetail().setNodeId(nodeObj.getId())));
                // 删除该文件分类下的所有流程环节
                this.removeById(nodeObj.getId());
            }
        }
    }

}
