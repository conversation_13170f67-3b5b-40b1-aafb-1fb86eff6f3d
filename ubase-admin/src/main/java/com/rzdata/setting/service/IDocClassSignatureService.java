package com.rzdata.setting.service;

import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.setting.domain.DocClassSignature;
import com.rzdata.setting.domain.bo.DocClassMergeBo;
import com.rzdata.setting.domain.bo.DocClassSignatureBo;
import com.rzdata.setting.domain.vo.DocClassMergeVo;
import com.rzdata.setting.domain.vo.DocClassSignatureVo;

import java.util.Collection;
import java.util.List;

/**
 * 编号规则日志Service接口
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public interface IDocClassSignatureService extends IServicePlus<DocClassSignature, DocClassSignatureVo> {

	/**
	 * 查询单个
	 * @return
	 */
	DocClassSignatureVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassSignatureVo> queryPageList(DocClassSignatureBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassSignatureVo> queryList(DocClassSignatureBo bo);

	/**
	 * 根据新增业务对象插入编号规则日志
	 * @param bo 编号规则日志新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassMergeBo bo);

	/**
	 * 根据新增业务对象插入编号规则日志
	 * @param boList 编号规则日志新增业务对象
	 * @return
	 */
	Boolean insertOrUpdateByBoList(List<DocClassSignatureBo> boList);

	/**
	 * 根据编辑业务对象修改编号规则日志
	 * @param bo 编号规则日志编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassSignatureBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

}
