package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassMerge;
import com.rzdata.setting.domain.bo.DocClassMergeBo;
import com.rzdata.setting.domain.vo.DocClassMergeVo;
import com.rzdata.setting.mapper.DocClassMergeMapper;
import com.rzdata.setting.service.IDocClassMergeService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class DocClassMergeServiceImpl extends ServicePlusImpl<DocClassMergeMapper, DocClassMerge, DocClassMergeVo> implements IDocClassMergeService {


    @Override
    public DocClassMergeVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassMergeVo> queryPageList(DocClassMergeBo bo) {
        PagePlus<DocClassMerge, DocClassMergeVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassMergeVo> queryList(DocClassMergeBo bo) {
        return listVo(buildQueryWrapper(bo));
    }
    private LambdaQueryWrapper<DocClassMerge> buildQueryWrapper(DocClassMergeBo bo) {
        LambdaQueryWrapper<DocClassMerge> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocClassMerge::getDocClass, bo.getDocClass());
        lqw.orderByDesc(DocClassMerge:: getCreateTime);
        return lqw;
    }
    @Override
    public Boolean insertByBo(DocClassMergeBo bo) {
        DocClassMerge update = BeanUtil.toBean(bo, DocClassMerge.class);
        return save(update);
    }

    @Override
    public Boolean insertOrUpdateByBoList(List<DocClassMergeBo> boList) {
        List<DocClassMerge> list = boList.stream().map(bo->{
            DocClassMerge docClassMerge = BeanUtil.toBean(bo, DocClassMerge.class);
            //docClassMerge.setCreateTime(new Date());
            docClassMerge.setUpdateTime(new Date());
            return docClassMerge;
        }).collect(Collectors.toList());
        return saveOrUpdateAll(list);
    }

    @Override
    public Boolean updateByBo(DocClassMergeBo bo) {
        DocClassMerge update = BeanUtil.toBean(bo, DocClassMerge.class);
        update.setUpdateTime(new Date());
        return updateById(update);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }

    @Override
    public String getTemplateIdByDocClass(String docClass, String mergeFactorName) {
        return this.baseMapper.getTemplateIdByDocClass(docClass,mergeFactorName);
    }
}
