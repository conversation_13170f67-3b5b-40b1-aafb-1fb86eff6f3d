package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.setting.domain.DocClassMerge;
import com.rzdata.setting.domain.DocClassSignature;
import com.rzdata.setting.domain.bo.DocClassMergeBo;
import com.rzdata.setting.domain.bo.DocClassSignatureBo;
import com.rzdata.setting.domain.vo.DocClassMergeVo;
import com.rzdata.setting.domain.vo.DocClassSignatureVo;
import com.rzdata.setting.mapper.DocClassSignatureMapper;
import com.rzdata.setting.service.IDocClassSignatureService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/31 11:42
 * @Version 1.0
 * @Description
 */
@Service
public class DocClassSignatureServiceImpl extends ServicePlusImpl<DocClassSignatureMapper, DocClassSignature, DocClassSignatureVo> implements IDocClassSignatureService {

    @Override
    public DocClassSignatureVo queryById(String id) {
        return getVoById(id);
    }

    @Override
    public TableDataInfo<DocClassSignatureVo> queryPageList(DocClassSignatureBo bo) {
        PagePlus<DocClassSignature, DocClassSignatureVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<DocClassSignatureVo> queryList(DocClassSignatureBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClassSignature> buildQueryWrapper(DocClassSignatureBo bo) {
        LambdaQueryWrapper<DocClassSignature> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDocClass()), DocClassSignature::getDocClass, bo.getDocClass());
        lqw.orderByDesc(DocClassSignature:: getCreateTime);
        return lqw;
    }
    @Override
    public Boolean insertByBo(DocClassMergeBo bo) {
        DocClassSignature update = BeanUtil.toBean(bo, DocClassSignature.class);
        return save(update);
    }

    @Override
    public Boolean insertOrUpdateByBoList(List<DocClassSignatureBo> boList) {
        List<DocClassSignature> list = boList.stream().map(bo->{
            DocClassSignature docClassSignature = BeanUtil.toBean(bo, DocClassSignature.class);
            //docClassMerge.setCreateTime(new Date());
            docClassSignature.setUpdateTime(new Date());
            return docClassSignature;
        }).collect(Collectors.toList());
        return saveOrUpdateAll(list);
    }

    @Override
    public Boolean updateByBo(DocClassSignatureBo bo) {
        DocClassSignature update = BeanUtil.toBean(bo, DocClassSignature.class);
        update.setUpdateTime(new Date());
        return updateById(update);
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return null;
    }
}
