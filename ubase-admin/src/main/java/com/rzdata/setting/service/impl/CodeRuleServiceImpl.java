package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.constant.CycleConstants;
import com.rzdata.framework.constant.NumberConstants;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.exception.ServiceException;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.domain.Standard;
import com.rzdata.process.domain.bo.ModifyApplyLinkBo;
import com.rzdata.process.domain.vo.DocNoVo;
import com.rzdata.process.domain.vo.ModifyApplyLinkVo;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyLinkService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.process.service.IStandardService;
import com.rzdata.setting.domain.CodeRule;
import com.rzdata.setting.domain.CodeRuleDetail;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.DocClass;
import com.rzdata.setting.domain.bo.CodeRuleBo;
import com.rzdata.setting.domain.bo.CodeRuleDetailBo;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import com.rzdata.setting.domain.bo.CreateNoBo;
import com.rzdata.setting.domain.vo.CodeRuleVo;
import com.rzdata.setting.domain.vo.DocClassVo;
import com.rzdata.setting.mapper.CodeRuleMapper;
import com.rzdata.setting.service.ICodeRuleDetailService;
import com.rzdata.setting.service.ICodeRuleLogService;
import com.rzdata.setting.service.ICodeRuleService;
import com.rzdata.setting.service.IDocClassService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 编号规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Slf4j
@Service
public class CodeRuleServiceImpl extends ServicePlusImpl<CodeRuleMapper, CodeRule, CodeRuleVo> implements ICodeRuleService {

    static final String RU_TYPE_DATE = "DATE";
    static final String RU_TYPE_STR = "STR";
    static final String RU_TYPE_FD = "FORM";
    static final String RU_TYPE_SNUM = "SNUM";
    static final String CYCLE_Y = "Y";
    static final String CYCLE_M = "M";
    static final String CYCLE_D = "D";
    static final String CYCLE_B = "B";
    static final String CYCLE_NO = "NO";
    @Autowired
    ICodeRuleDetailService iCodeRuleDetailService;

    @Autowired
    ICodeRuleLogService iCodeRuleLogService;

    @Autowired
    IDocClassService iDocClassService;

    @Autowired
    IStandardService iStandardService;

    @Autowired
    IModifyApplyService iModifyApplyService;

    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;

    @Autowired
    private IStandardService standardService;

    @Autowired
    private IGenerateIdService generateIdService;

    @Override
    public CodeRuleVo queryById(String id) {
        CodeRuleVo vo = getVoById(id);
        if (ObjectUtil.isNotEmpty(vo)) {
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, id);
            vo.setRuleDetailList(iCodeRuleDetailService.list(queryWrapper));
        }
        return vo;
    }

    @Override
    public TableDataInfo<CodeRuleVo> queryPageList(CodeRuleBo bo) {
        PagePlus<CodeRule, CodeRuleVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<CodeRuleVo> queryList(CodeRuleBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<CodeRule> buildQueryWrapper(CodeRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CodeRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), CodeRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getResetCycle()), CodeRule::getResetCycle, bo.getResetCycle());
        lqw.eq(bo.getNumberInitValue() != null, CodeRule::getNumberInitValue, bo.getNumberInitValue());
        lqw.eq(bo.getNumberDigit() != null, CodeRule::getNumberDigit, bo.getNumberDigit());
        return lqw;
    }

    @Override
    public String insertByBo(CodeRuleBo bo) {
        CodeRule add = BeanUtil.toBean(bo, CodeRule.class);
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
            handleRuleDetail(bo);
        }
        return add.getId();
    }

    /**
     * 处理详情
     *
     * @param bo
     */
    private void handleRuleDetail(CodeRuleBo bo) {
        if (ObjectUtil.isNotEmpty(bo.getId())) {
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, bo.getId());
            iCodeRuleDetailService.remove(queryWrapper);
            if (ObjectUtil.isNotEmpty(bo.getRuleDetailList())) {
                for (CodeRuleDetailBo codeRuleDetailBo : bo.getRuleDetailList()) {
                    codeRuleDetailBo.setRuleId(bo.getId());
                    iCodeRuleDetailService.insertByBo(codeRuleDetailBo);
                }
            }
        }
    }

    @Override
    public Boolean updateByBo(CodeRuleBo bo) {
        CodeRule update = BeanUtil.toBean(bo, CodeRule.class);
        validEntityBeforeSave(update);
        update.setUpdateTime(new Date());
        handleRuleDetail(bo);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(CodeRule entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        for (String id : ids) {
            QueryWrapper<DocClass> docClassQueryWrapper = new QueryWrapper<>();
            docClassQueryWrapper.lambda().eq(DocClass::getCodeId, id);
            if (iDocClassService.count(docClassQueryWrapper) > 0) {
                throw new ServiceException("已使用的编号规则不能删除");
            }
            QueryWrapper<CodeRuleDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CodeRuleDetail::getRuleId, id);
            iCodeRuleDetailService.remove(queryWrapper);
        }
        return removeByIds(ids);
    }

    public static void main(String[] args) {
        Integer initValue = 99;
        int num = 2;
        while (initValue.toString().length()>=num) {
            num+=1;
        }
        System.out.println("num = " + num);
    }





    @Override
    public Boolean checkNoIsExist(String busId, String newNo) {
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(busId), CodeRuleLog::getBusinessId, busId);
        queryWrapper.lambda().eq(StringUtils.isNotBlank(newNo), CodeRuleLog::getRuleValue, newNo);
        return iCodeRuleLogService.count(queryWrapper) > 0 ? true : false;
    }

    @Override
    public void removeByRule(String busId, String newNo) {
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(busId), CodeRuleLog::getBusinessId, busId);
        queryWrapper.lambda().eq(CodeRuleLog::getRuleValue, newNo);
        iCodeRuleLogService.remove(queryWrapper);
    }



    /**
     * 获取最大流水号
     * @param codeRule
     * @param businessId
     * @return
     */
    private CodeRuleLog getMaxSerialNumber(CodeRule codeRule, String businessId){
        CodeRuleLog query = CodeRuleLog.builder().ruleId(codeRule.getId()).build();
        if (CYCLE_B.equalsIgnoreCase(codeRule.getResetCycle())) {
            query.setBusinessId(businessId);
        }
        QueryWrapper<CodeRuleLog> queryWrapper = new QueryWrapper<CodeRuleLog>(query);
        queryWrapper.lambda().orderByDesc(CodeRuleLog::getNumberValue);
        queryWrapper.last("limit 1");
        CodeRuleLog codeRuleLog = iCodeRuleLogService.getOne(queryWrapper);
        if(codeRuleLog==null){
            codeRuleLog=CodeRuleLog.builder().ruleId(codeRule.getId()).businessId(businessId).
                    numberValue(codeRule.getNumberInitValue()).createTime(DateUtil.date()).build();
        }
        else{
            String nowDate = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            long numberValue = Long.valueOf(codeRuleLog.getNumberValue()+1);
            String resetDate = DateUtil.format(codeRuleLog.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
            if (CYCLE_D.equalsIgnoreCase(codeRule.getResetCycle())) {
                // 按照天重置
                if (!resetDate.substring(0, 10).equals(nowDate.substring(0, 10))) {
                    numberValue = 1;
                }
            } else if (CYCLE_M.equalsIgnoreCase(codeRule.getResetCycle())) {
                // 按照月重置
                if (!resetDate.substring(0, 7).equals(nowDate.substring(0, 7))) {
                    numberValue = 1;
                }
            } else if (CYCLE_Y.equalsIgnoreCase(codeRule.getResetCycle())) {
                // 按照年重置
                if (!resetDate.substring(0, 4).equals(nowDate.substring(0, 4))) {
                    numberValue = 1;
                }
            }
            else if (CYCLE_B.equalsIgnoreCase(codeRule.getResetCycle())) {
                while(true){
                    queryWrapper = new QueryWrapper<CodeRuleLog>(CodeRuleLog.builder().ruleId(codeRule.getId()).businessId(businessId).numberValue(numberValue).build());
                    if(iCodeRuleLogService.count(queryWrapper)>0){
                        numberValue++;
                    }
                    else{
                        break;
                    }
                }
            }
            else{
                while(true){
                    queryWrapper = new QueryWrapper<CodeRuleLog>(CodeRuleLog.builder().ruleId(codeRule.getId()).numberValue(numberValue).build());
                    if(iCodeRuleLogService.count(queryWrapper)>0){
                        numberValue++;
                    }
                    else{
                        break;
                    }
                }
            }

            codeRuleLog.setId(null);
            codeRuleLog.setBusinessId(businessId);
            codeRuleLog.setCreateTime(DateUtil.date());
            codeRuleLog.setNumberValue(numberValue);
        }
        iCodeRuleLogService.save(codeRuleLog);
        return codeRuleLog;
    }


    @Override
    public CodeRuleLog generatorEncodingRule(String ruleId, String businessId, Map<String, Object> bizMap) {
        CodeRule codeRule = this.baseMapper.selectById(ruleId);
        List<CodeRuleLog> list = iCodeRuleLogService.list(new QueryWrapper<>(CodeRuleLog.builder().ruleId(ruleId).businessId(businessId).build()));
        if(list.size() > 0 && !CYCLE_B.equalsIgnoreCase(codeRule.getResetCycle())){//如果当前业务表单对应的规则已经生成了编号，则直接返回
            return list.get(0);
        }
        CodeRuleLog codeRuleLog = null;
        QueryWrapper<CodeRuleDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.lambda().eq(CodeRuleDetail::getRuleId, codeRule.getId());
        detailQueryWrapper.lambda().orderByAsc(CodeRuleDetail::getOrderBy);
        List<CodeRuleDetail> details = iCodeRuleDetailService.list(detailQueryWrapper);
        StringBuffer snNumberBuffer = new StringBuffer();
        for(CodeRuleDetail detail:details){
            String suffixValue = "";
            String ruType = detail.getRuleType();// 规则类型
            String ruVal = detail.getRuleValue();// 规则值
            if (ruType.equalsIgnoreCase(RU_TYPE_FD) && bizMap.containsKey(ruVal)) {
                suffixValue = (String) bizMap.get(ruVal);
            }
            else if (ruType.equalsIgnoreCase(RU_TYPE_DATE)){
                suffixValue = DateUtil.format(DateUtil.date(), ruVal);
            }
            else if (ruType.equalsIgnoreCase(RU_TYPE_STR)){
                suffixValue = ruVal;
            }
            else if (ruType.equalsIgnoreCase(RU_TYPE_SNUM)){
                codeRuleLog = this.getMaxSerialNumber(codeRule,businessId);
                suffixValue = String.format("%0"+codeRule.getNumberDigit()+"d",codeRuleLog.getNumberValue());
            }
            snNumberBuffer.append(suffixValue);
            if(StrUtil.isNotBlank(detail.getSlicerValue())){
                snNumberBuffer.append(detail.getSlicerValue());
            }
        }
        if(codeRuleLog==null){
            codeRuleLog=CodeRuleLog.builder().ruleId(codeRule.getId()).ruleValue(snNumberBuffer.toString()).businessId(snNumberBuffer.toString()).createTime(DateUtil.date()).build();
            iCodeRuleLogService.save(codeRuleLog);
        }
        else{
            codeRuleLog.setRuleValue(snNumberBuffer.toString());
            if(StrUtil.isBlank(codeRuleLog.getBusinessId())){
                codeRuleLog.setBusinessId(codeRuleLog.getRuleValue());
            }
            iCodeRuleLogService.updateById(codeRuleLog);
        }

        return codeRuleLog;
    }
}
