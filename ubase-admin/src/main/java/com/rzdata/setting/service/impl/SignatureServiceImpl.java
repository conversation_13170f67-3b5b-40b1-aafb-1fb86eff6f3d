package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
    import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.setting.domain.bo.SignatureBo;
import com.rzdata.setting.domain.vo.SignatureVo;
import com.rzdata.setting.domain.Signature;
import com.rzdata.setting.mapper.SignatureMapper;
import com.rzdata.setting.service.ISignatureService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 签章管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-03-04
 */
@Service
public class SignatureServiceImpl extends ServicePlusImpl<SignatureMapper, Signature, SignatureVo> implements ISignatureService {

    @Override
    public SignatureVo queryById(String id){
        return getVoById(id);
    }

    @Override
    public TableDataInfo<SignatureVo> queryPageList(SignatureBo bo) {
        PagePlus<Signature, SignatureVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<SignatureVo> queryList(SignatureBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<Signature> buildQueryWrapper(SignatureBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Signature> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSignatureName()), Signature::getSignatureName, bo.getSignatureName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), Signature::getFileId, bo.getFileId());
        lqw.eq(StringUtils.isNotBlank(bo.getSignatureStatus()), Signature::getSignatureStatus, bo.getSignatureStatus());
        lqw.like(StringUtils.isNotBlank(bo.getTemplateName()), Signature::getTemplateName, bo.getTemplateName());
        return lqw;
    }

    @Override
    public Boolean insertByBo(SignatureBo bo) {
        Signature add = BeanUtil.toBean(bo, Signature.class);
        add.setCreateTime(new Date());
        validEntityBeforeSave(add);
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    @Override
    public Boolean updateByBo(SignatureBo bo) {
        Signature update = BeanUtil.toBean(bo, Signature.class);
        update.setUpdateTime(new Date());
        validEntityBeforeSave(update);
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Signature entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return removeByIds(ids);
    }
}
