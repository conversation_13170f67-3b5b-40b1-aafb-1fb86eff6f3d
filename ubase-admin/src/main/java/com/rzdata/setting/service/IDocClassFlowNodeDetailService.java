package com.rzdata.setting.service;

import com.rzdata.setting.domain.DocClassFlowNodeDetail;
import com.rzdata.setting.domain.vo.DocClassFlowNodeDetailVo;
import com.rzdata.setting.domain.bo.DocClassFlowNodeDetailBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件分类设置-节点明细设置Service接口
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface IDocClassFlowNodeDetailService extends IServicePlus<DocClassFlowNodeDetail, DocClassFlowNodeDetailVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassFlowNodeDetailVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassFlowNodeDetailVo> queryPageList(DocClassFlowNodeDetailBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassFlowNodeDetailVo> queryList(DocClassFlowNodeDetailBo bo);

	/**
	 * 根据文件分类流程配置ID和流程环节，获取环节授权明细
	 */
	List<DocClassFlowNodeDetail> queryList(String flowId,String nodeCode);

	/**
	 * 根据新增业务对象插入文件分类设置-节点明细设置
	 * @param bo 文件分类设置-节点明细设置新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassFlowNodeDetailBo bo);

	/**
	 * 根据编辑业务对象修改文件分类设置-节点明细设置
	 * @param bo 文件分类设置-节点明细设置编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassFlowNodeDetailBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
