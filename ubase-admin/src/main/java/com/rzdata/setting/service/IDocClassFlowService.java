package com.rzdata.setting.service;

import com.rzdata.framework.core.domain.entity.SysDictData;
import com.rzdata.setting.domain.DocClassFlow;
import com.rzdata.setting.domain.vo.DocClassFlowVo;
import com.rzdata.setting.domain.bo.DocClassFlowBo;
import com.rzdata.framework.core.mybatisplus.core.IServicePlus;
import com.rzdata.framework.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 文件分类设置-流程设置Service接口
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface IDocClassFlowService extends IServicePlus<DocClassFlow, DocClassFlowVo> {
	/**
	 * 查询单个
	 * @return
	 */
	DocClassFlowVo queryById(String id);

	/**
	 * 查询列表
	 */
    TableDataInfo<DocClassFlowVo> queryPageList(DocClassFlowBo bo);

	/**
	 * 查询列表
	 */
	List<DocClassFlowVo> queryList(DocClassFlowBo bo);

	/**
	 * 根据新增业务对象插入文件分类设置-流程设置
	 * @param bo 文件分类设置-流程设置新增业务对象
	 * @return
	 */
	Boolean insertByBo(DocClassFlowBo bo);

	/**
	 * 根据编辑业务对象修改文件分类设置-流程设置
	 * @param bo 文件分类设置-流程设置编辑业务对象
	 * @return
	 */
	Boolean updateByBo(DocClassFlowBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);


	/**
	 * @param vo
	 * @return
	 */
	DocClassFlowVo buildDeepClassFlowData(DocClassFlowVo vo);

	/**
	 * 文件分类流程相关配置-物理删除
	 *
	 * @param list
	 */
	void removeClassFlowCascade(List<DocClassFlowBo> list);

	/**
	 * 文件分类流程相关配置-新增保存
	 *
	 * @param list
	 */
	void insertClassFlowCascade(List<DocClassFlowBo> list);

	/**
	 * 根据文件分类和业务类型，获取文件分类流程配置（向上级联）
	 *
	 * @param docClass
	 * @param bizType
	 * @param applyFlag
	 * @return
	 */
	DocClassFlowVo getByUpDocClassAndBizType(String docClass,String bizType,String applyFlag);


	List<SysDictData> queryFlowList(String key);

}
