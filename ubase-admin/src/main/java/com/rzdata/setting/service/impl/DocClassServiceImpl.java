package com.rzdata.setting.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rzdata.framework.core.mybatisplus.core.ServicePlusImpl;
import com.rzdata.framework.core.page.PagePlus;
import com.rzdata.framework.core.page.TableDataInfo;
import com.rzdata.framework.utils.PageUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.enums.MergeTypeEnum;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.setting.domain.*;
import com.rzdata.setting.domain.bo.DocClassBo;
import com.rzdata.setting.domain.bo.DocClassMergeBo;
import com.rzdata.setting.domain.bo.DocClassSignatureBo;
import com.rzdata.setting.domain.vo.*;
import com.rzdata.setting.mapper.DocClassMapper;
import com.rzdata.setting.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.util.*;

/**
 * 文件类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Service
public class DocClassServiceImpl extends ServicePlusImpl<DocClassMapper, DocClass, DocClassVo> implements IDocClassService {

    @Autowired
    IBasicFileService iBasicFileService;

    @Resource
    IDocClassMergeService iDocClassMergeService;

    @Resource
    IDocClassSignatureService iDocClassSignatureService;

    @Resource
    ICodraftService codraftService;

    @Resource
    ISignatureService signatureService;

    @Override
    public DocClassVo queryById(String id) {
        DocClassVo vo = getVoById(id);
        if (ObjectUtil.isNotEmpty(vo)) {
            QueryWrapper<BasicFile> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BasicFile::getId, vo.getFileId());
            vo.setFileList(iBasicFileService.list(queryWrapper));
            //查出合稿设置
            LambdaQueryWrapper<DocClassMerge> query = new LambdaQueryWrapper<>();
            query.eq(DocClassMerge::getDocClass, vo.getId());
            List<DocClassMergeVo> list = iDocClassMergeService.listVo(query);
            vo.setDocClassMergeVoList(list);

            //查出签章设置
            LambdaQueryWrapper<DocClassSignature> querySignature = new LambdaQueryWrapper<>();
            querySignature.eq(DocClassSignature::getDocClass, vo.getId());
            querySignature.orderByAsc(DocClassSignature::getSort);
            List<DocClassSignatureVo> signatureList = iDocClassSignatureService.listVo(querySignature);
            vo.setDocClassSignatureVoList(signatureList);

        }

        return vo;
    }

    @Override
    public TableDataInfo<DocClassVo> queryPageList(DocClassBo bo) {
        PagePlus<DocClass, DocClassVo> result = pageVo(PageUtils.buildPagePlus(), buildQueryWrapper(bo));
        return PageUtils.buildDataInfo(result);
    }

    @Override
    public List<Tree<String>> buildTreeSelect(List<DocClassVo> docClassList) {
        //配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("label");
        List<Tree<String>> treeNodes = TreeUtil.build(docClassList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentClassId());
                    tree.setWeight(treeNode.getClassLevel());
                    tree.setName(treeNode.getClassName());
                    // 扩展属性 ...
                    tree.putExtra("classStatus", treeNode.getClassStatus());
                    tree.putExtra("classLevel", treeNode.getClassLevel());
                    tree.putExtra("expiration", treeNode.getExpiration());
                    tree.putExtra("reviewCycle", treeNode.getReviewCycle());
                    tree.putExtra("checkType", treeNode.getCheckType());
                    tree.putExtra("encryptType", treeNode.getEncryptType());
                    tree.putExtra("yNPrint", treeNode.getYNPrint());
                    tree.putExtra("yNRecyle", treeNode.getYNRecyle());
                    tree.putExtra("mergeDocConfig", treeNode.getMergeDocConfig());
                    tree.putExtra("codeId", treeNode.getCodeId());
                    tree.putExtra("fileId", treeNode.getFileId());
                    tree.putExtra("createTime", treeNode.getCreateTime());
                    tree.putExtra("updateTime", treeNode.getUpdateTime());
                });
        return treeNodes;
    }

    @Override
    public List<DocClassVo> queryList(DocClassBo bo) {
        return listVo(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<DocClass> buildQueryWrapper(DocClassBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<DocClass> lqw = Wrappers.lambdaQuery();
        //文件类型
        lqw.eq(StringUtils.isNotBlank(bo.getId()), DocClass::getId, bo.getId());
        lqw.like(StringUtils.isNotBlank(bo.getClassName()), DocClass::getClassName, bo.getClassName());
        lqw.eq(StringUtils.isNotBlank(bo.getParentClassId()), DocClass::getParentClassId, bo.getParentClassId());
        lqw.eq(StringUtils.isNotBlank(bo.getClassStatus()), DocClass::getClassStatus, bo.getClassStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getClassLevel()), DocClass::getClassLevel, bo.getClassLevel());
        lqw.eq(bo.getExpiration() != null, DocClass::getExpiration, bo.getExpiration());
        lqw.eq(bo.getReviewCycle() != null, DocClass::getReviewCycle, bo.getReviewCycle());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckType()), DocClass::getCheckType, bo.getCheckType());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptType()), DocClass::getEncryptType, bo.getEncryptType());
        lqw.eq(StringUtils.isNotBlank(bo.getYNPrint()), DocClass::getYNPrint, bo.getYNPrint());
        lqw.eq(StringUtils.isNotBlank(bo.getYNRecyle()), DocClass::getYNRecyle, bo.getYNRecyle());
        lqw.eq(StringUtils.isNotBlank(bo.getMergeDocConfig()), DocClass::getMergeDocConfig, bo.getMergeDocConfig());
        lqw.eq(StringUtils.isNotBlank(bo.getCodeId()), DocClass::getCodeId, bo.getCodeId());
        lqw.eq(StringUtils.isNotBlank(bo.getFileId()), DocClass::getFileId, bo.getFileId());
        lqw.orderByAsc(DocClass::getSort);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertByBo(DocClassBo bo) throws ServerException {
        DocClass add = BeanUtil.toBean(bo, DocClass.class);
        validEntityBeforeSave(add);
        add.setCreateTime(new Date());
        String docClass = bo.getId();
        LambdaQueryWrapper<Codraft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Codraft::getDeleteFlag, 0);
        queryWrapper.orderByDesc(Codraft::getCreateTime);
        List<CodraftVo> codraftVoList = codraftService.listVo(queryWrapper);


        String codraftId = null;
        if (codraftVoList == null) {
            throw new ServerException("未设置合稿样式!");
        }
        codraftId = codraftVoList.get(0).getId();
        if (ObjectUtil.isEmpty(bo.getParentClassId())) {
            bo.setParentClassId("0");
        }
        if (ObjectUtil.isNotEmpty(bo.getDocClassMergeBoList())) {
            iDocClassMergeService.insertOrUpdateByBoList(bo.getDocClassMergeBoList());
        } else {
            //没有值说明是新增 设置默认值
            List<DocClassMergeBo> defaultList = new ArrayList<>();
            Integer anotherPage = 1;
            Integer mergeFactor = 1;
            //生效合稿要素1
            //正文
            getDefault("正文", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //附件
            getDefault("附件", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //基础信息
            getDefault("基础信息", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //审批记录
            getDefault("审批记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //分发部门
            getDefault("分发部门", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //培训部门
            getDefault("培训部门", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //培训记录
            getDefault("培训记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //关联文件
            getDefault("关联文件", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //关联记录
            getDefault("关联记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //文件历史
            getDefault("文件历史", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //封面页
            getDefault("封面页", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);
            //目录
            getDefault("目录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.VALID, defaultList);

            //失效合稿要素1
            //正文
            getDefault("正文", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //附件
            getDefault("附件", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //基础信息
            getDefault("基础信息", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //审批记录
            getDefault("审批记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //分发部门
            getDefault("分发部门", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //培训部门
            getDefault("培训部门", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //培训记录
            getDefault("培训记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //关联文件
            getDefault("关联文件", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //关联记录
            getDefault("关联记录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //文件历史
            getDefault("文件历史", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //封面页
            getDefault("封面页", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            //目录
            getDefault("目录", docClass, codraftId, anotherPage, null, mergeFactor, MergeTypeEnum.INVALID, defaultList);

            //生效合稿要素2
            Integer defaultRange = 1;
            mergeFactor = 2;
            getDefault("页眉", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.VALID, defaultList);

            getDefault("页脚", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.VALID, defaultList);

            getDefault("底纹", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.VALID, defaultList);

            getDefault("电子章", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.VALID, defaultList);

            getDefault("电子签章", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.VALID, defaultList);

            //失效合稿要素2
            getDefault("页眉", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.INVALID, defaultList);

            getDefault("页脚", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.INVALID, defaultList);

            getDefault("底纹", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.INVALID, defaultList);

            getDefault("电子章", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.INVALID, defaultList);

            getDefault("电子签章", docClass, codraftId, null, defaultRange, mergeFactor, MergeTypeEnum.INVALID, defaultList);
            iDocClassMergeService.insertOrUpdateByBoList(defaultList);
        }

        if (ObjectUtil.isNotEmpty(bo.getDocClassSignatureBoList())) {
            iDocClassSignatureService.insertOrUpdateByBoList(bo.getDocClassSignatureBoList());
        }else {
            //没有值说明是新增 设置默认值
            LambdaQueryWrapper<Signature> querySignature = new LambdaQueryWrapper<>();
            querySignature.eq(Signature::getDeleteFlag, 0);
            querySignature.orderByDesc(Signature::getCreateTime);
            List<SignatureVo> signatureVoList = signatureService.listVo(querySignature);

            String signatureId = signatureVoList.get(0).getId();
            List<DocClassSignatureBo> defaultList = new ArrayList<>();
            getSignatureDefault("公司章",docClass,signatureId,1,1,defaultList);
            getSignatureDefault("部门章",docClass,signatureId,1,2,defaultList);
            getSignatureDefault("文件生效章",docClass,signatureId,1,3,defaultList);
            getSignatureDefault("文件失效章",docClass,signatureId,1,4,defaultList);
            iDocClassSignatureService.insertOrUpdateByBoList(defaultList);
        }
        boolean flag = save(add);
        if (flag) {
            bo.setId(add.getId() + "");
        }
        return add.getId() + "";
    }

    private void getDefault(String mergeFactorName, String docClass, String codraftId, Integer anotherPage, Integer range, Integer mergeFactor, MergeTypeEnum type, List<DocClassMergeBo> defaultList) {
        Integer defaultFlag = 1;
        //正文
        DocClassMergeBo text = new DocClassMergeBo();
        //.id(null)
        text.setMergeFactorName(mergeFactorName);
        text.setMergeFactorUse(1);
        text.setCodraftId(codraftId);
        text.setAnotherPage(anotherPage);
        text.setRangeValue(range);
        text.setType(type);
        text.setMergeFactor(mergeFactor);
        text.setDocClass(docClass);
        text.setCreateTime(new Date());
        text.setDefaultFlag(defaultFlag);
        defaultList.add(text);
    }

    private void getSignatureDefault(String signatureName, String docClass,String signatureId ,Integer range,Integer sort,List<DocClassSignatureBo> defaultList ){
        //正文
        DocClassSignatureBo signatureBo = new DocClassSignatureBo();
        signatureBo.setSort(sort);
        signatureBo.setDocClass(docClass);
        signatureBo.setSignatureFactorName(signatureName);
        signatureBo.setSignatureFactorUse(1);
        signatureBo.setSignatureId(signatureId);
        signatureBo.setRangeValue(range);
        signatureBo.setCreateTime(new Date());
        signatureBo.setDefaultFlag(1);
        defaultList.add(signatureBo);
    }
    @Override
    public Boolean updateByBo(DocClassBo bo) {
        DocClass update = BeanUtil.toBean(bo, DocClass.class);
        update.setUpdateTime(new Date());
        validEntityBeforeSave(update);
        if (ObjectUtil.isNotEmpty(bo.getDocClassMergeBoList())) {
            iDocClassMergeService.insertOrUpdateByBoList(bo.getDocClassMergeBoList());
        }
        if (ObjectUtil.isNotEmpty(bo.getDocClassSignatureBoList())) {
            iDocClassSignatureService.insertOrUpdateByBoList(bo.getDocClassSignatureBoList());
        }
        return updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(DocClass entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        LambdaQueryWrapper<DocClassMerge> queryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<DocClassSignature> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        ids.forEach(id->{
            queryWrapper.eq(DocClassMerge::getDocClass,id);
            lambdaQueryWrapper.eq(DocClassSignature::getDocClass,id);
            iDocClassMergeService.remove(queryWrapper);
            iDocClassSignatureService.remove(lambdaQueryWrapper);
        });
        return removeByIds(ids);
    }

    @Override
    public void revDisable(String id, String status) {
        DocClass docClass = new DocClass();
        docClass.setId(id);
        docClass.setUpdateTime(new Date());
        docClass.setClassStatus(status);
        this.baseMapper.updateById(docClass);
    }

    @Override
    public Integer validateZf(String versionId, String docId) {
        return this.baseMapper.validateZf(versionId, docId, SecurityUtils.getDeptId());
    }
}
