package com.rzdata.asas7.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
public class TokenManager {

    /**
     * 获取token
     *
     * @return tokenid
     */
    public String getTokenId() throws Exception {
//        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        String token = SpringUtils.getBean(RedisCache.class).getCacheObject(Constants.ASAS7_TOKEN);
        if (ObjectUtil.isNotEmpty(token)) {
            return token;
        }
//        Map<String,Object> params = new HashMap<String,Object>();
//        List<String> grantTypes = new ArrayList<String>();
//        grantTypes.add("authorization_code");
//        grantTypes.add("implicit");
//        grantTypes.add("refresh_token");
//        List<String> responseTypes = new ArrayList<String>();
//        responseTypes.add("token id_token");
//        responseTypes.add("code");
//        responseTypes.add("token");
//        List<String> redirectUris = new ArrayList<String>();
//        redirectUris.add("http://*************:88/dms-front/");
//        List<String> postLogoutRedirectUris = new ArrayList<String>();
//        postLogoutRedirectUris.add("http://*************:88/dms-front/");
//        Map<String,Object> device = new HashMap<String,Object>();
//        device.put("name", "ts-01");
//        device.put("client_type", "web");
//        device.put("description", "web");
//        Map<String,Object> deviceMap = new HashMap<String,Object>();
//        deviceMap.put("device",device);
//        params.put("client_name", "ts-01");
//        params.put("grant_types", grantTypes);
//        params.put("response_types", responseTypes);
//        params.put("scope", "offline openid all");
//        params.put("redirect_uris",redirectUris);
//        params.put("post_logout_redirect_uris",postLogoutRedirectUris);
//        params.put("metadata",deviceMap);
//        // 注册client
//        String result = HttpUtil.sendPost(baseUrl + "/oauth2/clients", JSONUtil.toJsonStr(params), "");
        try{
//            Clients clients = JSONUtil.toBean(result, Clients.class);
//            // 生成凭证
////        long expire = 120;
////        Date expireDate = new Date(new Date().getTime() + expire);
//            String secret = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_SECRET);
//            String userName = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_USER_NAME);
//            Algorithm algorithm = Algorithm.HMAC256(secret);
//            String evidence = JWT.create()
//                    .withClaim("username", userName) // Payload
////                .withExpiresAt(expireDate)
//                    .sign(algorithm);
//            //获取token
//            Map<String, Object> clientMap = new HashMap<>();
//            clientMap.put("client_id", clients.getClient_id());
//            clientMap.put("redirect_uri", "http://*************:88/dms-front/");
//            clientMap.put("response_type", "token id_token");
//            clientMap.put("scope", "offline openid all");
//            clientMap.put("udids", secret.split(","));
//            Map<String, Object> detailMap = new HashMap<>();
//            detailMap.put("id", "jwt");
//            Map<String, Object> codeMap = new HashMap<>();
//            codeMap.put("as_code", evidence);
//            detailMap.put("params", codeMap);
//            clientMap.put("credential", detailMap);
//            String tokenResult = HttpUtil.sendPost(baseUrl + "/api/authentication/v1/sso", JSONUtil.toJsonStr(clientMap), "");
//            TokenResult accessToken = JSONUtil.toBean(tokenResult, TokenResult.class);
//            token = accessToken.getAccess_token();
            token= getAppTokenId();
            //存入redis
            SpringUtils.getBean(RedisCache.class).setCacheObject(Constants.ASAS7_TOKEN, token, 50, TimeUnit.MINUTES);
            return token;
        }
        catch(Exception ex){
            log.error("getTokenId",ex);
            return "";
        }

    }


    /**
     * 获取体系文件系统访问AS7组织用户接口的调用令牌 （会出现SSL认证错误）
     *
     *
     * @return
     */
    public String getAppTokenId2() {
        String token = "";
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create("grant_type=client_credentials&scope=all", mediaType);
        // String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        String userName = "ed361fe6-eb5b-4ed2-976c-c3435cb05f17";
        String password = "654321";
        String credential = Credentials.basic(userName, password);
        Request request = new Request.Builder()
                .url("https://sz.aishu.cn/oauth2/token")
                .post(body)
                .addHeader("Authorization", credential)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        try {
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(request).execute();
            String returnRes = response.body().string();
            log.info("returnRes="+returnRes);
            JSONObject resJosn = JSONUtil.parseObj(returnRes);
            token = resJosn.getStr("access_token");
        } catch(Exception ex){
            log.error("getTokenId",ex);
            return "";
        }
        return token;
    }

    /**
     * 获取体系文件系统访问AS7组织用户接口的调用令牌
     * 使用 RestTemplate 方式
     * @return
     */
    public String getAppTokenId3() {
        String token = "";
        String userName = "ed361fe6-eb5b-4ed2-976c-c3435cb05f17";
        String password = "654321";
        String credential = Credentials.basic(userName, password);

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("grant_type","client_credentials");
        params.add("scope", "all");
        // 创建 HttpHeaders 对象，并设置 Content-Type 为 application/x-www-form-urlencoded
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization",credential);
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED);
        // 使用 MultiValueMap 类型创建请求体，并将参数放入其中
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params,headers);
        try {
            RestTemplate restTemplate = new RestTemplate(new HttpsClientRequestFactory());
            ResponseEntity<String> response = restTemplate.postForEntity("https://sz.aishu.cn/oauth2/token", requestEntity, String.class);
            String returnRes = response.getBody();
            log.info("returnRes="+returnRes);
            JSONObject resJosn = JSONUtil.parseObj(returnRes);
            token = resJosn.getStr("access_token");
        } catch(Exception ex){
            log.error("getTokenId",ex);
            return "";
        }
        return token;
    }


    /**
     * 获取体系文件系统访问AS7组织用户接口的调用令牌
     * 使用 RestTemplate 方式
     * @return
     */
    public String getAppTokenId() {
        String token = "";

        String asBaseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        String userName = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_USER_NAME);
        String password = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_USER_PWD);

        //String userName = "25665dc8-8465-4378-8950-350ea0d9893d";
        //String password = "dms@shlk2023";
        String credential = Credentials.basic(userName, password);

        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("grant_type","client_credentials");
        params.add("scope", "all");
        // 创建 HttpHeaders 对象，并设置 Content-Type 为 application/x-www-form-urlencoded
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization",credential);
        headers.setContentType(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED);
        // 使用 MultiValueMap 类型创建请求体，并将参数放入其中
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params,headers);
        try {
            RestTemplate restTemplate = new RestTemplate(new HttpsClientRequestFactory());
            // https://192.168.188.62/oauth2/token"
            ResponseEntity<String> response = restTemplate.postForEntity(asBaseUrl.concat("/oauth2/token"), requestEntity, String.class);
            String returnRes = response.getBody();
            log.info("returnRes="+returnRes);
            JSONObject resJosn = JSONUtil.parseObj(returnRes);
            token = resJosn.getStr("access_token");
        } catch(Exception ex){
            log.error("getAppTokenId异常",ex);
            return "";
        }
        return token;
    }

    public static void main(String[] args) {
        TokenManager obj = new TokenManager();
        log.info(obj.getAppTokenId());
    }


}
