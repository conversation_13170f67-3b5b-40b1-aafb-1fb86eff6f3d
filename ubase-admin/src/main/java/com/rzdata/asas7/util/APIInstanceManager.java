package com.rzdata.asas7.util;

import com.rzdata.asas7.conf.LoginInfo;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.ApiClient;
import io.contentBusAPI.docAccess.client.Configuration;
import io.contentBusAPI.docAccess.client.api.DefaultApi;
import org.springframework.beans.factory.annotation.Autowired;


public class APIInstanceManager {

    private DefaultApi apiInstanceWithToken;
    private DefaultApi apiInstanceWithoutToken;
    private TokenManager tokenManager;

    public TokenManager getTokenManager() {
        return tokenManager;
    }

    public APIInstanceManager() throws Exception {
        Boolean asPlatform = Boolean.valueOf(SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.ASAS7_PLATFORM));
        if (asPlatform) {
            tokenManager = new TokenManager();

            String basePath = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL) + "/api";
            ApiClient defaultClient = Configuration.getDefaultApiClient();
            defaultClient.setBasePath(basePath);
            defaultClient.setVerifyingSsl(false);
            defaultClient.setAccessToken(tokenManager.getTokenId());
            apiInstanceWithToken = new DefaultApi(defaultClient);

            ApiClient tmp = Configuration.getDefaultApiClient();
            tmp.setBasePath(basePath);
            tmp.setVerifyingSsl(false);
            apiInstanceWithoutToken = new DefaultApi(tmp);
        }
    }

    /**
     * 根据LoginInfo信息创建apiHelper类，报头设置tokenId，用https协议获取API调用类实例
     * 
     * @return https协议获取API调用类实例
     */
    public DefaultApi getAPIInstanceWithToken() throws Exception {
        tokenManager = new TokenManager();
        apiInstanceWithToken.getApiClient().setAccessToken(tokenManager.getTokenId());
        return apiInstanceWithToken;
    }


    /**
     * 根据LoginInfo信息创建apiHelper类，报头不设置tokenId，用https协议获取API调用类实例
     * 
     * @return https协议获取API调用类实例
     */
    public DefaultApi getAPIInstanceWithoutToken() {
        return apiInstanceWithoutToken;
    }

}
