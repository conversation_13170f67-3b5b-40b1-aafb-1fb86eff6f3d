package com.rzdata.asas7.service;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.asas7.util.JsonUtil;
import com.rzdata.asas7.util.TokenManager;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 爱数权限服务类
 */
@Slf4j
@Service
public class AsPermService {

    private TokenManager tokenManager;

    public AsPermService() {
        this.tokenManager = new TokenManager();
    }

    /**
     * 获取权限配置
     *
     * @param docid
     * @return
     */
    public AjaxResult<JSONObject> get(String docid) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/perm2/get"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 设置权限配置
     *
     * @param docid
     * @param perminfos
     * @param inherit
     *
     * @return
     */
    public AjaxResult<JSONObject> set(String docid, JSONArray perminfos, boolean inherit) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("perminfos", perminfos);
        requestMap.put("inherit", inherit);
        log.info(com.alibaba.fastjson.JSONObject.toJSONString(requestMap));
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/perm2/set"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 原生设置权限配置
     *
     * @param body
     *
     * @return
     */
    public AjaxResult<JSONObject> originalSet(String body) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/perm2/set"), body,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 构造用户权限JSON
     *
     * @param docid
     * @param userId
     * @param userName
     * @param allowValue
     * @param denyValue
     *
     * @return
     */
    private JSONObject newUserPerm(String docid, String userId, String userName, String allowValue, String denyValue) {
        return this.buildPermJson("user",docid,userId,userName,allowValue,denyValue);
    }

    /**
     * 构造部门权限JSON
     *
     * @param docid
     * @param deptId
     * @param deptName
     * @param allowValue
     * @param denyValue
     *
     * @return
     */
    private JSONObject newDeptPerm(String docid,String deptId,String deptName,String allowValue,String denyValue) {
        return this.buildPermJson("department",docid,deptId,deptName,allowValue,denyValue);
    }

    /**
     * 更新权限JSON
     *
     * @param json
     * @param allowValue
     * @param denyValue
     *
     * @return
     */
    private JSONObject updatePerm(JSONObject json, String allowValue, String denyValue, boolean merge) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("inheritdocid", "");
        requestMap.put("inheritpath", "");
        requestMap.put("accessorid", json.getStr("accessorid"));
        requestMap.put("accessorname", json.getStr("accessorname"));
        requestMap.put("accessortype", "user");
        // 允许
        // "display" "preview" "download" "create" "modify" "delete" "cache"
        if(merge) {
            // 合并模式-允许
            if(StringUtils.isNotEmpty(allowValue)) {
                // 合并
                List<String> temp = json.getJSONArray("allow").toList(String.class);
                temp.addAll(ListUtil.toList(allowValue.split(",")));
                requestMap.put("allow", temp);
            } else {
                requestMap.put("allow", json.getJSONArray("allow"));
            }
            // 合并模式-拒绝
            if(StringUtils.isNotEmpty(denyValue)) {
                // 合并
                List<String> temp = json.getJSONArray("deny").toList(String.class);
                temp.addAll(ListUtil.toList(denyValue.split(",")));
                requestMap.put("deny", temp);
            } else {
                requestMap.put("deny", json.getJSONArray("deny"));
            }
        } else {
            // 重置模式-允许
            if(StringUtils.isNotEmpty(allowValue)) {
                // 有值
                requestMap.put("allow", ListUtil.toList(allowValue.split(",")));
            } else if(StringUtils.isEmpty(allowValue) && !merge) {
                // 没有值，空数组
                requestMap.put("allow", new JSONArray());
            }
            // 重置模式-拒绝
            if(StringUtils.isNotEmpty(denyValue) ) {
                // 有值
                requestMap.put("deny", ListUtil.toList(denyValue.split(",")));
            } else {
                // 没有值，空数组
                requestMap.put("deny", new JSONArray());
            }
        }
        requestMap.put("createtime", System.currentTimeMillis());
        requestMap.put("modifytime", System.currentTimeMillis());
        requestMap.put("endtime", -1);
        JSONObject result = JsonUtil.convertJsonStrToBean(com.alibaba.fastjson.JSONObject.toJSONString(requestMap),JSONObject.class);
        return result;
    }


    /**
     * 构造权限JSON对象
     *
     * @param accessortype
     * @param docid
     * @param accessorid
     * @param accessorname
     * @param allowValue
     * @param denyValue
     * @return
     */
    private JSONObject buildPermJson(String accessortype,String docid,String accessorid,String accessorname,String allowValue,String denyValue) {
        JSONArray emptyArr = new JSONArray();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("inheritdocid", "");
        requestMap.put("inheritpath", "");
        requestMap.put("accessorid", accessorid);
        requestMap.put("accessorname", accessorname);
        // requestMap.put("accessorcsflevel", 5); // 用于军方
        requestMap.put("accessortype", accessortype);
        requestMap.put("allow", allowValue != null ?  ListUtil.toList(allowValue.split(",")) : emptyArr);
        requestMap.put("deny", denyValue != null ? ListUtil.toList(denyValue.split(",")) : emptyArr);
        requestMap.put("createtime", System.currentTimeMillis());
        requestMap.put("modifytime", System.currentTimeMillis());
        requestMap.put("endtime", -1);
        JSONObject result = JsonUtil.convertJsonStrToBean(com.alibaba.fastjson.JSONObject.toJSONString(requestMap),JSONObject.class);
        return result;
    }

    public JSONArray addCommonPerm(String accessortype, String docid, String accessorid, String accessorname, String allowValue, String denyValue, boolean merge)  {
        // 获取此文档的目前权限配置
        AjaxResult<JSONObject> getPerm = this.get(docid);
        JSONArray permArr = getPerm.getData().getJSONArray("perminfos");
        List<Object> list = permArr.stream().filter(t -> ((JSONObject) t).getStr("accessortype").equals(accessortype) & ((JSONObject) t).getStr("accessorid").equals(accessorid)).collect(Collectors.toList());

        if(permArr.size() == 0 || list.size() == 0) {
            // 新构造权限JSON对象
            JSONObject willAdd = this.buildPermJson(accessortype,docid,accessorid,accessorname,allowValue,denyValue);
            permArr.add(willAdd);
        } else if(list.size() > 0) {
            // 同一个人合并权限JSON
            JSONObject willUpdate = this.updatePerm((JSONObject)list.get(0),allowValue,denyValue,merge);
            // 删除原有JSON对象
            permArr.removeIf(t -> ((JSONObject) t).getStr("accessortype").equals(accessortype) & ((JSONObject) t).getStr("accessorid").equals(accessorid));
            // 添加最新权限对象
            permArr.add(willUpdate);
        }
        return permArr;
    }


}
