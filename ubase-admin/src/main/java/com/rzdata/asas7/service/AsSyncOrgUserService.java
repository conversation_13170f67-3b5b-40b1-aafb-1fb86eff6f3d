package com.rzdata.asas7.service;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rzdata.asas7.enums.ErrCodeEnum;
import com.rzdata.asas7.util.TokenManager;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.domain.entity.SysDept;
import com.rzdata.framework.core.domain.entity.SysUser;
import com.rzdata.framework.enums.UserStatus;
import com.rzdata.framework.utils.DateUtils;
import com.rzdata.framework.utils.SecurityUtils;
import com.rzdata.framework.utils.StringUtils;
import com.rzdata.process.domain.Tenant;
import com.rzdata.system.domain.SysUserTenant;
import com.rzdata.system.domain.TUnifyOrg;
import com.rzdata.system.domain.UnifyUser;
import com.rzdata.system.domain.vo.UnifyOrgPositionVo;
import com.rzdata.system.domain.vo.UnifyPositionVo;
import com.rzdata.system.mapper.SysUserRoleMapper;
import com.rzdata.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AsSyncOrgUserService {


    @Resource
    IUnifyUserService unifyUserService;

    @Resource
    ITUnifyOrgService unifyOrgService;

    @Resource
     AsOrgService asOrgService;

    @Resource
     AsDeptService asDeptService;

    @Resource
    AsUserService asUserService;

    @Resource
     ISysDeptService sysDeptService;

    @Resource
     ISysUserService sysUserService;

    @Resource
     ISysConfigService configService;

    @Resource
     ISysUserTenantService sysUserTenantService;

    @Resource
    ITenantService tenantService;

    @Resource
    SysUserRoleMapper sysUserRoleMapper;

    /**
     * 指定租户ID
     */
    static String DEFAULT_TENANT_ID = "";

    /**
     * 指定租户名称
     */
    static String DEFAULT_TENANT_NAME = "";

    /**
     * 数据创建人
     */
    static final String DEFAULT_CREATE_ID = "as7";

    /**
     * 排除部门开关
     */
    static final boolean EXCLUDE_FLAG = false;

    /**
     * 排除部门关键字
     */
    static final String EXCLUDE_DEPT_KEY = "杭州";

    /**
     * 是否单独去获取爱数第三方ID
     */
    static final boolean THIRD_FLAG = false;
    @Autowired
    private ISysConfigService iSysConfigService;

    public AsSyncOrgUserService() {
    }

    /**
     * 获取令牌接口
     *
     * @return
     */
    public AjaxResult getToken() {
        TokenManager tokenManager = new TokenManager();
        return AjaxResult.success(tokenManager.getAppTokenId());
    }


    /**
     * 初始化方法入口
     *
     * @param topOrgName    抽取顶级组织的名称
     * @param parentId      挂靠在睿展组织的ID
     * @param isCheckStatus 是否检查用户和部门的状态（反查爱数数据状态并更新）
     * @param isRebuild     是否重新构建
     * @param isRecursion   是否递归抽取
     * @param dataBase
     * @return
     */
    public AjaxResult initOrgAndUser(String topOrgName, String parentId, boolean isCheckStatus, boolean isRebuild, boolean isRecursion, boolean deptAsOrgFlag, String deptAsOrg, String dataBase) {
        try {
            // 初始化设置应用账号令牌
            String asAppToken = new TokenManager().getAppTokenId();
            if(StringUtils.isEmpty(asAppToken)) {
                return AjaxResult.error("获取爱数应用账号令牌失败");
            }
            this.asDeptService.setToken(asAppToken);
            this.asOrgService.setToken(asAppToken);
            this.asUserService.setToken(asAppToken);
            if(deptAsOrgFlag) {
                // 根据指定爱数组织下的第一层部门级别的部门名称KEY转DMS公司
                this.initOrgAndUserByDept(topOrgName, parentId, isRebuild, isRecursion, deptAsOrgFlag, deptAsOrg, dataBase);
            } else {
                // 根据指定爱数组织转DMS公司
                this.initOrgAndUserByOrg(topOrgName, parentId, isRebuild, isRecursion, dataBase);
            }
            if(isCheckStatus) {
                //获取数据所有数据比对爱数是否存在，如不存在删除、禁用
                this.updateDeptStatus(null,false);
                this.updateUserStatus(null,false);
            }
            log.info("与爱数平台同步完成，now="+DateUtils.getTime());
        } catch (Exception e) {
            log.error("initOrgAndUser异常",e);
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(DateUtils.getTime());
    }

    private void initOrgAndUserByOrg(String topOrgName, String parentId, boolean isRebuild, boolean isRecursion, String dataBase) throws Exception {
        // 获取顶级组织名称
        JSONObject topJson = this.getJSONObject(asOrgService.getOrgByName(topOrgName));
        // 构建顶级部门对象
        SysDept topDept = this.convertOrgJsonToSysDept(topJson, parentId,false);
        // 自动保存顶级作为DMS公司ID
        this.autoSaveComNode(topDept);
        // 获取顶级组织下的用户集合
        AjaxResult<JSONObject> subUserAjax = this.asDeptService.getSubUsersByDepid(topDept.getDeptId(), 0, 2000);
        JSONObject userJson = this.getJSONObject(subUserAjax);
        JSONArray userArray = userJson.getJSONArray("subUserInfos");
        if (userArray != null && userArray.size() > 0) {
            for (int i = 0; i < userArray.size(); i++) {
                JSONObject userItem = userArray.getJSONObject(i);
                SysUser userObj = this.convertToSysUser(userItem);
                userObj.setDeptId(topDept.getDeptId());
                this.autoSaveUser(userObj, dataBase);
            }
        }

        // 获取此顶级组织的所有子部门集合
        JSONObject subDeptJson = this.getJSONObject(asOrgService.getSubDepsByOrgId(topDept.getDeptId()));
        JSONArray subDepts = subDeptJson.getJSONArray("subDepInfos");
        if (subDepts != null && subDepts.size() > 0) {
            for (int i = 0; i < subDepts.size(); i++) {
                JSONObject deptItem = subDepts.getJSONObject(i);
                if (!this.isFilter(EXCLUDE_FLAG, EXCLUDE_DEPT_KEY, deptItem.getStr("depName"))) {
                    // 针对子部门进行用户和部门数据的抽取
                    this.buildUserAndDept(topDept, deptItem, subDepts.size() - i, deptItem.get("depId").toString(), isRecursion, dataBase);
                }
            }
        }
    }

    private void initOrgAndUserByDept(String topOrgName, String parentId, boolean isRebuild, boolean isRecursion, boolean deptAsOrgFlag, String deptAsOrg, String dataBase) throws Exception {
        // 获取顶级组织名称
        JSONObject topJson = this.getJSONObject(asOrgService.getOrgByName(topOrgName));

        // 获取此顶级组织的所有子部门集合
        JSONObject subDeptJson = this.getJSONObject(asOrgService.getSubDepsByOrgId(topJson.getStr("orgId")));
        JSONArray subDepts = subDeptJson.getJSONArray("subDepInfos");

        boolean isFind = false;
        if (subDepts != null && subDepts.size() > 0) {
            for (int i = 0; i < subDepts.size(); i++) {
                JSONObject deptItem = subDepts.getJSONObject(i);
                if (this.isFilter(deptAsOrgFlag, deptAsOrg, deptItem.getStr("depName"))) {
                    // 找到了业务部门作为DMS的公司
                    topJson = deptItem;
                    isFind = true;
                    break;
                }
            }
        }
        if(isFind) {
            // 构建顶级部门对象
            SysDept topDept = this.convertOrgJsonToSysDept(topJson, parentId,true);
            // 自动保存顶级作为DMS公司ID
            this.autoSaveComNode(topDept);
            // 获取顶级组织下的用户集合
            AjaxResult<JSONObject> subUserAjax = this.asDeptService.getSubUsersByDepid(topDept.getDeptId(), 0, 2000);
            JSONObject userJson = this.getJSONObject(subUserAjax);
            JSONArray userArray = userJson.getJSONArray("subUserInfos");
            if (userArray != null && userArray.size() > 0) {
                for (int i = 0; i < userArray.size(); i++) {
                    JSONObject userItem = userArray.getJSONObject(i);
                    SysUser userObj = this.convertToSysUser(userItem);
                    userObj.setDeptId(topDept.getDeptId());
                    this.autoSaveUser(userObj, dataBase);
                }
            }

            // 获取此顶级组织的所有子部门集合
            JSONObject subDeptJson2 = this.getJSONObject(asOrgService.getSubDepsByOrgId(topDept.getDeptId()));
            JSONArray subDepts2 = subDeptJson2.getJSONArray("subDepInfos");
            if (subDepts2 != null && subDepts2.size() > 0) {
                for (int i = 0; i < subDepts2.size(); i++) {
                    JSONObject deptItem = subDepts2.getJSONObject(i);
                    if (!this.isFilter(EXCLUDE_FLAG, EXCLUDE_DEPT_KEY, deptItem.getStr("depName"))) {
                        // 针对子部门进行用户和部门数据的抽取
                        this.buildUserAndDept(topDept, deptItem, subDepts2.size() - i, deptItem.get("depId").toString(), isRecursion, dataBase);
                    }
                }
            }

        }
    }

    private void autoSaveComNode(SysDept topDept) {
        // 保存顶级组织
        this.autoSaveDept(topDept);
        // 识别租户是否存在
        Tenant obj = this.tenantService.getById(topDept.getDeptId());
        if(obj == null) {
            // 构建租户对象
            obj = new Tenant();
            obj.setId(topDept.getDeptId());
            obj.setTenantName(topDept.getDeptName());
            obj.setAddress("auto");
            obj.setContact("auto");
            obj.setUrl("http://*************:88/dev-dms-front/#/login?tenantId="+obj.getId());
            // 新增保存
            this.tenantService.save(obj);
        }
    }

    /**
     * 是否过滤关键字
     *
     * @param excludeFlag
     * @param excludeKey
     * @param name
     * @return true 过滤、false 不过滤
     */
    private boolean isFilter(boolean excludeFlag,String excludeKey,String name) {
        boolean result = false;
        if(!excludeFlag) {
            return false;
        }
        if(name.indexOf(excludeKey) != -1) {
            result = true;
        }
        return result;
    }

    /**
     * 比对爱数部门数据修改状态
     */
    public String updateDeptStatus(String deptName,boolean reGetAppToken){
        int count = 0;
        int success = 0;
        QueryWrapper query = new QueryWrapper<>(new SysDept().setDeptType("DEPT").setStatus("0").setDelFlag("0"));
        if(StringUtils.isNotEmpty(deptName)) {
            query.eq("dept_name",deptName);
        }
        // 获取 有效且未删除的普通部门
        List<SysDept> list = sysDeptService.list(query);
        count = list.size();
        if(count > 0 && reGetAppToken) {
            // 初始化设置应用账号令牌
            String asAppToken = new TokenManager().getAppTokenId();
            this.asDeptService.setToken(asAppToken);
            this.asOrgService.setToken(asAppToken);
            this.asUserService.setToken(asAppToken);
        }
        for(SysDept item :  list) {
            AjaxResult<JSONObject> ajaxResult = asDeptService.getDepByid(item.getDeptId());
            JSONObject data=ajaxResult.getData();
            if(StringUtils.isNotEmpty(deptName)) {
                log.info("updateDeptStatus"+deptName+"="+ JSON.toJSONString(data));
            }
            if (data.containsKey("code") && data.getInt("code") == ErrCodeEnum.DEPT_INFO_ERR.getValue().intValue()) {
                // 爱数删除
                LambdaUpdateWrapper<SysDept> luw=new LambdaUpdateWrapper<>();
                luw.set(SysDept::getStatus, UserStatus.DISABLE.getCode());
                luw.set(SysDept::getDelFlag, UserStatus.DELETED.getCode());
                luw.set(SysDept::getMajorBusinesses, "爱数删除");
                luw.set(SysDept::getUpdateTime, new Date());
                luw.eq(SysDept::getDeptId,item.getDeptId());
                sysDeptService.update(luw);
                success++;
            } else if (data.containsKey("status") && data.getInt("status") == 1) {
                // 爱数禁用
                LambdaUpdateWrapper<SysDept> luw=new LambdaUpdateWrapper<>();
                luw.set(SysDept::getStatus, UserStatus.DISABLE.getCode());
                luw.set(SysDept::getDelFlag, UserStatus.DELETED.getCode());
                luw.set(SysDept::getMajorBusinesses, "爱数禁用");
                luw.set(SysDept::getUpdateTime, new Date());
                luw.eq(SysDept::getDeptId,item.getDeptId());
                sysDeptService.update(luw);
                success++;
            }
        }
        String result = "process="+success+"/"+count+ "|"+DateUtils.getTime();
        return result;
    }


    /**
     * 比对爱数用户数据修改状态
     */
    public String updateUserStatus(String userName,boolean reGetAppToken){
        int count = 0;
        int success = 0;
        // 获取 有效且未删除的普通用户
        QueryWrapper query = new QueryWrapper<>(new SysUser().setUserType("00").setStatus("0").setDelFlag("0"));
        if(StringUtils.isNotEmpty(userName)) {
            query.eq("user_name",userName);
        }
        List<SysUser> list = sysUserService.list(query);
        count = list.size();
        if(count > 0 && reGetAppToken) {
            // 初始化设置应用账号令牌
            String asAppToken = new TokenManager().getAppTokenId();
            this.asDeptService.setToken(asAppToken);
            this.asOrgService.setToken(asAppToken);
            this.asUserService.setToken(asAppToken);
        }
        for(SysUser user : list) {
            AjaxResult<JSONObject> ajaxResult = asUserService.getUserById(user.getUserId());
            JSONObject data=ajaxResult.getData();
            if(StringUtils.isNotEmpty(userName)) {
                log.info("updateUserStatus调试"+userName+"="+ JSON.toJSONString(data));
            }
            if (data.containsKey("code") && data.getInt("code") == ErrCodeEnum.USER_INFO_ERR.getValue().intValue()) {
                // 爱数删除
                LambdaUpdateWrapper<SysUser> luw=new LambdaUpdateWrapper<>();
                luw.set(SysUser::getStatus, UserStatus.DISABLE.getCode());
                luw.set(SysUser::getDelFlag, UserStatus.DELETED.getCode());
                luw.set(SysUser::getUpdateTime, new Date());
                luw.set(SysUser::getRemark, "爱数删除");
                luw.eq(SysUser::getUserId,user.getUserId());
                sysUserService.update(luw);
                success++;
            } else if (data.containsKey("status") && data.getInt("status") == 1) {
                // 爱数禁用
                LambdaUpdateWrapper<SysUser> luw=new LambdaUpdateWrapper<>();
                luw.set(SysUser::getStatus, UserStatus.DISABLE.getCode());
                luw.set(SysUser::getUpdateTime, new Date());
                luw.set(SysUser::getRemark, "爱数禁用");
                luw.eq(SysUser::getUserId,user.getUserId());
                sysUserService.update(luw);
                success++;
            }
        }
        String result = "process="+success+"/"+count+ "|"+DateUtils.getTime();
        return result;
    }


    /**
     *
     * @param currDeptJson
     * @param dataBase
     * @return
     */
    private void buildUserAndDept(SysDept parentDept, JSONObject currDeptJson, int orderNum, String secDeptId, boolean recursion, String dataBase) throws Exception {

        // 构造当前部门对象
        SysDept currDept = this.convertDeptJsonToSysDept(currDeptJson);
        // 部门序号
        currDept.setOrderNum(String.valueOf(orderNum));
        // 上级部门
        currDept.setParentId(parentDept.getDeptId());
        currDept.setParentName(parentDept.getDeptName());
        currDept.setAncestors(parentDept.getAncestors()+","+parentDept.getDeptId());
        // 全路径
        currDept.setDeptFullPathId(currDept.getDeptId()+","+parentDept.getDeptFullPathId());
        currDept.setDeptFullPathName(currDept.getDeptName()+","+parentDept.getDeptFullPathName());
        // 部门层级
        currDept.setDeptLevel(parentDept.getDeptLevel()+1);
        // 二级部门ID（流程平台需要）
        currDept.setSecDeptId(secDeptId);
        // 判断部门是否已存在
        this.autoSaveDept(currDept);
        // 判断是否存在子用户
        int subUserCnt = Integer.parseInt(currDeptJson.get("subUserCnt").toString());
        if(subUserCnt > 0) {
            // 获取子用户集合
            AjaxResult<JSONObject> subUserAjax = this.asDeptService.getSubUsersByDepid(currDept.getDeptId(),0,2000);
            JSONObject userJson = this.getJSONObject(subUserAjax);
            JSONArray userArray = userJson.getJSONArray("subUserInfos");
            if(userArray != null && userArray.size() > 0) {
                for(int i=0;i<userArray.size();i++) {
                    JSONObject userItem = userArray.getJSONObject(i);
                    SysUser userObj = this.convertToSysUser(userItem);
                    userObj.setDeptId(currDept.getDeptId());
                    this.autoSaveUser(userObj, dataBase);
                }
            }
        }
        // 这里的递归是指 下层有子部门的时候
        if(recursion) {
            // 判断是否存在子部门
            int subDepCnt = Integer.parseInt(currDeptJson.get("subDepCnt").toString());
            if(subDepCnt > 0) {
                JSONObject subDeptJson = this.getJSONObject(asOrgService.getSubDepsByOrgId(currDept.getDeptId()));
                JSONArray subDepts = subDeptJson.getJSONArray("subDepInfos");
                if(subDepts != null && subDepts.size() > 0) {
                    for(int i=0;i<subDepts.size();i++) {
                        JSONObject subDetpItem = subDepts.getJSONObject(i);
                        this.buildUserAndDept(currDept, subDetpItem, subDepts.size() - i, secDeptId, recursion, dataBase);
                    }
                }
            }
        }

    }

    /**
     * @param obj
     * @return
     */
    private JSONObject getJSONObject(AjaxResult<JSONObject> obj) {
        JSONObject result = null;
        if(obj.getCode() == 200) {
            result = obj.getData();
        }
        return result;
    }


    /**
     * 将组织JSON转为系统部门对象（顶级节点）
     *
     * @param orgJson
     * @return
     */
    private SysDept convertOrgJsonToSysDept(JSONObject orgJson, String parentId, boolean deptAsOrgFlag) {
        SysDept result = new SysDept();
        // 设置默认租户ID
        if(deptAsOrgFlag) {
            // 根据爱数组织下的部门来转DMS的公司
            result.setDeptId(orgJson.get("depId").toString());
            result.setDeptName(orgJson.get("depName").toString());

        } else {
            // 根据爱数组织来转DMS的公司
            result.setDeptId(orgJson.get("orgId").toString());
            result.setDeptName(orgJson.get("orgName").toString());
        }
        DEFAULT_TENANT_ID = result.getDeptId();
        DEFAULT_TENANT_NAME = result.getDeptName();
        result.setParentId(parentId);
        result.setAncestors("0,"+parentId);
        // 全路径
        result.setDeptFullPathId(result.getDeptId()+",1");
        result.setDeptFullPathName(result.getDeptName()+",顶级组织");
        result.setDeptLevel(1);
        result.setTenantId(DEFAULT_TENANT_ID);
        result.setStatus("0");
        result.setDelFlag("0");
        result.setDeptType("ORG");
        result.setCreateBy(DEFAULT_CREATE_ID);
        result.setCreateTime(new Date());
        return result;
    }

    /**
     * 将部门JSON转为系统部门对象
     *
     * @param deptJson
     * @return
     */
    private SysDept convertDeptJsonToSysDept(JSONObject deptJson) {
        SysDept result = new SysDept();
        result.setDeptId(deptJson.get("depId").toString());
        result.setDeptName(deptJson.get("depName").toString());
        result.setTenantId(DEFAULT_TENANT_ID);
        result.setStatus("0");
        result.setDelFlag("0");
        result.setDeptType("DEPT");
        result.setCreateBy(DEFAULT_CREATE_ID);
        result.setCreateTime(new Date());
        return result;
    }

    /**
     * 将用户JSON转为系统用户对象
     *
     * @param userJson
     * @return
     */
    private SysUser convertToSysUser(JSONObject userJson) {
        SysUser result = new SysUser();
        result.setUserId(userJson.getStr("userId"));
        String thirdId = null;
        if(THIRD_FLAG) {
            // 设置thirdid（钉钉通讯录的userid）
            JSONObject json = this.getJSONObject(this.asUserService.getUserById(result.getUserId()));
            thirdId = json.getStr("thirdId");
        }
        result.setThirdId(thirdId == null ? "" : thirdId);
        result.setNickName(userJson.getStr("displayName"));
        result.setUserName(userJson.getStr("loginName"));
        result.setEmail(userJson.getStr("email"));
        // 默认密码在 系统参数中配置
        result.setPassword(SecurityUtils.encryptPassword(configService.selectConfigByKey("sys.user.initPassword")));
        result.setSex("0");
        // 帐号状态（0正常 1停用）
        result.setStatus(userJson.getStr("status").equals("0") ? "0" : "1");
        // 删除标志（0代表存在 2代表删除）
        result.setDelFlag("0");
        result.setCreateBy(DEFAULT_CREATE_ID);
        result.setCreateTime(new Date());
        return result;
    }


    private String getLeaderCode(SysUser sysUser){
        String domain = sysUser.getUserName().indexOf("@mehowcy.com")>0?"@mehowcy.com":"@mehowmy.com";
        String leaderCode = null;
        List<UnifyUser> unifyUserList = unifyUserService.list(new QueryWrapper<UnifyUser>(new UnifyUser().setStatus("1")
                .setSn(sysUser.getUserName().replace(domain,""))));
        if(unifyUserList.size()>0){
            UnifyUser unifyUser = unifyUserList.get(0);
            if(unifyUser.getPositions()!=null){
                List<UnifyPositionVo> positionsList = JSON.parseObject(unifyUser.getPositions(), new TypeReference<List<UnifyPositionVo>>(){});
                List<UnifyPositionVo> positionsFilterList = positionsList.stream()
                        .filter(p -> "1".equals(p.getStatus()))
                        .collect(Collectors.toList());

                //取上级领导人
                if(positionsFilterList.size()>0){
                    leaderCode = StringUtils.isNotEmpty(positionsFilterList.get(0).getLeader())?positionsFilterList.get(0).getLeader()+domain:null;
                    if(sysUser.getUserName().equals(leaderCode)){
                        TUnifyOrg unifyOrg =unifyOrgService.getById(unifyUser.getOrgId());
                        if(unifyOrg!=null){
                            UnifyOrgPositionVo unifyOrgPositionVo = JSON.parseObject(unifyOrg.getExtraAttrs(), UnifyOrgPositionVo.class);
                            leaderCode = unifyOrgPositionVo!=null &&unifyOrgPositionVo.getSuperLeader()!=null?unifyOrgPositionVo.getSuperLeader()+domain:null;
                        }
                    }
                    sysUser.setLeaderCode(leaderCode);
                }
            }
        }
        return leaderCode;
    }

    /**
     * 识别并保存用户、用户租户关系记录
     *
     * @param userObj
     * @param dataBase
     * @return
     */
    private boolean autoSaveUser(SysUser userObj, String dataBase) {
        boolean result = false;
        SysUser dbUser = this.sysUserService.selectUserById(userObj.getUserId());
        //更新用户的上级领导与上级领导所属部门
        if(userObj.getUserName().indexOf("@mehowmy.com")>0){
            String userName = userObj.getUserName().replace("@mehowmy.com","@mehowcy.com");
            SysUser dbUser2 = this.sysUserService.selectUserByUserName(userName);
            if(dbUser2!=null){
                userObj.setUserName(userName);
            }
        }

        if(dbUser == null) {
            this.getLeaderCode(userObj);
            // 新增保存用户
            this.sysUserService.save(userObj);
            result = this.autoSaveUserTenant(userObj);
            if (StrUtil.isNotBlank(dataBase)) {
                List<String> list = Arrays.asList(dataBase.split(","));
                String roleIds = iSysConfigService.selectConfigByKey("sync_user_role");
                if(StrUtil.isNotBlank(roleIds)) {
                    list = Arrays.asList(roleIds.split(","));
                }

                sysUserRoleMapper.addRole(userObj.getUserId(), list,);
            }
        } else {
            // 更新
            userObj.setCreateBy(null);
            userObj.setCreateTime(null);
            this.getLeaderCode(userObj);
            this.sysUserService.updateById(userObj);
            result = this.autoSaveUserTenant(userObj);
        }
        return result;
    }

    /**
     * 保存用户租户关系记录
     *
     * @param userObj
     * @return
     */
    private boolean autoSaveUserTenant(SysUser userObj) {
        boolean result = false;
        LambdaQueryWrapper<SysUserTenant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserTenant::getUserId,userObj.getUserId());
        queryWrapper.eq(SysUserTenant::getTenantId,DEFAULT_TENANT_ID);
        long count = this.sysUserTenantService.count(queryWrapper);
        if(count == 0) {
            // 新增保存用户租户关系记录
            SysUserTenant newUserTenant = new SysUserTenant();
            newUserTenant.setUserId(userObj.getUserId());
            newUserTenant.setUserName(userObj.getUserName());
            newUserTenant.setTenantId(DEFAULT_TENANT_ID);
            newUserTenant.setTenantName(DEFAULT_TENANT_ID);
            result = this.sysUserTenantService.save(newUserTenant);
        }
        return result;
    }

    /**
     * 识别并自动更新部门
     *
     * @param deptObj
     * @return
     */
    private boolean autoSaveDept(SysDept deptObj) {
        boolean result = false;
        // 判断部门是否已存在
        SysDept dbObj = sysDeptService.selectDeptById(deptObj.getDeptId());
        if(dbObj == null) {
            // 不存在则新增保存
            result = this.sysDeptService.save(deptObj);
        } else {
            // 更新
            deptObj.setCreateBy(null);
            deptObj.setCreateTime(null);
            result = this.sysDeptService.updateById(deptObj);
        }
        return result;
    }


}
