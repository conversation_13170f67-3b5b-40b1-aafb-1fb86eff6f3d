package com.rzdata.asas7.service;

import cn.hutool.json.JSONObject;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AsOrgService extends AsBaseService {

    @Deprecated
    public AjaxResult getAllOrg() throws Exception {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("rows", 20);
        requestMap.put("type", "all");
        requestMap.put("keyword", "out");
        requestMap.put("start", 0);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getallorg"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 根据组织名获取组织信息
     *
     * @param orgName
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getOrgByName(String orgName) throws Exception {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("orgName", orgName);

        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getorgbyname"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 获取组织下子部门信息
     *
     * @param orgId
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getSubDepsByOrgId(String orgId) throws Exception {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("orgId", orgId);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getsubdepsbyorgid"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }
}
