package com.rzdata.asas7.service;

import cn.hutool.json.JSONObject;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.asas7.util.TokenManager;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 爱数目录服务类
 */
@Slf4j
@Service
public class AsDirService {

    private TokenManager tokenManager;

    public AsDirService() {
        this.tokenManager = new TokenManager();
    }

    /**
     * 由名字路径获取对象信息协议
     *
     * @param fullPath
     * @return
     */
    public AjaxResult<JSONObject> getInfoByPath(String fullPath) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("namepath", fullPath);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/getinfobypath"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 转换路径协议
     *
     * @param docid
     * @return
     */
    public AjaxResult<JSONObject> convertPath(String docid) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);

        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/convertpath"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 创建目录协议
     *
     * @param docid 待创建目录的父目录gns路径
     * @param name 目录名称，UTF8编码
     * @param ondup 默认值 ：1
     * 1:检查是否重命名，重名则抛异常
     * 2:如果重名冲突，自动重名
     * 3:如果重名冲突，自动覆盖
     *
     * @return
     */
    public AjaxResult<JSONObject> create(String docid, String name, int ondup) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("name", name);
        requestMap.put("ondup", ondup);

        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/dir/create"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 创建多级目录协议
     *
     * @param docid 待创建目录的父目录gns路径
     * @param path 多级目录名称，UTF8编码
     *
     * @return
     */
    public AjaxResult<JSONObject> createMultilevelDir(String docid, String path) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("path", path);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/dir/createmultileveldir"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 删除目录
     *
     * @param docid 待删除目录gns路径
     *
     * @return
     */
    public AjaxResult<JSONObject> delete(String docid) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/dir/delete"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 删除文件
     *
     * @param docid 待删除文件gns路径
     *
     * @return
     */
    public AjaxResult<JSONObject> fileDelete(String docid) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/delete"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 移动文件
     *
     * @param docid 待移动文件gns路径
     * @param destparent 目标父对象的gns路径
     *
     * @return
     */
    public AjaxResult<JSONObject> fileMove(String docid,String destparent) {
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("docid", docid);
        requestMap.put("destparent", destparent);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/efast/v1/file/move"), requestMap,requestHeader, JSONObject.class, "Bearer " + tokenManager.getAppTokenId());
        return AjaxResult.success("success",jsonObjectTemp);
    }

}
