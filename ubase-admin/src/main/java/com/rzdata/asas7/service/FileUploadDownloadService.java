package com.rzdata.asas7.service;

import com.rzdata.asas7.model.MultiUploadReq;
import com.rzdata.asas7.model.OpstatiStics;
import io.contentBusAPI.docAccess.client.model.ContactorSearchRes;
import io.contentBusAPI.docAccess.client.model.DirCreateRes;
import io.contentBusAPI.docAccess.client.model.FileOsbeginuploadRes;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface FileUploadDownloadService {
    /**
     * 文件单次上传
     *
     * @param
     * @throws Exception
     */
    public FileOsbeginuploadRes singleUpload(String filePath) throws Exception;

    /**
     * 文件分块上传
     *
     * @param
     */
    public void multiUpload(MultiUploadReq uploadBigDataReq) throws Exception;

    /**
     * 单文件下载
     *
     * @param downloadReq
     * @throws Exception
     */
    public void singleDownload(String savePath, String filePath) throws Exception;

    void downloadFileId(String fileId, HttpServletResponse response);

    String previewFileId(String fileId, HttpServletResponse response);

    /**
     * 获取token
     * @return
     */
    String getAccessToken();

    /**
     * 文件统计
     */
    List<OpstatiStics> fileOpstatiStics(List<String> docIds) throws Exception;

    /**
     * 创建目录
     * @return
     */
    DirCreateRes dirCreate(String parentId, String name) throws Exception;

    /**
     * 文件搜索
     * @return
     * @throws Exception
     */
    void fileSearch() throws Exception;
}
