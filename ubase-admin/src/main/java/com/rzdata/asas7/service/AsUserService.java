package com.rzdata.asas7.service;

import cn.hutool.json.JSONObject;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * 爱数用户服务类
 */
@Slf4j
@Service
public class AsUserService extends AsBaseService {

    /**
     * 根据令牌获取爱数用户信息
     *
     * @param asUserToken
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getUserInfoByToken(String asUserToken)  {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        JSONObject jsonObjectTemp = HttpUtils.getInstance().getForObject(baseUrl.concat("/api/eacp/v1/user/get"), null, JSONObject.class, "Bearer " + asUserToken);
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 通过id获取用户信息
     *
     * @param userId
     * @return
     */
    public AjaxResult<JSONObject> getUserById(String userId)  {
        // 请求参数
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userId", userId);
        // 请求头
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        // 获取基础URL
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        // 发起请求和获取回执
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getuserbyid"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }

}
