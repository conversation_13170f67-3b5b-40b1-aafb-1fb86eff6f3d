package com.rzdata.asas7.service;

import cn.hutool.json.JSONObject;
import com.rzdata.asas7.util.HttpUtils;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AsDeptService extends AsBaseService {

    /**
     * 获取所有子部门信息
     *
     * @param depId
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getSubDepsByDepId(String depId) throws Exception {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("depId", depId);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getsubdepsbydepid"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }


    /**
     * 获取所有子用户信息
     *
     * @param depId
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getSubUsersByDepid(String depId,int start, int limit) throws Exception {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("depId", depId);
        requestMap.put("start", start);
        requestMap.put("limit", limit);

        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getsubusersbydepid"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }

    /**
     * 通过id获取部门信息
     *
     * @param depId
     * @return
     * @throws Exception
     */
    public AjaxResult<JSONObject> getDepByid(String depId)  {
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("depId", depId);

        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        JSONObject jsonObjectTemp = HttpUtils.getInstance().postForObject(baseUrl.concat("/api/eacp/v1/organization/getdepbyid"), requestMap,requestHeader, JSONObject.class, "Bearer " + super.getToken());
        return AjaxResult.success("success",jsonObjectTemp);
    }
}
