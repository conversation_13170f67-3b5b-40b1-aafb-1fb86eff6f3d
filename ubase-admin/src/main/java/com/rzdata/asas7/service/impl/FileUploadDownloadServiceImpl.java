package com.rzdata.asas7.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.rzdata.asas7.conf.LoginInfo;
import com.rzdata.asas7.model.*;
import com.rzdata.asas7.service.FileUploadDownloadService;
import com.rzdata.asas7.util.*;
import com.rzdata.framework.constant.Constants;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.core.redis.RedisCache;
import com.rzdata.framework.utils.spring.SpringUtils;
import com.rzdata.process.domain.BasicFile;
import com.rzdata.process.service.IBasicFileService;
import com.rzdata.system.service.ISysConfigService;
import io.contentBusAPI.docAccess.client.ApiException;
import io.contentBusAPI.docAccess.client.ApiResponse;
import io.contentBusAPI.docAccess.client.api.DefaultApi;
import io.contentBusAPI.docAccess.client.model.*;
import okhttp3.*;
import org.apache.http.Header;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class FileUploadDownloadServiceImpl implements FileUploadDownloadService {

    private OSSHelper ossHttpHelper;
    private APIInstanceManager apiInstceManager;

    @Autowired
    private IBasicFileService iBasicFileService;

    private long minSize; // 分块上传块最小单位大小。


    public long getMinSize() {
        return minSize;
    }

    public void setMinSize(long minSize) {
        this.minSize = minSize;
    }

    public FileUploadDownloadServiceImpl() throws Exception {
        ossHttpHelper = new OSSHelper();
        this.apiInstceManager = new APIInstanceManager();
        minSize = 4 * 1024 * 1024; // 默认为4M
    }

    private String getDocidByPath(String namePath) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        FileGetinfobypathReq getinfobypathBody = new FileGetinfobypathReq();
        getinfobypathBody.setNamepath(namePath);
        String docId = "";
        try {
            docId = apiInstance.efastV1FileGetinfobypathPost(getinfobypathBody).getDocid();
        } catch (ApiException e) {
            System.out.println(e.getCode() + " " + e.getResponseBody() + " " + e.getResponseHeaders());
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return docId;
    }

    @Override
    public FileOsbeginuploadRes singleUpload(String filePath) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        SingleUploadReq uploadReq = new SingleUploadReq();
        String path = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_FILE_PATH);
        String parentId = getDocidByPath(path);
        uploadReq.setDocid(parentId);
        uploadReq.setFilePath(filePath);
        uploadReq.setOndup(2L);
        // 调用 osbeginupload API
        File uploadFile = new File(uploadReq.getFilePath());
        uploadReq.setLength(uploadFile.length());
        uploadReq.setName(uploadFile.getName());
        FileOsbeginuploadReq osbeginuploadBody = new FileOsbeginuploadReq();
        osbeginuploadBody = uploadReq;

        FileOsbeginuploadRes osbeginuploadResult = apiInstance.efastV1FileOsbeginuploadPost(osbeginuploadBody);
//        System.out.println(osbeginuploadResult);

        // 根据服务器返回的对象存储请求，向对象存储上传数据
        InputStreamEntity body = new InputStreamEntity(new FileInputStream(uploadFile), uploadFile.length());
        Vector<String> headers = new Vector<String>();
        List<String> authRequestList = osbeginuploadResult.getAuthrequest();
        for (int i = 2; i < authRequestList.size(); ++i) {
            String header = authRequestList.get(i);
            headers.add(header);
        }
        ossHttpHelper.SendReqToOSS(authRequestList.get(0), authRequestList.get(1), headers, body);

        // 调用osendupload API
        FileOsenduploadReq osenduploadBody = new FileOsenduploadReq();
        osenduploadBody.setDocid(osbeginuploadResult.getDocid());
        osenduploadBody.setRev(osbeginuploadResult.getRev());
        FileOsenduploadRes osenduploadResult = apiInstance.efastV1FileOsenduploadPost(osenduploadBody);
//        System.out.println(osenduploadResult);
        return osbeginuploadResult;
    }

    @Override
    public void multiUpload(MultiUploadReq multiUploadReq) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        File uploadFile = new File(multiUploadReq.getFilePath());

        // 获取对象存储信息，判断得出分块的大小
        FileOsoptionRes result = apiInstance.efastV1FileOsoptionPost();
        System.out.println(result);
        long partMinSize = result.getPartminsize();
        if (partMinSize <= this.minSize) {
            partMinSize = this.minSize;
        }
        long partMaxSize = result.getPartmaxsize();
        long partMaxNum = result.getPartmaxnum();
        long partSize = partMinSize;
        int partCount;

        while (true) {
            if (partSize > partMaxSize) {
                throw new Exception("上传文件过大!");
            }
            partCount = (int) (uploadFile.length() / partSize);
            if (uploadFile.length() == 0 || uploadFile.length() % partSize != 0) {
                ++partCount;
            }
            if (partCount <= partMaxNum) {
                break;
            }
            partSize += partMinSize;
        }

        // 调用 osinitmultiupload API
        multiUploadReq.setLength(uploadFile.length());
        multiUploadReq.setName(uploadFile.getName());

        FileOsinitmultiuploadReq osinitmultiuploadBody = new FileOsinitmultiuploadReq();
        osinitmultiuploadBody = multiUploadReq;
        FileOsinitmultiuploadRes osinitmultiuploadResult = apiInstance.efastV1FileOsinitmultiuploadPost(osinitmultiuploadBody);
        System.out.println(osinitmultiuploadResult);

        String retDocId = osinitmultiuploadResult.getDocid();
        String retRev = osinitmultiuploadResult.getRev();
        String retUploadId = osinitmultiuploadResult.getUploadid();

        // 调用 osuploadpart API
        String parts = "1-" + partCount;
        FileOsuploadpartReq osuploadpartBody = new FileOsuploadpartReq();
        osuploadpartBody.setDocid(retDocId);
        osuploadpartBody.setRev(retRev);
        osuploadpartBody.setUploadid(retUploadId);
        osuploadpartBody.setParts(parts);

        FileOsuploadpartRes osuploadpartResult = apiInstance.efastV1FileOsuploadpartPost(osuploadpartBody);
        System.out.println(osuploadpartResult);

        // 根据服务器返回的对象存储请求，向对象存储分块上传数据
        byte[] buf = new byte[(int) partSize];
        FileInputStream fis = new FileInputStream(uploadFile);
        int partIndex = 1; // 记录当前处理的是第几个part
        FileOscompleteuploadReqPartinfo partInfo = new FileOscompleteuploadReqPartinfo();
        do {
            int writeSize = fis.read(buf);
            Vector<String> headers = new Vector<String>();
            List<String> authRequestList = osuploadpartResult.getAuthrequests().get(String.valueOf(partIndex));
            for (int i = 2; i < authRequestList.size(); ++i) {
                String header = authRequestList.get(i);
                headers.add(header);
            }
            ByteArrayEntity body = new ByteArrayEntity(buf, 0, writeSize);
            OSSHelper.OSSReqResult ossResult = ossHttpHelper.SendReqToOSS(authRequestList.get(0), authRequestList.get(1), headers, body); // 上传块
            // 获取etag,由于报头中"etag"可能为"Etag","ETag","ETAG"等情况，故这里对报头key值进行遍历，将key值变为大写后与"ETAG"进行比较，若相等则让etag等于其value，推出循环。0
            String etag = null;

            Header[] headerArray = ossResult.response.getAllHeaders();
            for (int i = 0; i < headerArray.length; ++i) {
                String key = headerArray[i].getName();
                if (key.toUpperCase().equals("ETAG")) {
                    etag = headerArray[i].getValue();
                    i = headerArray.length;
                }
            }
            ossResult.response.close();
            ArrayList<Object> tempList = new ArrayList<>();
            tempList.add(etag);
            tempList.add(writeSize);
            partInfo.put(String.valueOf(partIndex), tempList);
            ++partIndex;
        } while (partIndex <= partCount);
        fis.close();

        // 调用 oscompleteupload API
        FileOscompleteuploadReq oscompleteuploadBody = new FileOscompleteuploadReq();
        oscompleteuploadBody.setDocid(retDocId);
        oscompleteuploadBody.setRev(retRev);
        oscompleteuploadBody.setUploadid(retUploadId);
        oscompleteuploadBody.setPartinfo(partInfo);

        String[] completeuploadInfo = new String[2];
        ApiResponse<String> oscompleteuploadResult = apiInstance
                .efastV1FileOscompleteuploadPostWithHttpInfo(oscompleteuploadBody);
        String boundary = oscompleteuploadResult.getHeaders().get("Content-Type").get(0).split(";", 2)[1].split("=",
                2)[1];
        completeuploadInfo = oscompleteuploadResult.getData().split("--" + boundary);
        System.out.println(oscompleteuploadResult);

        // 根据服务器返回的索引信息和对象存储请求，向对象存储上传索引信息对块文件进行合并
        StringEntity body = new StringEntity(completeuploadInfo[1].replaceAll("\r\n", ""), "UTF-8");
        JSONObject returnJson = JSONUtil.parseObj((completeuploadInfo[2].replaceAll("\r\n", "")));
        JSONArray authRequest = returnJson.getJSONArray("authrequest");
        String method = (String) authRequest.get(0);
        String url = (String) authRequest.get(1);
        Vector<String> headers = new Vector<String>();
        for (int i = 2; i < authRequest.size(); i++) {
            headers.add((String) authRequest.get(i));
        }
        ossHttpHelper.SendReqToOSS(method, url, headers, body);

        // 调用osendupload API
        FileOsenduploadReq osenduploadBody = new FileOsenduploadReq();
        osenduploadBody.setDocid(retDocId);
        osenduploadBody.setRev(retRev);
        FileOsenduploadRes osenduploadResult = apiInstance.efastV1FileOsenduploadPost(osenduploadBody);
        System.out.println(osenduploadResult);

    }

    @Override
    public void singleDownload(String savePath, String filePath) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        SingleDownloadReq downloadReq = new SingleDownloadReq();
        String docid = getDocidByPath(filePath);
        downloadReq.setDocid(docid);
        downloadReq.setSavePath(savePath);
        // 调用 osdownload API
        FileOsdownloadReq osdownloadBody = new FileOsdownloadReq();
        osdownloadBody = downloadReq;
        FileOsdownloadRes osdownloadResult = apiInstance.efastV1FileOsdownloadPost(osdownloadBody);
        System.out.println(osdownloadResult);

        // 根据服务器返回的对象存储请求，向对象存储下载数据
        File saveFile = new File(downloadReq.getSavePath() + "\\" + osdownloadResult.getName());
        if (saveFile.exists()) {
            throw new Exception("下载路径存在同名文件，下载失败。");
        }
        BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(saveFile));
        Vector<String> headers = new Vector<String>();
        List<String> authRequestList = osdownloadResult.getAuthrequest();
        for (int i = 2; i < authRequestList.size(); ++i) {
            String header = authRequestList.get(i);
            headers.add(header);
        }
        OSSHelper.OSSReqResult ossResult = ossHttpHelper.SendReqToOSS(authRequestList.get(0), authRequestList.get(1), headers, null);
        BufferedInputStream bis = new BufferedInputStream(ossResult.response.getEntity().getContent());
        int len = -1;
        byte[] bytes = new byte[1024];
        while ((len = bis.read(bytes)) != -1) {
            bos.write(bytes, 0, len);
        }
        bis.close();
        ossResult.request.releaseConnection();
        bos.close();
    }

    @Override
    public void downloadFileId(String fileId, HttpServletResponse response) {
        try {
            BasicFile file = iBasicFileService.getById(fileId);
            DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
            FileOsdownloadReq osdownloadBody = new FileOsdownloadReq();
            osdownloadBody.setDocid(file.getExternalFileId());
            FileOsdownloadRes osdownloadResult = apiInstance.efastV1FileOsdownloadPost(osdownloadBody);
            System.out.println(osdownloadResult);
            Vector<String> headers = new Vector<String>();
            List<String> authRequestList = osdownloadResult.getAuthrequest();
            for (int i = 2; i < authRequestList.size(); ++i) {
                String header = authRequestList.get(i);
                headers.add(header);
            }
            OSSHelper.OSSReqResult ossResult = ossHttpHelper.SendReqToOSS(authRequestList.get(0), authRequestList.get(1), headers, null);
            BufferedInputStream bis = new BufferedInputStream(ossResult.response.getEntity().getContent());
            // 2、通过response对象，获取到输出流
            ServletOutputStream outputStream = response.getOutputStream();
            // 3、通过response对象设置响应数据格式(image/jpeg)
            response.setContentType(file.getFileType()+";charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getFileName()+ Constants.CHARACTER_DROP+file.getFileType(), "utf-8"));
            int len = 0;
            byte[] bytes = new byte[1024*1024];
            while ((len = bis.read(bytes)) != -1){
                // 4、通过输入流读取文件数据，然后通过上述的输出流写回浏览器
                outputStream.write(bytes,0,len);
                // 刷新
                outputStream.flush();
            }
            // 5、关闭资源
            outputStream.close();
            bis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String previewFileId(String fileId, HttpServletResponse response) {
        BasicFile file = iBasicFileService.getById(fileId);
        TokenManager tokenManager = new TokenManager();
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("id", file.getExternalFileId().substring(file.getExternalFileId().lastIndexOf("/")+1));
        requestMap.put("version", file.getExternalRev());
        requestMap.put("format", ListUtil.toList(".pdf"));
        requestMap.put("video_quality", "od");
        Map<String, Object> watermarkMap = new HashMap<>();
        watermarkMap.put("layout", "tile");
        Map<String, Object> customConfigMap = new HashMap<>();
        customConfigMap.put("enabled",true);
        customConfigMap.put("content","受控");
        customConfigMap.put("color","#f5666d");
        customConfigMap.put("opacity",50);
        customConfigMap.put("font_size",50);
        watermarkMap.put("custom_config", customConfigMap);
        requestMap.put("watermark", watermarkMap);
        Map<String, String> requestHeader = new HashMap<>();
        requestHeader.put("Content-Type", "application/json");
        System.out.println(JSONUtil.toJsonStr(requestMap));
        return HttpUtils.getInstance().postForString(baseUrl.concat("/api/open-doc/v1/file-preview"), requestMap,requestHeader, "Bearer " + tokenManager.getAppTokenId());
    }

    @Override
    public String getAccessToken() {
        TokenManager tokenManager = new TokenManager();
        String token = "";
        try {
            token = tokenManager.getTokenId();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }


    @Override
    public List<OpstatiStics> fileOpstatiStics(List<String> docIds) throws Exception {
        List<OpstatiStics> list = new ArrayList<>();
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        FileOpstatisticsReq req = new FileOpstatisticsReq();
        req.setDocid(docIds);
        FileOpstatisticsRes res = apiInstance.efastV1FileOpstatisticsPost(req);
        if (ObjectUtil.isNotEmpty(res)) {
            Set<String> set = res.keySet();
            // 遍历键的集合，获取得到每一个键
            for (String key : set) {
                OpstatiStics opstatiStics = new OpstatiStics();
                // 根据键去找值
                JSONObject value = JSONUtil.parseObj(res.get(key));
                String download = value.getStr("download");
                String preview = value.getStr("preview");
                opstatiStics.setDocId(key);
                opstatiStics.setDownLoad(download);
                opstatiStics.setPreview(preview);
                list.add(opstatiStics);
            }
        }
        return list;
    }

    @Override
    public DirCreateRes dirCreate(String parentId, String name) throws Exception {
        DefaultApi apiInstance = apiInstceManager.getAPIInstanceWithToken();
        DirCreateReq dirCreateReq = new DirCreateReq();
        dirCreateReq.setDocid(parentId);
        dirCreateReq.setName(name);
        dirCreateReq.setOndup(1L);
        DirCreateRes dirCreateRes = apiInstance.efastV1DirCreatePost(dirCreateReq);
        return dirCreateRes;
    }

    @Override
    public void fileSearch() throws Exception {
        TokenManager tokenManager = new TokenManager();
        Map<String, Object> requsetMap = new HashMap<>();
        requsetMap.put("rows", 20);
        requsetMap.put("type", "all");
//        requsetMap.put("range", "gns://5EF26633E8E740D3AFBB14022CC575CA/636190A105D0452A81E9C44D41B6AD66");
        requsetMap.put("keyword", "out");
        requsetMap.put("start", 0);
//        requsetMap.put("keyword", "");
//        requsetMap.put("dimension", "");
        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(JSONUtil.toJsonStr(requsetMap), mediaType);
        String baseUrl = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey(Constants.AS_BASE_URL);
        Request request = new Request.Builder()
                .url(baseUrl + "/api/ecosearch/v1/file-search")
                .post(body)
                .addHeader("Authorization", "Bearer " + tokenManager.getTokenId())
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        String StringTemp = response.body().string();
        JSONObject jsonObjectTemp = JSONUtil.parseObj(StringTemp);
        String str2 = jsonObjectTemp.toString();
    }
}
