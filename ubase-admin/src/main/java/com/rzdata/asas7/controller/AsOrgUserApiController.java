package com.rzdata.asas7.controller;

import com.rzdata.asas7.service.AsDeptService;
import com.rzdata.asas7.service.AsOrgService;
import com.rzdata.framework.annotation.Log;
import com.rzdata.framework.core.domain.AjaxResult;
import com.rzdata.framework.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * AS7集成组织、部门、用户接口
 *
 * <AUTHOR>
 */
@Api(value = "AS7集成接口", tags = {"AS7集成接口"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/as/api")
public class AsOrgUserApiController {

    private final AsOrgService asOrgService;

    private final AsDeptService asDeptService;

    private final com.rzdata.asas7.service.AsSyncOrgUserService asSyncOrgUserService;

    @ApiOperation("获取所有组织信息")
    @GetMapping("/getToken")
    public AjaxResult getToken() throws Exception {
        return this.asSyncOrgUserService.getToken();
    }

    @ApiOperation("初始化组织和用户入口")
    @GetMapping("/initOrgAndUser")
    public AjaxResult initOrgAndUser(@RequestParam String topOrgName,
                                     @RequestParam String parentId,
                                     @RequestParam boolean isCheckStatus,
                                     @RequestParam boolean isRebuild,
                                     @RequestParam boolean isRecursion,
                                     @RequestParam boolean deptAsOrgFlag,
                                     @RequestParam String deptAsOrg)  {
        return this.asSyncOrgUserService.initOrgAndUser(topOrgName,parentId,isCheckStatus,isRebuild,isRecursion,deptAsOrgFlag,deptAsOrg, dataBase);
    }

    @ApiOperation("初始化组织和用户入口-定时任务")
    @GetMapping("/initOrgAndUserJob")
    @Log(title = "同步爱数-基础数据-定时", businessType = BusinessType.INSERT)
    public AjaxResult initOrgAndUserJob(@RequestParam String topOrgName,
                                        @RequestParam String parentId,
                                        @RequestParam boolean isCheckStatus,
                                        @RequestParam boolean isRebuild,
                                        @RequestParam boolean isRecursion,
                                        @RequestParam boolean deptAsOrgFlag,
                                        @RequestParam String deptAsOrg)  {
        // return this.asSyncOrgUserService.initOrgAndUser("长园集团","1",false,true,true,"长园深瑞");
        return this.asSyncOrgUserService.initOrgAndUser(topOrgName,parentId,isCheckStatus,isRebuild,isRecursion,deptAsOrgFlag,deptAsOrg, dataBase);
    }

    @ApiOperation("比对爱数用户数据修改状态")
    @GetMapping("/updateUserStatus")
    @Log(title = "同步爱数-更新用户状态", businessType = BusinessType.INSERT)
    public AjaxResult updateUserStatus(String userName)  {
        return AjaxResult.success(this.asSyncOrgUserService.updateUserStatus(userName,true));
    }

    @ApiOperation("比对爱数部门数据修改状态")
    @GetMapping("/updateDeptStatus")
    @Log(title = "同步爱数-更新部门状态", businessType = BusinessType.INSERT)
    public AjaxResult updateDeptStatus(String deptName)  {
        return AjaxResult.success(this.asSyncOrgUserService.updateDeptStatus(deptName,true));
    }

    @ApiOperation("获取所有组织信息")
    @GetMapping("/getAllOrg")
    public AjaxResult getAllOrg() throws Exception {
       return this.asOrgService.getAllOrg();
    }

    @ApiOperation("根据组织名获取组织信息")
    @GetMapping("/getOrgByName")
    public AjaxResult getOrgByName(@RequestParam String orgName) throws Exception {
        return this.asOrgService.getOrgByName(orgName);
    }

    @ApiOperation("获取组织下子部门信息")
    @GetMapping("/getSubDepsByOrgId")
    public AjaxResult getSubDepsByOrgId(@RequestParam String orgId) throws Exception {
        return this.asOrgService.getSubDepsByOrgId(orgId);
    }

    @ApiOperation("获取所有子部门信息")
    @GetMapping("/getSubDepsByDepId")
    public AjaxResult getSubDepsByDepId(@RequestParam String depId) throws Exception {
        return this.asDeptService.getSubDepsByDepId(depId);
    }

    @ApiOperation("获取所有子用户信息")
    @GetMapping("/getSubUsersByDepid")
    public AjaxResult getSubUsersByDepid(@RequestParam String depId,
                                         @RequestParam int start,
                                         @RequestParam int limit) throws Exception {
        return this.asDeptService.getSubUsersByDepid(depId,start,limit);
    }

}
