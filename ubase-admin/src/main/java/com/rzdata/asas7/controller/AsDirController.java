package com.rzdata.asas7.controller;

import com.rzdata.framework.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * AS7集成目录接口（涉及目录新建、删除、授权等）
 *
 * <AUTHOR>
 */
@Api(value = "AS7集成接口", tags = {"AS7集成接口"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/as/api/dir")
public class AsDirController {

    private final com.rzdata.asas7.service.AsDirService asDirService;

    @ApiOperation("由名字路径获取对象信息协议")
    @GetMapping("/getInfoByPath")
    public AjaxResult getInfoByPath(@RequestParam String fullPath)  {
        return asDirService.getInfoByPath(fullPath);
    }

    @ApiOperation("转换路径协议")
    @GetMapping("/convertPath")
    public AjaxResult convertPath(@RequestParam String docid)  {
        return asDirService.convertPath(docid);
    }

    @ApiOperation("创建目录协议")
    @GetMapping("/create")
    public AjaxResult create(@RequestParam String docid,
                             @RequestParam String name,
                             @RequestParam int ondup)  {
        return asDirService.create(docid,name,ondup);
    }

    @ApiOperation("创建多级目录协议")
    @GetMapping("/createMultilevelDir")
    public AjaxResult createMultilevelDir(@RequestParam String docid,
                             @RequestParam String path)  {
        return asDirService.createMultilevelDir(docid,path);
    }

    @ApiOperation("删除目录")
    @GetMapping("/delete")
    public AjaxResult delete(@RequestParam String docid)  {
        return asDirService.delete(docid);
    }

    @ApiOperation("删除文件")
    @GetMapping("/fileDelete")
    public AjaxResult fileDelete(@RequestParam String docid)  {
        return asDirService.fileDelete(docid);
    }

    @ApiOperation("移动文件")
    @GetMapping("/fileMove")
    public AjaxResult fileMove(@RequestParam String docid,
                               @RequestParam String destparent)  {
        return asDirService.fileMove(docid,destparent);
    }

}
