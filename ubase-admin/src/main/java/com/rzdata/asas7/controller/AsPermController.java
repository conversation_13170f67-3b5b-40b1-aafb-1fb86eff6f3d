package com.rzdata.asas7.controller;

import cn.hutool.json.JSONArray;
import com.rzdata.framework.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * AS7集成权限接口
 *
 * <AUTHOR>
 */
@Api(value = "AS7集成权限接口", tags = {"AS7集成权限接口"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/as/api/perm")
public class AsPermController {

    private final com.rzdata.asas7.service.AsPermService asPermService;

    @ApiOperation("获取权限配置")
    @GetMapping("/get")
    public AjaxResult get(@RequestParam String docid)  {
        return asPermService.get(docid);
    }

    @ApiOperation("原生权限配置")
    @GetMapping("/originalSet")
    public AjaxResult originalSet(@RequestBody String body)  {
        return asPermService.originalSet(body);
    }

    @ApiOperation("添加用户权限配置")
    @GetMapping("/addUserPerm")
    public AjaxResult addUserPerm(@RequestParam String docid,String userId,String userName,String allowValue,String denyValue,boolean merge)  {
        // 设置权限JSON数组
        JSONArray permArr = this.asPermService.addCommonPerm("user",docid,userId,userName,allowValue,denyValue,merge);
        // 设置权限配置
        return this.asPermService.set(docid,permArr,true);
    }

    @ApiOperation("添加部门权限配置")
    @GetMapping("/addDeptPerm")
    public AjaxResult addDeptPerm(@RequestParam String docid,String deptId,String deptName,String allowValue,String denyValue,boolean merge)  {
        // 设置权限JSON数组
        JSONArray permArr = this.asPermService.addCommonPerm("department",docid,deptId,deptName,allowValue,denyValue,merge);
        // 设置权限配置
        return this.asPermService.set(docid,permArr,true);
    }



}
