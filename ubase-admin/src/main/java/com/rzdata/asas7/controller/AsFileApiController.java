package com.rzdata.asas7.controller;

import com.rzdata.asas7.service.FileUploadDownloadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * AS7集成接口
 *
 * <AUTHOR>
 */
@Api(value = "AS7集成接口", tags = {"AS7集成接口"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/as/file/api")
public class AsFileApiController {

    private final FileUploadDownloadService fileUploadService;

    @ApiOperation("下载")
    @PostMapping("/downloadFileId")
    public void downloadFileId(@RequestParam("fileId") String fileId, HttpServletResponse response) {
        fileUploadService.downloadFileId(fileId,response);
    }

    @ApiOperation("预览")
    @PostMapping("/previewFileId")
    public String previewFileId(@RequestParam("fileId") String fileId, HttpServletResponse response) {
       return fileUploadService.previewFileId(fileId,response);
    }


}
