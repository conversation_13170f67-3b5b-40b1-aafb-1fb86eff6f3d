--- # 流程 配置
process:
  uniteworkUrl: http://localhost:80/ehm-front/
  procDefKeyXJ: process_xunjian
  procDefKeyWX: process_weixiu
  procDefKeyBY: process_baoyang
--- # 配置临时路径存储
spring:
  servlet:
    multipart:
      # 临时文件存储位置 避免临时文件被系统清理报错
      location: /rzdata/server/temp

--- # 监控配置
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        # 增加客户端开关
        enabled: true
        # 设置 Spring Boot Admin Server 地址
        url: http://***********:9090/admin
        instance:
          prefer-ip: true # 注册实例时，优先使用 IP
        username: rzdata
        password: 123456

--- # xxl-job 配置
xxl:
  job:
    # 执行器开关
    enabled: true
    # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
    admin-addresses: http://***********:9100/xxl-job-admin
    # 执行器通讯TOKEN：非空时启用
    access-token: xxl-job
    # 执行器配置
    executor:
      # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
      appname: xxl-job-executor
      # 执行器端口号 执行器从9101开始往后写
      port: 9101
      # 执行器注册：默认IP:PORT
      address:
      # 执行器IP：默认自动获取IP
      ip:
      # 执行器运行日志文件存储磁盘路径
      logpath: ./logs/xxl-job
      # 执行器日志文件保存天数：大于3生效
      logretentiondays: 30

--- # 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************************
          username: root
          password: root
        # 从库数据源
        slave:
          lazy: true
          driverClassName: com.mysql.cj.jdbc.Driver
          url:
          username:
          password:
      druid:
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 注意这个值和druid原生不一致，默认启动了stat
        filters: stat

--- # druid 配置
spring:
  datasource:
    druid:
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: rzdata
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: ***********
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 传输模式
  transportMode: "NIO"
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${rzdata.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 如果尝试在此限制之内发送成功，则开始启用 timeout 计时。
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒
    retryInterval: 1500
    # 发布和订阅连接的最小空闲连接数
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 单个连接最大订阅数量
    subscriptionsPerConnection: 5
    # DNS监测时间间隔，单位：毫秒
    dnsMonitoringInterval: 5000

code:
  rule:
    record: 1503659081738682369