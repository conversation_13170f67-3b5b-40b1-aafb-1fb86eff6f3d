logging:
  level:
    com.rzdata: debug
    org.springframework: error
rzdata:
  name: ubase
  version: 0.1
  bpmTenantId: STANDARD_SYS
  workflowServiceUrl: http://*************:9089/ebpm-process-rest
  addressEnabled: true
  cacheLazy: true
# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /dev-ubase-admin
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256
--- # 流程 配置
--- # xxl-job 配置
xxl:
  job:
    # 执行器开关
    enabled: false
    # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
    admin-addresses: http://*************:9100/xxl-job-admin
    # 执行器通讯TOKEN：非空时启用
    access-token:
    # 执行器配置
    executor:
      # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
      appname: dev-ubase-job-executor
      # 执行器端口号 执行器从9101开始往后写
      port: 9103
      # 执行器注册：默认IP:PORT
      address:
      # 执行器IP：默认自动获取IP
      ip:
      # 执行器运行日志文件存储磁盘路径
      logpath: ./logs/xxl-job
      # 执行器日志文件保存天数：大于3生效
      logretentiondays: 30

--- # druid 配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************
    username: ubase
    password: Ubase@123
    druid:
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: rzdata
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: *************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 5
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

#onlyoffice配置
document:
  server:
    # 本程序运行时的IP地址(如果有防火墙 开启了地址映射 请配置映射后地址) 先暂时写死
    host: http://*************:88/dev-ubase-admin
    #host: http://************:8080/dev-ubase-admin
    #登录地址
    loginUrl: http://*************:88/dev-ubase-front/#/login
  cache:
    redis:
      # 是否使用Redis缓存
      enabled: true

qys:
  #契约锁服务地址
  url: 'https://oa.vazyme.com:9186'
  #契约锁accessKey
  accessKey: 'Em9DlpUe0y'
  #契约锁accessSecret
  accessSecret: '9YP1tnyX14zphdGoXaT7kF1Pw12dji'
  #契约锁业务ID
  bizId: '2935025908536250384'
  #契约锁印章ID
  sealId: '2708529673458024474'
  #发起方公司名称 必须要和契约锁的配置一致
  tenantName: '南京诺唯赞医疗科技有限公司'

drm:
  enabled: true

code:
  rule:
    record: 1503659081738682369
