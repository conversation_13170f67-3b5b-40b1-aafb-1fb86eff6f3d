<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.CodeRuleLogMapper">

    <resultMap type="com.rzdata.setting.domain.CodeRuleLog" id="CodeRuleLogResult">
        <result property="id" column="id"/>
        <result property="ruleId" column="rule_id"/>
        <result property="numberValue" column="number_value"/>
        <result property="ruleValue" column="rule_value"/>
        <result property="businessId" column="business_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectLogByValue" resultMap="CodeRuleLogResult">
        select max(number_value)  as number_value from basic_code_rule_log
        <where>
            <if test="numberValue != null">
                and number_value is not null
            </if>
            <if test="ruleId != null and ruleId != ''">
                and rule_id = #{ruleId}
            </if>
            <if test="businessId != null and businessId != ''">
                and business_id = #{businessId}
            </if>
            <if test="ruleValue != null and ruleValue != ''">
                and rule_value = #{ruleValue}
            </if>
        </where>
        order by create_time desc
        limit 1
    </select>

</mapper>
