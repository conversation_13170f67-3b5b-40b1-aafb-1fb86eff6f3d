<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassFlowMapper">

    <resultMap type="com.rzdata.setting.domain.DocClassFlow" id="DocClassFlowResult">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="bizType" column="biz_type"/>
        <result property="bizName" column="biz_name"/>
        <result property="flowKey" column="flow_key"/>
        <result property="flowName" column="flow_name"/>
        <result property="openFlag" column="open_flag"/>
        <result property="applyFlag" column="apply_flag"/>
        <result property="dealTime" column="deal_time"/>
        <result property="dealTimeUnit" column="deal_time_unit"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


    <!-- 从流程平台获取 文件新增、文件修订、文件作废的流程KEY -->
    <select id="queryFlowList" resultType="com.rzdata.framework.core.domain.entity.SysDictData">
        SELECT t.KEY_ dictValue,t.NAME_ dictLabel FROM wms_bpm.ACT_RE_PROCDEF t join wms_bpm.ACT_RE_DEPLOYMENT ard on t.DEPLOYMENT_ID_ = ard.ID_
        where t.KEY_ like '${key}%'
        group by t.KEY_, t.NAME_
    </select>


</mapper>
