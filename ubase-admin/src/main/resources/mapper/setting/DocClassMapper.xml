<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassMapper">

    <resultMap type="com.rzdata.setting.domain.DocClass" id="DocClassResult">
        <result property="id" column="id"/>
        <result property="className" column="class_name"/>
        <result property="parentClassId" column="parent_class_id"/>
        <result property="classStatus" column="class_status"/>
        <result property="classLevel" column="class_level"/>
        <result property="expiration" column="expiration"/>
        <result property="reviewCycle" column="review_cycle"/>
        <result property="checkType" column="check_type"/>
        <result property="encryptType" column="encrypt_type"/>
        <result property="yNPrint" column="y_n_print"/>
        <result property="yNRecyle" column="y_n_recyle"/>
        <result property="mergeDocConfig" column="merge_doc_config"/>
        <result property="yNOpenCode" column="y_n_open_code"/>
        <result property="yNApplyCode" column="y_n_apply_code"/>
        <result property="distributeId" column="distribute_id"/>
        <result property="openDistribute" column="open_distribute"/>
        <result property="applyDistribute" column="apply_distribute"/>
        <result property="applyPrescription" column="apply_prescription"/>
        <result property="openPrescription" column="open_prescription"/>
        <result property="sort" column="sort"/>
        <result property="openReview" column="open_review"/>
        <result property="applyReview" column="apply_review"/>

        <result property="openMerge" column="open_merge"/>
        <result property="applyMerge" column="apply_merge"/>

        <result property="signatureType" column="signature_type"/>
        <result property="openSignature" column="open_signature"/>
        <result property="applySignature" column="apply_signature"/>

        <result property="codeId" column="code_id"/>
        <result property="fileId" column="file_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="validateZf" resultType="java.lang.Integer">
        select count(*)
        from doc_print_log
        where doc_id = #{docId}
          and version_id = #{versionId}
          and (print_nums!=printed_nums)
          and dept_id = #{deptId};
    </select>

</mapper>
