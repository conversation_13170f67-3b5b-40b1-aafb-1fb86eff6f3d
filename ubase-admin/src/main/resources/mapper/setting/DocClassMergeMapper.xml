<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.DocClassMergeMapper">

    <select id="getTemplateIdByDocClass" resultType="java.lang.String">
        SELECT bc.file_id
        FROM basic_doc_class_merge bdcm
        JOIN basic_codraft bc ON bdcm.codraft_id = bc.id
        <where>
            <if test="docClass!=null and docClass!=''">
                AND bdcm.doc_class=#{docClass}
            </if>
            <if test="mergeFactorName!=null and mergeFactorName!=''">
                AND bdcm.merge_factor_name = #{mergeFactorName}
            </if>
        </where>
    </select>
</mapper>
