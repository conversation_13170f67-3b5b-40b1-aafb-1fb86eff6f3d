<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.setting.mapper.CodraftMapper">

    <resultMap type="com.rzdata.setting.domain.Codraft" id="CodraftResult">
        <result property="id" column="id"/>
        <result property="codraftName" column="codraft_name"/>
        <result property="fileId" column="file_id"/>
        <result property="codraftStatus" column="codraft_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getVoById" resultType="com.rzdata.setting.domain.Codraft">
        select * from biaoming where id = #{id}
    </select>


</mapper>