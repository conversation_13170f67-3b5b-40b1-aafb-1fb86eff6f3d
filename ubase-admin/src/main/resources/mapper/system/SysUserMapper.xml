<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysUserMapper">

    <resultMap type="com.rzdata.framework.core.domain.entity.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="tenantId" column="tenant_id"/>
        <association property="dept" column="dept_id" javaType="com.rzdata.framework.core.domain.entity.SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="com.rzdata.framework.core.domain.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="deptCode" column="dept_code"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.rzdata.framework.core.domain.entity.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               d.dept_id,
               d.parent_id,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status as dept_status,
               d.dept_code,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status as role_status,
               t.tenant_id
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role ur on u.user_id = ur.user_id
                 left join sys_role r on r.role_id = ur.role_id
                 LEFT JOIN sys_user_tenant t on u.user_id = t.user_id
    </sql>

    <select id="selectPageUserList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_tenant t on t.user_id = u.user_id
        where u.del_flag = '0'
        <if test="user.userId != null and user.userId != '0'.toString() ">
            AND u.user_id = #{user.userId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>
        <if test="user.deptId != null and user.deptId != '0'.toString() ">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
            find_in_set(#{user.deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectPageUserListForGroup" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="user.userId != null and user.userId != '0'.toString() ">
            AND u.user_id = #{user.userId}
        </if>
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.nick_name like concat('%', #{user.nickName}, '%')
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <if test="user.params.beginTime != null and user.params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{user.params.beginTime},'%y%m%d')
        </if>
        <if test="user.params.endTime != null and user.params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{user.params.endTime},'%y%m%d')
        </if>
        <if test="user.deptId != null and user.deptId != '0'.toString() ">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE
            find_in_set(#{user.deptId},
            ancestors) ))
        </if>
        <if test="user.groupType != null and user.groupType != '' and user.groupId != null and user.groupId != ''">
            <if test='user.groupType == "group"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where group_id = #{user.groupId})
            </if>
            <if test='user.groupType == "group_term"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where term_id = #{user.groupId})
            </if>
            <if test='user.groupType == "group_shift"'>
                AND u.user_id NOT IN (select user_id from basic_group_user where shift_id = #{user.groupId})
            </if>
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUserList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userId != null and userId != '0'.toString() ">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != '0'.toString() ">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        <if test="params.dataScope != null and params.dataScope != ''">
            AND ( ${params.dataScope} )
        </if>
    </select>

    <select id="selectAllocatedList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id = #{user.roleId}
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUnallocatedList" parameterType="com.rzdata.framework.core.domain.entity.SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and (r.role_id != #{user.roleId} or r.role_id IS NULL)
        and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
        ur.role_id = #{user.roleId})
        <if test="user.userName != null and user.userName != ''">
            AND u.user_name like concat('%', #{user.userName}, '%')
        </if>
        <if test="user.phonenumber != null and user.phonenumber != ''">
            AND u.phonenumber like concat('%', #{user.phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        <if test="user.params.dataScope != null and user.params.dataScope != ''">
            AND ( ${user.params.dataScope} )
        </if>
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_name = #{userName}
    </select>

    <select id="selectUserById" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_id = #{userId}
    </select>

    <select id="selectListAllByRoleKey" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and r.role_key = #{roleKey}
    </select>

    <select id="selectDeptFileManagerByDeptId" parameterType="String"
            resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE dept_id = #{deptId}
          and user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN
                                (SELECT role_id FROM sys_role WHERE role_key = '7' OR role_key = 'dept_file_manager'));-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE u.dept_id = #{deptId}  AND r.role_key = '7' OR r.role_key = 'dept_file_manager'
    </select>
    <select id="selectQaAndSc" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN (SELECT role_id FROM sys_role WHERE role_key = '1' OR role_key = '2'));-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE r.role_key = '1' OR r.role_key = '2'
    </select>

    <select id="isQa" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        <!--SELECT *
        FROM `sys_user`
        WHERE user_id IN (SELECT user_id
                          FROM sys_user_role
                          WHERE role_id IN
                                (SELECT role_id FROM sys_role WHERE role_key = 'WJGLY' OR role_key = 'company_file_manager')) and user_name = #{username};-->
        SELECT * FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
        LEFT JOIN sys_role r ON r.role_id = ur.role_id
        WHERE (r.role_key = 'WJGLY' OR r.role_key = 'company_file_manager') AND u.user_name = #{username}
    </select>

    <select id="selectUserToAiShu" resultType="com.rzdata.system.domain.vo.SysUserVo">
        SELECT
            user_id as userId,
            dept_id as deptId,
            user_name as userName,
            nick_name as nickName,
            email,
            1 as status,
            '' as password,
            0 as sort
        FROM
            sys_user
        WHERE
            `status` = 0
          AND del_flag = 0
    </select>

    <select id="selectUserByDeptPost" resultType="SysUserVo">
        select su.user_id,su.user_name,sd.dept_name from sys_user su
        LEFT JOIN sys_user_post sup on su.user_id = sup.user_id
        LEFT JOIN sys_dept sd on su.dept_id = sd.dept_id
        <where>
            <if test="deptIds != null and deptIds.size()>0" >
               su.dept_id IN
                <foreach collection="deptIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="postIds != null and postIds.size()>0" >
                AND sup.post_id IN
                <foreach collection="postIds" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            and sup.tenant_id = #{tenantId}
        </where>
    </select>
    <select id="selectUserByDeptIdAndPerms" resultType="com.rzdata.framework.core.domain.entity.SysUser">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name,d.dept_code,
        d.leader from
        sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN sys_user_role ur ON ur.user_id = u.user_id
        LEFT JOIN sys_role_menu rm ON rm.role_id = ur.role_id
        LEFT JOIN sys_menu m ON m.menu_id = rm.menu_id
        where u.del_flag = '0'
        <if test="deptId != null and deptId != '0'.toString() ">
            AND (u.dept_id = #{deptId})
        </if>
          <if test="perms!=null and perms!=''">
            AND m.perms = #{perms}
          </if>

    </select>
</mapper>
