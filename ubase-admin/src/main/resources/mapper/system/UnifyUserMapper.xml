<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.UnifyUserMapper">

    <resultMap type="com.rzdata.system.domain.UnifyUser" id="UnifyUserResult">
        <result property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="userTypeId" column="user_type_id"/>
        <result property="telephone" column="telephone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="optUser" column="opt_user"/>
        <result property="extraAttrs" column="extra_attrs"/>
        <result property="updateTime" column="update_time"/>
        <result property="appName" column="app_name"/>
        <result property="orgId" column="org_id"/>
        <result property="orgSn" column="org_sn"/>
        <result property="orgName" column="org_name"/>
        <result property="orgNamePath" column="org_name_path"/>
        <result property="position" column="position"/>
        <result property="image" column="image"/>
        <result property="orgTypeId" column="org_type_id"/>
        <result property="userTypeName" column="user_type_name"/>
        <result property="multiOrgs" column="multi_orgs"/>
        <result property="positions" column="positions"/>
        <result property="orgType" column="org_type"/>
        <result property="multiOrgsString" column="multi_orgs_string"/>
        <result property="positionType" column="position_type"/>
        <result property="groupId" column="group_id"/>
    </resultMap>


</mapper>
