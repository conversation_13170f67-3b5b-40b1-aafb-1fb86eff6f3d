<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysUserRoleMapper">

    <resultMap type="SysUserRole" id="SysUserRoleResult">
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
    </resultMap>

    <update id="addRole">
        <foreach collection="dataBaseList !=null and dataBaseList.size() > 0" item="item" separator=";">
            insert into #{item}.sys_user_role(user_id,role_id) values(#{userId},#{roleId})
        </foreach>
    </update>

</mapper>
