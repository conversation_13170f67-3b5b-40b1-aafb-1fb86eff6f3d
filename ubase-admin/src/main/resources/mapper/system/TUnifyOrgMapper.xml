<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.TUnifyOrgMapper">

    <resultMap type="com.rzdata.system.domain.TUnifyOrg" id="TUnifyOrgResult">
        <result property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="sortNum" column="sort_num"/>
        <result property="orgTypeId" column="org_type_id"/>
        <result property="snPath" column="sn_path"/>
        <result property="idPath" column="id_path"/>
        <result property="namePath" column="name_path"/>
        <result property="createTime" column="create_time"/>
        <result property="version" column="version"/>
        <result property="updateTime" column="update_time"/>
        <result property="authPortalId" column="auth_portal_id"/>
        <result property="optUser" column="opt_user"/>
        <result property="multiUsers" column="multi_users"/>
        <result property="extraAttrs" column="extra_attrs"/>
        <result property="multiUsersString" column="multi_users_string"/>
        <result property="status" column="status"/>
        <result property="userNum" column="user_num"/>
    </resultMap>


</mapper>
