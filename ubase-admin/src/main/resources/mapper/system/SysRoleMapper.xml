<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysRoleMapper">

    <resultMap type="SysRole" id="SysRoleResult">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="menuCheckStrictly" column="menu_check_strictly"/>
        <result property="deptCheckStrictly" column="dept_check_strictly"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectRoleVo">
        select distinct r.role_id,
                        r.role_name,
                        r.role_key,
                        r.role_sort,
                        r.data_scope,
                        r.menu_check_strictly,
                        r.dept_check_strictly,
                        r.status,
                        r.del_flag,
                        r.create_time,
                        r.remark
        from sys_role r
                 left join sys_user_role ur on ur.role_id = r.role_id
                 left join sys_user u on u.user_id = ur.user_id
                 left join sys_dept d on u.dept_id = d.dept_id
    </sql>

    <select id="selectPageRoleList" parameterType="SysRole" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.del_flag = '0'
        <if test="role.roleId != null and role.roleId != 0">
            AND r.role_id = #{role.roleId}
        </if>
        <if test="role.roleName != null and role.roleName != ''">
            AND r.role_name like concat('%', #{role.roleName}, '%')
        </if>
        <if test="role.status != null and role.status != ''">
            AND r.status = #{role.status}
        </if>
        <if test="role.roleKey != null and role.roleKey != ''">
            AND r.role_key like concat('%', #{role.roleKey}, '%')
        </if>
        <if test="role.params.beginTime != null and role.params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{role.params.beginTime},'%y%m%d')
        </if>
        <if test="role.params.endTime != null and role.params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{role.params.endTime},'%y%m%d')
        </if>
        <!-- 数据范围过滤 -->
        <if test="role.params.dataScope != null and role.params.dataScope != ''">
            AND ( ${role.params.dataScope} )
        </if>
        order by r.role_sort
    </select>

    <select id="selectRoleList" parameterType="SysRole" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        where r.del_flag = '0'
        <if test="roleId != null and roleId != 0">
            AND r.role_id = #{roleId}
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name like concat('%', #{roleName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND r.status = #{status}
        </if>
        <if test="roleKey != null and roleKey != ''">
            AND r.role_key like concat('%', #{roleKey}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <!-- 数据范围过滤 -->
        <if test="params.dataScope != null and params.dataScope != ''">
            AND ( ${params.dataScope} )
        </if>
        order by r.role_sort
    </select>

    <select id="selectRolePermissionByUserId" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and ur.user_id = #{userId}
    </select>

    <select id="selectRoleListByUserId" parameterType="String" resultType="Long">
        select r.role_id
        from sys_role r
                 left join sys_user_role ur on ur.role_id = r.role_id
                 left join sys_user u on u.user_id = ur.user_id
        where u.user_id = #{userId}
    </select>

    <select id="selectRolesByUserName" parameterType="String" resultMap="SysRoleResult">
        <include refid="selectRoleVo"/>
        WHERE r.del_flag = '0' and u.user_name = #{userName}
    </select>

    <select id="checkRoleByUserIdAndKey" resultType="integer">
        SELECT count(1)
        FROM sys_role s
                 LEFT JOIN sys_user_role u ON s.role_id = u.role_id
        WHERE u.user_id = #{userId}
          AND s.role_key = #{roleKey}
    </select>
    <select id="queryRolesByUserId" resultType="com.rzdata.framework.core.domain.entity.SysRole">
        select r.* from sys_role r
        join sys_user_role sur on r.role_id = sur.role_id
        <where>
            <if test="userId!=null and userId != '0'.toString()">
                sur.user_id = #{userId}
            </if>
        </where>
    </select>

</mapper>
