<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.system.mapper.SysOperLogMapper">

    <resultMap type="SysOperLog" id="SysOperLogResult">
        <id property="operId" column="oper_id"/>
        <result property="title" column="title"/>
        <result property="businessType" column="business_type"/>
        <result property="method" column="method"/>
        <result property="requestMethod" column="request_method"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operName" column="oper_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="operUrl" column="oper_url"/>
        <result property="operIp" column="oper_ip"/>
        <result property="operLocation" column="oper_location"/>
        <result property="operParam" column="oper_param"/>
        <result property="jsonResult" column="json_result"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="operTime" column="oper_time"/>
    </resultMap>

</mapper> 