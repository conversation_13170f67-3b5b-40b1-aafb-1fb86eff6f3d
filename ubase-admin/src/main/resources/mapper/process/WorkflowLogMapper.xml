<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.WorkflowLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.WorkflowLogVo" id="WorkflowLogResult">
        <result property="id" column="id"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="businessId" column="business_id"/>
        <result property="actDefName" column="act_def_name"/>
        <result property="actInstId" column="act_inst_id"/>
        <result property="yNApprove" column="y_n_approve"/>
        <result property="opinion" column="opinion"/>
        <result property="sender" column="sender"/>
        <result property="receiver" column="receiver"/>
        <result property="actStatus" column="act_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="procDefKey" column="proc_def_key"/>
        <result property="userName" column="nick_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="postName" column="post_name"/>
        <result property="pass" column="pass"/>
        <result property="actName" column="act_name"/>
    </resultMap>

    <select id="selectLogByBusinessId" resultMap="WorkflowLogResult">
        SELECT DISTINCT u.nick_name,
                        d.dept_name,
                        l.*
        FROM doc_workflow_log l
                 LEFT JOIN sys_user u ON l.sender = u.user_name
                 LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE l.act_status not in ('end', 'save')
          and l.act_def_name not in ('编制', '发起申请')
          and l.business_id = #{businessId}
        ORDER BY l.create_time DESC
    </select>

    <select id="queryRecentLog" resultType="java.lang.String">

        SELECT ifnull(l.pass, "2")
        from doc_workflow_log l
        where l.business_id = #{businessId}
        order by l.create_time desc limit 1;
    </select>

    <select id="queryToDoList" parameterType="com.rzdata.process.domain.bo.ProcessWorkFlowBo"
            resultMap="WorkflowLogResult">
        select *
        from doc_workflow_log l
        where exists(select 1
                     from doc_workflow_log_item i

                     where i.workflow_log_id = l.id
                       and i.receiver_man = #{bo.receiverMan})
    </select>
    <select id="queryApprovalRecord" resultType="com.rzdata.process.domain.vo.WorkflowLogVo">
        SELECT
        dwl.opinion,
        dwl.create_time,
        su.nick_name,
        sd.dept_name
        FROM doc_workflow_log dwl
        JOIN sys_user su ON dwl.sender = su.user_name
        JOIN sys_dept sd ON su.dept_id = sd.dept_id
        <where>
            <!--这个条件是用于拿到去重以及拿到去重后 最新的一条数据-->
            dwl.create_time IN (
            SELECT
            MAX( dwl.create_time ) AS create_time
            FROM
            doc_workflow_log dwl
            JOIN sys_user su ON dwl.sender = su.user_name
            WHERE
            <if test="applyId!=null and applyId!=''">
                dwl.business_id = #{applyId}
            </if>
            <if test="actDefName!=null and actDefName!=''">
                AND dwl.act_def_name = #{actDefName}
            </if>
            AND dwl.act_status = 'submit'
            GROUP BY
            dwl.sender,
            su.dept_id
            )
            <if test="applyId!=null and applyId!=''">
                AND dwl.business_id = #{applyId}
            </if>
            <if test="actDefName!=null and actDefName!=''">
                AND dwl.act_def_name = #{actDefName}
            </if>
            AND dwl.act_status = 'submit'
        </where>
    </select>

</mapper>
