<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.RecoveryLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.RecoveryLogVo" id="RecoveryLogResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="createTime" column="create_time"/>
        <result property="versionId" column="version_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="applyId" column="apply_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="versionValue" column="version_value"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="compileDeptId" column="compile_dept_id"/>
        <result property="compileDeptName" column="compile_dept_name"/>
        <result property="classCame" column="class_name"/>
        <result property="noRecyclNum" column="no_recycl_num"/>
        <result property="recyclNum" column="recycl_num"/>
    </resultMap>

    <select id="selectRecoveryLogPage" resultMap="RecoveryLogResult">
        SELECT
        <!--分发数量-->
        (select sum(nums) from doc_distribute_log dl  join sys_dept d on dl.dept_id=d.dept_id

        where dl.apply_id=v.apply_id and dl.doc_id=v.doc_id

        <!-- 数据范围过滤 -->
        <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
            AND ( ${bo.params.dataScope} )
        </if>
        group by dl.doc_id) as no_recycl_num,

        (select create_time from doc_distribute_log dl  where dl.apply_id=s.id group by apply_id) as create_time,

        s.id as  apply_id,
        s.doc_id,
        v.version_value,
        v.start_date,
        v.end_date,
        s.doc_class,
        s.doc_name,
        s.user_name as org_user_name,
        d.dept_name as org_dept_name,
        dc.class_name
        FROM doc_modify_apply s
        LEFT JOIN doc_version v ON v.apply_id = s.id
        LEFT JOIN basic_doc_class dc ON s.doc_class = dc.id
        LEFT JOIN sys_dept d ON s.dept_id = d.dept_id
        <where>
            exists (select 1 from doc_recovery_log dca where dca.apply_id=s.id)

            <if test="bo.docName != '' and bo.docName != null">
                and s.doc_name like concat('%',#{bo.docName},'%')
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                and s.doc_class = #{bo.docClass}
            </if>

            and exists (select 1 from doc_distribute_log dl join sys_dept d on dl.dept_id=d.dept_id
            where dl.apply_id=s.id
                <!-- 数据范围过滤 -->
                <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                    AND ( ${bo.params.dataScope} )
                </if>
            )

            <if test="bo.status != '' and bo.status != null">
                <!---新增回收中的逻辑 -->
                <if test="bo.status == 0">
                    and (select sum(no_recycl_num) from doc_recovery_log dl
                    join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=v.apply_id  and dl.doc_id=v.doc_id
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>
                    group by dl.apply_id
                    )
                    &gt;
                    ifnull((select sum(recycl_num) from doc_recovery_log dl
                    join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=v.apply_id and  dl.doc_id=v.doc_id
                    <!--doc_distribute_log表的分发部门-->
                    <!-- 数据范围过滤 -->
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>
                    group by dl.apply_id),0)
                </if>

                <!---新增已回收的逻辑 -->
                <if test="bo.status == 1">
                    and (select sum(no_recycl_num) from doc_recovery_log dl
                    join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=v.apply_id  and dl.doc_id=v.doc_id
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>
                    group by dl.apply_id
                    )
                    &lt;=
                    ifnull((select sum(recycl_num) from doc_recovery_log dl
                    join sys_dept d on dl.dept_id=d.dept_id
                    where dl.apply_id=v.apply_id and  dl.doc_id=v.doc_id
                    <!--doc_distribute_log表的分发部门-->
                    <!-- 数据范围过滤 -->
                    <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                        AND ( ${bo.params.dataScope} )
                    </if>
                    group by dl.apply_id),0)
                </if>

            </if>

        </where>
        ORDER BY s.apply_time DESC
    </select>

    <select id="listPage" resultMap="RecoveryLogResult">
        SELECT l.*,
        v.start_date
        FROM doc_recovery_log l
        LEFT JOIN doc_version v ON l.version_id = v.id
        <where>
            <!-- 前端传入status=0 表示查询未回收的 -->
            <if test="bo.status != null and bo.status == 0">
                <!-- 已回收数量不等于回收数量(也就是没有全部都回收完) 则为未回收 -->
                and l.no_recycl_num != l.recycl_num
            </if>
            <!-- 前端传入status=1 表示查询已回收的 -->
            <if test="bo.status != null and bo.status == 1">
                <!-- 已回收数量等于回收数量(也就是全部都回收完) 则为已回收 -->
                and l.no_recycl_num = l.recycl_num
            </if>
            <if test="bo.docName != '' and bo.docName != null">
                AND l.doc_name like CONCAT('%', #{bo.docName} ,'%')
            </if>
            <!--文件类型查询条件-->
            <if test="bo.docClass!='' and bo.docClass!=null">
                AND l.doc_class = #{bo.docClass}
            </if>
            <if test="bo.compileDeptId!='' and bo.compileDeptId!=null">
                AND l.compile_dept_id = #{bo.compileDeptId}
            </if>
            <!--分发时间查询条件-->
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and l.distribute_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and l.distribute_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.disStartTime!=null and bo.disStartTime!='' and bo.disEndTime!=null and bo.disEndTime!=''">
                AND l.distribute_time BETWEEN #{bo.disStartTime} AND #{bo.disEndTime}
            </if>
            <!--生效日期查询条件-->
            <if test="bo.startTime!=null and bo.startTime!='' and bo.endTime!=null and bo.endTime!=''">
                AND v.start_date BETWEEN #{bo.startTime} AND #{bo.endTime}
            </if>
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
        </where>
        ORDER BY l.distribute_time DESC

    </select>

</mapper>
