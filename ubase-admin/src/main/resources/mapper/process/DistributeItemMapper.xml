<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DistributeItemMapper">

    <resultMap type="com.rzdata.process.domain.vo.DistributeItemVo" id="DistributeItemResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="distributeId" column="distribute_id"/>
        <result property="docId" column="doc_id"/>
        <result property="versionId" column="version_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="distributeTime" column="distribute_time"/>
        <result property="receiveUserName" column="receive_user_name"/>
        <result property="receiveTime" column="receive_time"/>
<!--        <result property="printUserName" column="print_user_name"/>-->
<!--        <result property="printTime" column="print_time"/>-->
<!--        <result property="printTimes" column="print_times"/>-->
<!--        <result property="recoveryUserName" column="recovery_user_name"/>-->
<!--        <result property="recoveryTime" column="recovery_time"/>-->
<!--        <result property="recoveryType" column="recovery_type"/>-->
<!--        <result property="isDeleted" column="is_deleted"/>-->
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptName" column="dept_name"/>
<!--        <result property="receiveNickName" column="receiveNickName"/>-->
<!--        <result property="printNickName" column="printNickName"/>-->
<!--        <result property="recoveryNickName" column="recoveryNickName"/>-->
    </resultMap>


    <select id="selectDistributeItemPage" resultMap="DistributeItemResult">
        SELECT
            i.*
        FROM
            doc_distribute_item i
        <where>
            <if test="bo.docId != null and bo.docId != ''">
                and i.doc_id = #{bo.docId}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and i.version_id = #{bo.versionId}
            </if>
            <!--分发部门 查询自己分发部门-->
            <if test="bo.deptId != null and bo.deptId != ''">
                and i.dept_id = #{bo.deptId}
            </if>
        </where>
        ORDER BY i.create_time DESC
    </select>

    <select id="listByAppid" resultMap="DistributeItemResult">
        SELECT
        i.*,
        d.dept_name,
        su.nick_name as receiveNickName,
        su1.nick_name as printNickName,
        su2.nick_name as recoveryNickName
        FROM
        doc_distribute_item i
        LEFT JOIN sys_dept d ON i.dept_id = d.dept_id
        LEFT JOIN sys_user su ON i.receive_user_name = su.user_name
        LEFT JOIN sys_user su1 ON i.print_user_name = su1.user_name
        LEFT JOIN sys_user su2 ON i.recovery_user_name = su2.user_name
        <where>
            <if test="bo.applyId != null and bo.applyId != ''">
                and i.apply_id = #{bo.applyId}
            </if>

            <!-- 数据范围过滤 -->
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
        </where>
        ORDER BY i.create_time DESC
    </select>

    <select id="listRecoveryByAppid" resultMap="DistributeItemResult">
        SELECT
        i.*,
        d.dept_name,
        su.nick_name as receiveNickName,
        su1.nick_name as printNickName,
        su2.nick_name as recoveryNickName
        FROM
        doc_distribute_item i
        LEFT JOIN sys_dept d ON i.dept_id = d.dept_id
        LEFT JOIN sys_user su ON i.receive_user_name = su.user_name
        LEFT JOIN sys_user su1 ON i.print_user_name = su1.user_name
        LEFT JOIN sys_user su2 ON i.recovery_user_name = su2.user_name
        <where>
            <if test="bo.applyId != null and bo.applyId != ''">
                and i.apply_id = #{bo.applyId}
            </if>

            <if test="bo.docId != null and bo.docId != ''">
                and i.doc_id = #{bo.docId}
            </if>

            <!-- 数据范围过滤 -->
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
        </where>
        ORDER BY i.id DESC
    </select>


    <select id="selectDistributeItemList" resultMap="DistributeItemResult">
        SELECT
        i.*,
        d.dept_name,
        su.nick_name as receiveNickName,
        su.nick_name as printNickName,
        su.nick_name as recoveryNickName
        FROM
        doc_distribute_item i
        LEFT JOIN sys_dept d ON i.dept_id = d.dept_id
        LEFT JOIN sys_user su ON i.receive_user_name = su.user_name
        LEFT JOIN sys_user su1 ON i.print_user_name = su1.user_name
        LEFT JOIN sys_user su2 ON i.recovery_user_name = su2.user_name
        <where>
            <if test="bo.distributeId != null and bo.distributeId != ''">
                and i.distribute_id = #{bo.distributeId}
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and i.doc_id = #{bo.deptId}
            </if>
            <if test="bo.receiveUserName != null and bo.receiveUserName != ''">
                and i.receive_user_name = #{bo.receiveUserName}
            </if>
        </where>
        ORDER BY i.create_time DESC
    </select>

    <select id="selectPrintNumsById" resultType="long">
        select sum(print_times) from doc_distribute_item where distribute_id= #{id}
    </select>

    <select id="listByDisId" resultMap="DistributeItemResult">
        SELECT
        i.*,
        <!-- e.id as extra_apply_doc_number_id,-->
        <!-- i.id as reissue_apply_id,-->
        d.dept_name,
        su.nick_name as receiveNickName,
        FROM
        doc_distribute_item i
        LEFT JOIN sys_dept d ON i.dept_id = d.dept_id
        LEFT JOIN sys_user su ON i.receive_user_name = su.user_name
        LEFT JOIN doc_print_item p ON p.distribute_item_id = i.id
        <where>
            <if test="bo.docId != null and bo.docId != ''">
                and i.doc_id = #{bo.docId}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and i.version_id = #{bo.versionId}
            </if>
        </where>
         ORDER BY i.create_time DESC
        <!--UNION ALL SELECT * FROM doc_reissue_apply r WHERE i.id = r.distribute_ids
        UNION ALL SELECT * From doc_extra_apply_doc_number e WHERE i.doc_id = e.doc_id AND i.version_id = e.version_id-->
    </select>

</mapper>
