<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ExtraApplyMapper">

    <resultMap type="com.rzdata.process.domain.vo.ExtraApplyVo" id="ExtraApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="applyType" column="apply_type"/>
        <result property="docId" column="doc_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="versionId" column="version_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="reason" column="reason"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptName" column="dept_name"/>
    </resultMap>

    <select id="selectExtraApplyList" resultType="map">
        SELECT
        ea.id,
        ea.apply_title as applyTitle,
        ea.dept_id as deptId,
        ea.user_name as userName,
        ea.apply_time as applyTime,
        ea.reason,
        ea.status,
        w.update_time as updateTime,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_extra_apply ea
        LEFT JOIN doc_workflow_apply_log w ON ea.id = w.id
        LEFT JOIN sys_dept d ON ea.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != '' and bo.applyTitle != null">
                and ea.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.userName != '' and bo.userName != null">
                and ea.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.reason != '' and bo.reason != null">
                and ea.reason like concat('%', #{bo.reason}, '%')
            </if>
            <if test="bo.deptId != null">
                and ea.dept_id = #{bo.deptId}
            </if>
            <if test="bo.applyStartTime != null">
                and ea.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and ea.apply_time &lt;= #{bo.applyEndTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                ea.apply_title like concat('%', #{bo.searchValue}, '%')
                OR ea.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and ea.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY ea.apply_time DESC
    </select>

    <select id="selectExtraApplyList4New" resultType="map">
        SELECT
        ea.id,
        ea.apply_title as applyTitle,
        ea.dept_id as deptId,
        ea.user_name as userName,
        ea.apply_time as applyTime,
        ea.reason,
        ea.status,
        ea.update_time as updateTime,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_extra_apply ea
        LEFT JOIN doc_workflow_apply_log w ON ea.id = w.id
        LEFT JOIN sys_dept d ON ea.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != '' and bo.applyTitle != null">
                and ea.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.userName != '' and bo.userName != null">
                and ea.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.reason != '' and bo.reason != null">
                and ea.reason like concat('%', #{bo.reason}, '%')
            </if>
            <if test="bo.deptId != null">
                and ea.dept_id = #{bo.deptId}
            </if>
            <if test="bo.applyStartTime != null">
                and ea.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and ea.apply_time &lt;= #{bo.applyEndTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                ea.apply_title like concat('%', #{bo.searchValue}, '%')
                OR ea.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and ea.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY ea.apply_time DESC
    </select>


    <select id="selectDetail" resultMap="ExtraApplyResult">
        SELECT
            ea.id,
            ea.apply_title,
            ea.dept_id,
            ea.user_name,
            ea.nick_name,
            ea.apply_time,
            ea.reason,
            ea.status,
            ea.update_time,
            ea.apply_type,
            e.doc_id,
            e.doc_name,
            e.doc_class,
            e.version_id,
            e.version_value,
            d.dept_name
        FROM
            doc_extra_apply ea
                LEFT JOIN sys_dept d ON ea.dept_id = d.dept_id
                LEFT JOIN doc_extra_apply_doc_number e ON e.apply_id = ea.id
        where ea.id = #{id}
    </select>
</mapper>
