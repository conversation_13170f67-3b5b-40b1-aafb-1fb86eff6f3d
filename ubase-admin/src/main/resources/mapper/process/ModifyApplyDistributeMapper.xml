<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ModifyApplyDistributeMapper">

    <resultMap type="com.rzdata.process.domain.ModifyApplyDistribute" id="ModifyApplyDistributeResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="nums" column="nums"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryNumsByMap" resultType="java.lang.Integer" parameterType="java.util.Map">
        select ifnull(nums,0) from doc_modify_apply_distribute d
            where d.apply_id=#{applyId} and dept_id = #{deptId}
    </select>

    <select id="queryListByChangeIdApplyId" resultType="com.rzdata.process.domain.vo.ModifyApplyDistributeVo">
        select  d.* from doc_modify_apply_distribute d

        where d.apply_id=(select apply_id from doc_change_apply where id = #{bo.changeId})
    </select>

</mapper>
