<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.BorrowApplyUserMapper">

    <resultMap type="com.rzdata.process.domain.BorrowApplyUser" id="BorrowApplyUserResult">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="borrowUser" column="borrow_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>


</mapper>
