<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocLinkLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.DocLinkLogVo" id="DocLinkLogResult">
        <result property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="linkCode" column="link_code"/>
        <result property="fileName" column="file_name"/>
        <result property="fileId" column="file_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="linkType" column="link_type"/>
    </resultMap>

    <select id="queryLinkVo" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.link_code,
        l.file_name,
        l.file_id,
        l.link_type,
        l.doc_class,
        l.create_time,
        v.standard_id,
        v.doc_id,
        l.version_value,
        v.id AS version_id
        FROM
        doc_link_log l
        LEFT JOIN doc_version_link dv ON l.id = dv.link_id
        LEFT JOIN doc_version v ON dv.version_id = v.id
        <where>
            <if test="docId!=null and docId!=''">
                and v.doc_id = #{docId}
            </if>
            <if test="versionId!=null and versionId!=''">
                and v.id = #{versionId}
            </if>
            <if test="linkCode!=null and linkCode!=''">
                and l.link_code = #{linkCode}
            </if>
        </where>
    </select>


    <select id="getPageList" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.link_code,
        l.file_name,
        l.file_id,
        l.link_type,
        l.doc_class,
        l.create_time,
        v.standard_id,
        v.doc_id,
        l.version_value,
        v.id AS version_id
        FROM
        doc_link_log l
        LEFT JOIN doc_version_link dv ON l.id = dv.link_id
        LEFT JOIN doc_version v ON dv.version_id = v.id
        <where>
            <if test="bo.docId != null and  bo.docId !='' ">
                and v.doc_id = #{bo.docId}
            </if>
            <if test="bo.standardId != null and bo.standardId !='' ">
                and v.standard_id = #{bo.standardId}
            </if>
            <if test="bo.linkType != null and bo.linkType !='' ">
                and l.link_type = #{bo.linkType}
            </if>
            <if test="bo.versionId != null and bo.versionId !='' ">
                and v.id = #{bo.versionId}
            </if>
        </where>
    </select>
    <select id="queryDocLinkVo" resultType="com.rzdata.process.domain.vo.DocLinkLogVo">
        SELECT
        l.id,
        l.link_code,
        b.file_name,
        b.id as file_id,
        l.link_type,
        l.doc_class,
        l.create_time,
        v.standard_id,
        v.doc_id,
        l.version_value,
        s.doc_name,
        v.id AS version_id
        FROM
        doc_link_log l
        LEFT JOIN doc_version_link dv ON l.id = dv.link_id
        LEFT JOIN doc_version v ON dv.version_id = v.id
        LEFT JOIN doc_standard s ON v.standard_id = s.id
        LEFT JOIN basic_file b ON l.file_id = b.id
        <where>
            <!-- status=1 查出有效的关联文件 2=失效-->
            l.status = '1'
            <if test="docId!=null and docId!=''">
                and v.doc_id = #{docId}
            </if>
            <if test="versionId!=null and versionId!=''">
                and v.id = #{versionId}
            </if>
            <if test="linkType!=null and linkType!=''">
                and l.link_type = #{linkType}
            </if>
        </where>
        order by l.create_time desc
    </select>

</mapper>
