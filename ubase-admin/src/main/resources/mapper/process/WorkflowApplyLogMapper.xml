<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.WorkflowApplyLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.WorkflowApplyLogVo" id="WorkflowApplyLogResult">
        <result property="id" column="id"/>
        <result property="applyClass" column="apply_class"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="docName" column="doc_name"/>
        <result property="docId" column="doc_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="versionId" column="version_id"/>
        <result property="versionValue" column="version_value"/>
        <result property="sender" column="sender"/>
        <result property="deptId" column="dept_id"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="procDefKey" column="proc_def_key"/>
        <result property="applyTime" column="apply_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="actDefName" column="act_def_name"/>
        <result property="userName" column="user_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="changeType" column="change_type"/>
    </resultMap>


    <select id="selectWorkflowApplyLogPage" resultMap="WorkflowApplyLogResult">
        SELECT
        l.id,l.apply_class,l.apply_status,
        case when l.doc_name is null then i.doc_name else l.doc_name end as doc_name,
        case when l.doc_id is null then i.doc_id else l.doc_id end as doc_id,
        case when l.doc_class is null then i.doc_class else l.doc_class end as doc_class,
        case when l.version_id is null then i.version_id else l.version_id end as version_id,
        case when l.version_value is null then i.version_value else l.version_value end as version_value,
        l.user_name,l.sender,l.dept_id,l.proc_inst_id,l.proc_def_key,l.apply_time,l.update_time,l.act_def_name,l.change_type,l.tenant_id,
        l.proc_status
        <!-- u.nick_name AS user_name,
        d.dept_name -->
        FROM
        doc_workflow_apply_log l
        <!-- LEFT JOIN sys_user u ON l.sender = u.user_name
        LEFT JOIN sys_dept d ON l.dept_id = d.dept_id
        LEFT JOIN doc_version v ON v.`doc_id` = l.doc_id AND v.`version_value` = l.version_value-->
        LEFT JOIN doc_review_apply_item i on l.id = i.apply_id
        <where>
            l.apply_class not in ('ADD','UPDATE','DISUSE')
            <if test='bo.actDefName != null and bo.actDefName != "" and bo.actDefName == "2"'>
                and (l.act_def_name = '结束' or l.act_def_name = '归档')
            </if>
            <if test="bo.sender != null and bo.sender != ''">
                and l.sender = #{bo.sender}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (l.doc_name like concat('%', #{bo.docName}, '%') or i.doc_name like concat('%', #{bo.docName}, '%'))
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and ( l.doc_class = #{bo.docClass} or i.doc_class = #{bo.docClass})
            </if>
            <if test="bo.applyClass != null and bo.applyClass != ''">
                and l.apply_class = #{bo.applyClass}
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and l.apply_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and l.apply_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.procStatus != null and bo.procStatus != '' and bo.procStatus == 'done'" >
                and l.proc_status = #{bo.procStatus}
            </if>
            <if test="bo.procStatus != null and bo.procStatus != '' and bo.procStatus != 'done'" >
                and l.proc_status != 'done'
            </if>
        </where>
        GROUP BY l.id
        ORDER BY l.apply_time desc
    </select>

    <select id="selectEffectDate" resultType="date">
        select v.start_date
        from doc_version v
        where v.`status` = 1
          and v.doc_id = #{docId}
    </select>

    <select id="selectBorrowUser" resultType="string">
        SELECT distinct u.nick_name
        FROM doc_borrow_apply_user a
                 LEFT JOIN sys_user u ON a.borrow_user = u.user_name
        WHERE a.apply_id = #{id}
    </select>

    <select id="selectExtraList" resultType="map">
        select c.apply_num, v.start_date
        from doc_extra_apply_doc_number c
                 <!--LEFT JOIN doc_standard s on c.doc_id = s.id -->
        LEFT JOIN doc_version v on c.doc_id = v.doc_id
        LEFT JOIN doc_standard s on v.standard_id = s.id
        where c.apply_id = #{id}
    </select>

    <select id="selectFileStatistic" resultType="com.rzdata.process.domain.vo.IndexVo">
        SELECT
            d.dept_name as name,
            count( 1 ) as num,
            CONCAT(d.ancestors,",",d.dept_id) as deptId
        FROM
            doc_workflow_apply_log w
                LEFT JOIN sys_dept d ON w.dept_id = d.dept_id
        <where>
            apply_class in ('ADD','UPDATE','DISUSE')
            <if test="bo.deptId != null">
                and w.dept_id = #{bo.deptId}
            </if>
            <if test="bo.type != null and bo.type != ''">
                and w.apply_class = #{bo.type}
            </if>
            <if test="bo.startTime != null and bo.endTime != null">
                and w.apply_time between #{bo.startTime } and #{bo.endTime}
            </if>
        </where>
        GROUP BY
            w.dept_id
    </select>

    <select id="selectDeptList" resultType="com.rzdata.process.domain.vo.IndexDeptVo">
        SELECT
            dept_id as deptId,
            dept_name as name
        FROM
            sys_dept
        WHERE
            del_flag = 0
          AND LENGTH( ancestors )- LENGTH(
                REPLACE ( ancestors, ",", "" )) = 1
          AND dept_id not in (225,226)
    </select>

    <select id="isAllPrint" resultType="java.lang.Long">
<!--查文件打印表 这个版本的文件 打印数不等于已打印(说明未打印完)-->
        select count(*) from doc_print_log where doc_id = #{docId} and version_id = #{versionId} and (print_nums!=printed_nums);
    </select>

    <select id="queryProcessStatus" resultType="java.lang.String">
        SELECT change_type from doc_workflow_apply_log l

        where l.doc_id=#{docId} ORDER BY l.apply_time desc limit 1;
    </select>

    <select id="getDocInfoByApplyId" resultType="com.rzdata.process.domain.vo.VersionVo">
        SELECT * from doc_version v
        left join doc_borrow_apply_user bu on bu.version_id = v.id
           where
             bu.apply_id=#{applyId}
            group by bu.doc_id
    </select>
</mapper>
