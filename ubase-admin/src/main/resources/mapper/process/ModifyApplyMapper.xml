<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ModifyApplyMapper">

    <resultMap type="com.rzdata.process.domain.ModifyApply" id="ModifyApplyResult">
        <result property="id" column="id"/>
        <result property="changeType" column="change_type"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="docId" column="doc_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="expiration" column="expiration"/>
        <result property="versionValue" column="version_value"/>
        <result property="versionId" column="version_id"/>
        <result property="yNTrain" column="y_n_train"/>
        <result property="trainDept" column="train_dept"/>
        <result property="yNMergeDraft" column="y_n_merge_draft"/>
        <result property="yNEncrypt" column="y_n_encrypt"/>
        <result property="yNDistribute" column="y_n_distribute"/>
        <result property="fileId" column="file_id"/>
        <result property="recordStatus" column="record_status"/>
        <result property="changeReason" column="change_reason"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="nickName" column="nick_name"/>
    </resultMap>

    <select id="selectModifyApplyList" resultType="map">
        SELECT
        m.id,
        m.change_type as changeType,
        m.doc_class as docClass,
        m.doc_name as docName,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.user_name as userName,
        m.apply_time as applyTime,
        w.update_time as updateTime,
        m.expiration,
        m.version_id as versionId,
        m.version_value as versionValue,
        m.y_n_train as yNTrain,
        m.train_dept as trainDept,
        m.y_n_merge_draft as yNMergeDraft,
        m.y_n_encrypt as yNEncrypt,
        m.y_n_distribute as yNDistribute,
        m.file_id as fileId,
        m.record_status as recordStatus,
        m.change_reason as changeReason,
        m.content,
        m.remark,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName,
        m.change_id AS changeId
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.versionValue != null and bo.versionValue != ''">
                and m.version_value = #{bo.versionValue}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and m.version_id = #{bo.versionId}
            </if>
            <if test="bo.applyStartTime != null">
                and m.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.apply_time &lt;= #{bo.applyEndTime}
            </if>


            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.doc_name like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>
    <select id="selectModifyApplyList4New" resultType="map">
        SELECT
        m.id,
        m.change_type as changeType,
        m.doc_class as docClass,
        m.doc_name as docName,
        m.doc_id as docId,
        m.dept_id as deptId,
        m.user_name as userName,
        m.apply_time as applyTime,
        m.update_time as updateTime,
        m.expiration,
        m.version_id as versionId,
        m.version_value as versionValue,
        m.y_n_train as yNTrain,
        m.train_dept as trainDept,
        m.y_n_merge_draft as yNMergeDraft,
        m.y_n_encrypt as yNEncrypt,
        m.y_n_distribute as yNDistribute,
        m.file_id as fileId,
        m.record_status as recordStatus,
        m.change_reason as changeReason,
        m.content,
        m.remark,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        <where>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>

            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>

            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.versionValue != null and bo.versionValue != ''">
                and m.version_value = #{bo.versionValue}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and m.version_id = #{bo.versionId}
            </if>
            <if test="bo.applyStartTime != null">
                and m.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and m.apply_time &lt;= #{bo.applyEndTime}
            </if>


            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                m.doc_id like concat('%', #{bo.searchValue}, '%')
                OR m.doc_name like concat('%', #{bo.searchValue}, '%')
                OR m.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="ids != null and ids.size > 0">
                and m.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>

    <select id="getDocTypeByDocId" resultType="string">
        select doc_class from doc_standard where id = #{docId}
    </select>

    <select id="getDocVersionByDocId" resultType="string">
        select version_value from doc_version where doc_id = #{docId} and `status` = 1 LIMIT 1
    </select>

    <select id="getApplyIdByDocId" resultType="string">
        select id from doc_modify_apply where  doc_id = #{docId} and change_type != 'DISUSE' order by apply_time desc limit 1
    </select>


    <select id="getApplyModifyList" resultMap="ModifyApplyResult">
        SELECT
        m.id,
        m.change_type,
        m.doc_class,
        m.doc_name,
        m.doc_id,
        m.dept_id,
        m.user_name,
        m.apply_time,
        m.update_time,
        m.expiration,
        m.y_n_train,
        m.train_dept,
        m.y_n_merge_draft,
        m.y_n_encrypt,
        m.y_n_distribute,
        m.file_id,
        m.record_status,
        m.change_reason,
        m.content,
        m.remark,
        w.apply_class,
        w.apply_status,
        w.doc_name,
        w.doc_id,
        w.doc_class,
        w.version_id,
        w.version_value,
        w.sender,
        d.dept_name,
        su.nick_name
        FROM
        doc_modify_apply m
        LEFT JOIN doc_workflow_apply_log w ON m.id = w.id
        LEFT JOIN sys_dept d ON m.dept_id = d.dept_id
        LEFT JOIN sys_user su ON m.user_name = su.user_name
        <where>

            <if test="bo.changeType != null and bo.changeType != ''">
                and m.change_type = #{bo.changeType}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and m.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and m.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and m.doc_id like concat('%', #{bo.docId}, '%')
            </if>
            <if test="bo.deptId != null">
                and m.dept_id = #{bo.deptId}
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and m.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.versionValue != null and bo.versionValue != ''">
                and m.version_value = #{bo.versionValue}
            </if>
            <if test="bo.versionId != null and bo.versionId != ''">
                and m.version_id = #{bo.versionId}
            </if>

            <!--只有两个状态，一个是进行中，一个是已完结-->
            <if test="bo.processStatus != null and bo.processStatus != '' and bo.processStatus == 'NEW'">
                and w.act_def_name = #{bo.processStatus}
            </if>

            <if test="bo.processStatus != null and bo.processStatus != '' and bo.processStatus != 'NEW'">
                and w.act_def_name != 'NEW'
            </if>

            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and m.apply_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and m.apply_time &lt;= #{bo.params.endTime}
            </if>
            <!-- 数据范围过滤 -->
            <if test="bo.params.dataScope != null and bo.params.dataScope != ''">
                AND ( ${bo.params.dataScope} )
            </if>
        </where>
        ORDER BY m.apply_time desc
    </select>
</mapper>
