<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.StandardMapper">

    <resultMap type="com.rzdata.process.domain.vo.StandardVo" id="StandardResult">
        <result property="id" column="id"/>
        <result property="docClass" column="doc_class"/>
        <result property="docName" column="doc_name"/>
        <result property="status" column="status"/>
        <result property="expiration" column="expiration"/>
        <result property="currentVersion" column="current_version"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="fileId" column="file_id"/>
        <result property="encryptFileId" column="encrypt_file_id"/>
        <result property="content" column="content"/>
        <result property="remark" column="remark"/>
        <result property="isSign" column="is_sign"/>
    </resultMap>

    <select id="selectPageList" resultType="StandardVo">
        select ds.*,
               bdc.class_name,
               bf.file_type,
               dv.start_date,
               dv.end_date,
               dv.review_time,
               dv.version_value,
               dv.doc_id,
               dv.id as version_id,
               dv.encrypt_file_id,
               dv.merge_file_id
        from doc_standard ds
        left join (select * from doc_version where `status`='1') dv on ds.id = dv.standard_id
        left join basic_file bf on ds.file_id = bf.id
        left join basic_doc_class bdc on ds.doc_class = bdc.id
        <where>
            <if test="bo.initFile != null and bo.initFile != ''">
                and ds.init_file = #{bo.initFile}
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and ds.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="bo.status != null and bo.status != ''">
                and ds.status = #{bo.status}
            </if>
            <if test="bo.deptId != null and bo.deptId != ''">
                and ds.dept_id = #{bo.deptId}
            </if>
            <if test="bo.inside != null and bo.inside != ''">
                and ds.dept_id = #{bo.inside}
            </if>
            <if test="bo.outside != null and bo.outside != ''">
                and ds.dept_id != #{bo.outside}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and ds.doc_class = #{bo.docClass}
            </if>
            <if test="bo.docId != null and bo.docId != ''">
                and dv.doc_id = #{bo.docId}
            </if>
            <if test="bo.params.startTime != null and bo.params.startTime != ''">
                and dv.update_time >= #{bo.params.startTime}
            </if>
            <if test="bo.params.endTime != null and bo.params.endTime != ''">
                and dv.update_time &lt;= #{bo.params.endTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                ds.doc_name LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR dv.doc_id LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR dv.version_value LIKE CONCAT('%', #{bo.searchValue}, '%')
                )
            </if>
            <if test="bo.standardId != null and bo.standardId != ''">
                and ds.id != #{bo.standardId}
                AND dv.doc_id NOT IN (
                SELECT
                l.link_code
                FROM
                doc_link_log l
                LEFT JOIN doc_version_link dv ON l.id = dv.link_id
                LEFT JOIN doc_version v ON dv.version_id = v.id
                WHERE
                v.standard_id = #{bo.standardId}
                and
                l.link_type = #{bo.linkType}
                )
            </if>
        </where>
        order by ds.apply_time desc
    </select>

    <select id="exportLinkLog" resultType="DocLinkLogVo">
        <!--select dll.id,bdc1.class_name as
        doc_class,dll.doc_id,dll.link_code,dll.version_value,dll.file_name,dll.create_time,bdc2.class_name as
        ds_doc_class,dv.version_value as current_version,ds.doc_name
        from doc_link_log dll
        left join doc_standard ds on dll.doc_id = ds.id
        left join doc_version dv on dll.version_id = dv.id
        left join basic_doc_class bdc1 on dll.doc_class = bdc1.id
        left join basic_doc_class bdc2 on ds.doc_class = bdc2.id
        <where>
            <if test="bo.linkType != null and bo.linkType != ''">
                and dll.link_type = #{bo.linkType}
            </if>
            <if test="bo.initFile != null and bo.initFile != ''">
                and ds.init_file = #{bo.initFile}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and ds.doc_class = #{bo.docClass}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                ds.doc_name LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR ds.id LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR ds.current_version LIKE CONCAT('%', #{bo.searchValue}, '%')
                )
            </if>
        </where>-->

        SELECT
        a.*,
        ds.doc_name,
        bdc1.class_name AS ds_doc_class
        FROM
        (
        SELECT
        dll.id,
        dv.doc_id,
        dll.link_code,
        dll.version_value,
        dll.file_name ,
        dll.create_time,
        bdc2.class_name AS doc_class,
        dv.version_value AS current_version,
        dv.standard_id
        FROM
        doc_link_log dll
        LEFT JOIN doc_version_link dvl ON dll.id = dvl.link_id
        LEFT JOIN doc_version dv ON dvl.version_id = dv.id
        LEFT JOIN basic_doc_class bdc2 ON dll.doc_class = bdc2.id
        WHERE
        <if test="bo.linkType != null and bo.linkType != ''">
             dll.link_type = #{bo.linkType}
        </if>
        ) a
        LEFT JOIN doc_standard ds ON a.standard_id = ds.id
        LEFT JOIN basic_doc_class bdc1 ON ds.doc_class = bdc1.id
        <where>
            <if test="bo.initFile != null and bo.initFile != ''">
                and ds.init_file = #{bo.initFile}
            </if>
            <if test="bo.docClass != null and bo.docClass != ''">
                and ds.doc_class = #{bo.docClass}
            </if>
            <if test="ids != null and ids.size > 0">
                AND ds.id IN
                <foreach collection="ids" item="i" separator="," open="(" close=")">
                      #{i}
                </foreach>
            </if>

            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (
                ds.doc_name LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR a.doc_id LIKE CONCAT('%', #{bo.searchValue}, '%')
                OR ds.current_version LIKE CONCAT('%', #{bo.searchValue}, '%')
                )
            </if>
        </where>
    </select>

    <select id="selectDocInfo" resultType="com.rzdata.process.domain.vo.DocInfoVo">
        SELECT
        s.id AS docId,
        s.doc_class AS docClass,
        s.doc_name AS docName,
        v.version_value AS versionValue,
        d.dept_name AS deptName,
        c.class_name AS typeName,
        f.id as fileId,
        f.file_name as fileName,
        f.file_path as filePath,
        f.external_file_id as externalFileId,
        f.external_file_path AS externalFilePath,
        f.external_rev AS externalRev
        FROM
        doc_standard s
        LEFT JOIN doc_version v ON s.id = v.doc_id
        LEFT JOIN sys_dept d ON s.dept_id = d.dept_id
        LEFT JOIN basic_doc_class c ON s.doc_class = c.id
        LEFT JOIN basic_file f ON s.file_id = f.id
        <where>
            <if test="docIds != null and docIds.size > 0">
                s.id in
                <foreach collection="docIds" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDocInfoByFileId" resultType="com.rzdata.process.domain.vo.DocInfoVo">
        SELECT id       as docId,
               doc_name as docName
        FROM doc_standard
        WHERE file_id = #{fileId}
    </select>

    <select id="selectFileEffect" resultType="com.rzdata.process.domain.vo.IndexVo">
        SELECT
        d.class_name as name,
        count(1) as num
        FROM
        doc_standard s
        LEFT JOIN basic_doc_class d ON s.doc_class = d.id
        INNER JOIN doc_version v ON s.id = v.doc_id
        where s.`status` = 1
        <if test="bo.deptId != null">
            and s.dept_id = #{bo.deptId}
        </if>
        <if test="bo.startTime != null and bo.endTime != null">
            and s.apply_time between #{bo.startTime } and #{bo.endTime}
        </if>
        GROUP BY doc_class
    </select>

    <select id="selectValidFile" resultType="StandardVo">
        select ds.id, ds.doc_class, ds.doc_name, ds.dept_id, dv.end_date, dv.version_value, dv.tenant_id
        from doc_standard ds
                 LEFT JOIN doc_version dv on ds.id = dv.doc_id
        where dv.`status` = '1'
          and ds.`status` = '1'
          and (forever != '1' or forever is null)
    </select>

    <select id="getStandardVoByVersionId" resultType="com.rzdata.process.domain.vo.StandardVo">
        select s.*
        from doc_version v
                 left join doc_standard s on s.id = v.standard_id
        where v.id=#{versionId}
    </select>

    <select id="queryInfoById" resultType="com.rzdata.process.domain.vo.StandardVo">
        select
               s.*,
                v.doc_id,
                v.id as versionId
        from doc_version v
                 left join doc_standard s on s.id = v.standard_id
        where s.id = #{id}
    </select>
</mapper>
