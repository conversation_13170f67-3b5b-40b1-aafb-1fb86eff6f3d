<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ReviewApplyMapper">

    <resultMap type="com.rzdata.process.domain.vo.ReviewApplyVo" id="ReviewApplyResult">
        <result property="id" column="id"/>
        <result property="applyTitle" column="apply_title"/>
        <result property="docId" column="doc_id"/>
        <result property="reviewAction" column="review_action"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="reason" column="reason"/>
        <result property="status" column="status"/>
        <result property="updateTime" column="update_time"/>
        <result property="versionValue" column="version_value"/>
        <result property="nickName" column="nickName"/>
        <result property="versionId" column="version_id"/>
        <result property="fileId" column="file_id"/>
        <result property="mergeFileId" column="merge_file_id"/>
        <result property="encryptFileId" column="encrypt_file_id"/>
        <result property="endDate" column="end_date"/>
        <association property="workflowLogVo" column="flowId" javaType="com.rzdata.process.domain.vo.WorkflowLogVo"
                     resultMap="WorkflowLogResult"/>
    </resultMap>

    <resultMap type="com.rzdata.process.domain.vo.WorkflowLogVo" id="WorkflowLogResult">
        <result property="flowId" column="flow_id"/>
        <result property="procInstId" column="proc_inst_id"/>
        <result property="businessId" column="business_id"/>
        <result property="actDefName" column="act_def_name"/>
        <result property="actInstId" column="act_inst_id"/>
        <result property="yNApprove" column="y_n_approve"/>
        <result property="opinion" column="opinion"/>
        <result property="sender" column="sender"/>
        <result property="receiver" column="receiver"/>
        <result property="actStatus" column="act_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="procDefKey" column="proc_def_key"/>
        <result property="deptName" column="deptName"/>
        <result property="nickName" column="nickName"/>
    </resultMap>

    <select id="selectReviewApplyPage" resultMap="ReviewApplyResult">
        SELECT
        ra.*,
        w.id AS flow_id,
        w.proc_inst_id,
        w.proc_def_key,
        w.act_def_name,
        w.act_inst_id,
        w.y_n_approve,
        w.opinion,
        w.sender,
        w.act_status
        FROM
        doc_review_apply ra
        LEFT JOIN doc_workflow_log w ON ra.id = w.business_id
        <where>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and ra.apply_title concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.reviewAction != null and bo.reviewAction != ''">
                and ra.review_action concat('%', #{bo.reviewAction}, '%')
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and ra.user_name concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.reason != null and bo.reason != ''">
                and ra.reason concat('%', #{bo.reason}, '%')
            </if>
            <if test="bo.deptId != null">
                and ra.dept_id = #{bo.deptId}
            </if>
            <if test="bo.applyStartTime != null">
                and ra.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and ra.apply_time &lt;= #{bo.applyEndTime}
            </if>
            <if test="ids != null and ids.size > 0">
                and w.proc_inst_id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectReviewApply" resultType="map">
        SELECT
        ra.id,
        ra.apply_title as applyTitle,
        ra.doc_id as docId,
        ra.review_action as reviewAction,
        ra.dept_id as deptId,
        ra.user_name as userName,
        ra.apply_time as applyTime,
        ra.reason,
        ra.status,
        w.update_time as updateTime,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,

        w.sender AS sender,
        d.dept_name AS deptName,
        i.doc_name AS docName,
        i.doc_class AS docClass,
        i.version_value AS versionValue
        FROM
        doc_review_apply ra
        LEFT JOIN doc_review_apply_item i ON ra.id = i.apply_id
        LEFT JOIN doc_workflow_apply_log w ON ra.id = w.id
        LEFT JOIN sys_dept d ON ra.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and ra.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.reviewAction != null and bo.reviewAction != ''">
                and ra.review_action like concat('%', #{bo.reviewAction}, '%')
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and ra.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.reason != null and bo.reason != ''">
                and ra.reason like concat('%', #{bo.reason}, '%')
            </if>
            <if test="bo.deptId != null">
                and ra.dept_id = #{bo.deptId}
            </if>
            <if test="bo.applyStartTime != null">
                and ra.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and ra.apply_time &lt;= #{bo.applyEndTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (ra.apply_title like concat('%', #{bo.searchValue}, '%')
                OR ra.review_action like concat('%', #{bo.searchValue}, '%')
                OR ra.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%'))
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and (w.doc_name like concat('%', #{bo.docName}, '%') or i.doc_name like concat('%', #{bo.docName}, '%'))
            </if>
            <if test="ids != null and ids.size > 0">
                and ra.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by ra.apply_time desc
    </select>

    <select id="selectReviewApply4New" resultType="map">
        SELECT
        ra.id,
        ra.apply_title as applyTitle,
        ra.doc_id as docId,
        ra.review_action as reviewAction,
        ra.dept_id as deptId,
        ra.user_name as userName,
        ra.apply_time as applyTime,
        ra.reason,
        ra.status,
        ra.update_time as updateTime,
        w.apply_class AS applyClass,
        w.apply_status AS applyStatus,
        w.doc_name AS docName,
        w.doc_id AS docId,
        w.doc_class AS docClass,
        w.version_id AS versionId,
        w.version_value AS versionValue,
        w.sender AS sender,
        d.dept_name AS deptName
        FROM
        doc_review_apply ra
        LEFT JOIN doc_workflow_apply_log w ON ra.id = w.id
        LEFT JOIN sys_dept d ON ra.dept_id = d.dept_id
        <where>
            <!--发起人-->
            <if test="bo.startUserId != null and bo.startUserId != ''">
                and w.sender = #{bo.startUserId}
            </if>
            <if test="bo.procDefKey != null and bo.procDefKey != ''">
                and w.proc_def_key like  concat(#{bo.procDefKey}, '%')
            </if>
            <if test="bo.applyTitle != null and bo.applyTitle != ''">
                and ra.apply_title like concat('%', #{bo.applyTitle}, '%')
            </if>
            <if test="bo.reviewAction != null and bo.reviewAction != ''">
                and ra.review_action like concat('%', #{bo.reviewAction}, '%')
            </if>
            <if test="bo.userName != null and bo.userName != ''">
                and ra.user_name like concat('%', #{bo.userName}, '%')
            </if>
            <if test="bo.reason != null and bo.reason != ''">
                and ra.reason like concat('%', #{bo.reason}, '%')
            </if>
            <if test="bo.deptId != null">
                and ra.dept_id = #{bo.deptId}
            </if>
            <if test="bo.applyStartTime != null">
                and ra.apply_time &gt;= #{bo.applyStartTime}
            </if>
            <if test="bo.applyEndTime != null">
                and ra.apply_time &lt;= #{bo.applyEndTime}
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (ra.apply_title like concat('%', #{bo.searchValue}, '%')
                OR ra.review_action like concat('%', #{bo.searchValue}, '%')
                OR ra.user_name like concat('%', #{bo.searchValue}, '%')
                OR w.doc_name like concat('%', #{bo.searchValue}, '%'))
            </if>
            <if test="bo.docName != null and bo.docName != ''">
                and w.doc_name like concat('%', #{bo.docName}, '%')
            </if>
            <if test="ids != null and ids.size > 0">
                and ra.id in
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by ra.apply_time desc
    </select>

    <select id="selectDetailById" resultMap="ReviewApplyResult">
        SELECT
            ra.id,
            ra.apply_title,
            ra.doc_id,
            ra.review_action,
            ra.dept_id,
            ra.user_name,
            ra.apply_time,
            ra.reason,
            ra.STATUS,
            ra.update_time,
            d.dept_name AS deptName,
            u.nick_name AS nickName,
            v.file_id,
            v.merge_file_id,
            v.encrypt_file_id,
            v.end_date
        FROM
            doc_review_apply ra
                LEFT JOIN doc_workflow_apply_log w ON ra.id = w.id
                LEFT JOIN sys_dept d ON ra.dept_id = d.dept_id
                LEFT JOIN sys_user u ON ra.user_name = u.user_name
                LEFT JOIN doc_version v ON ra.version_id = v.id
        where ra.id = #{id}
    </select>

    <select id="selectUndueList" resultType="ReviewApplyVo">
        SELECT
        dra.id,
        v.doc_id,
        dra.apply_time,
        v.version_value,
        dra.tenant_id,
        dra.STATUS,
        bdc.class_name,
        bdc.review_cycle,
        ds.dept_id,
        ds.doc_class,
        ds.doc_name
        FROM
        doc_review_apply dra
        LEFT JOIN doc_review_apply_item i ON dra.id = i.apply_id
        LEFT JOIN doc_version v ON i.version_id = v.id
        LEFT JOIN doc_standard ds ON ds.id = i.doc_id
        LEFT JOIN basic_doc_class bdc ON bdc.id = ds.doc_class
        <where>
             dra.`status` in
            <foreach collection="statusList" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>
