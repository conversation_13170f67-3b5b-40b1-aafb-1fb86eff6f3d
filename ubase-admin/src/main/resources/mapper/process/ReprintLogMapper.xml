<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.ReprintLogMapper">

    <resultMap type="com.rzdata.process.domain.vo.ReprintLogVo" id="ReprintLogResult">
        <result property="id" column="id"/>
        <result property="reprintUserName" column="reprint_user_name"/>
        <result property="reprintNickName" column="reprint_nick_name"/>
        <result property="reprintNum" column="reprint_num"/>
        <result property="reprintTime" column="reprint_time"/>
        <result property="disItemId" column="dis_item_id"/>
    </resultMap>

    <select id="selectReprintLogPage" resultMap="ReprintLogResult">
        SELECT *
        FROM doc_reprint_log
        <where>
            <if test="bo.disItemId != null and bo.disItemId != ''">
                 dis_item_id = #{bo.disItemId}
            </if>
            <if test="bo.reprintUserName != null and bo.reprintUserName != ''">
                AND reprint_user_name = #{bo.reprintUserName}
            </if>
        </where>
        ORDER BY reprint_time ASC
    </select>
</mapper>
