<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzdata.process.mapper.DocStatisticsMapper">
    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsChangeTypeVo" id="DocStatisticsChangeTypeResult">
        <result property="deptId" column="dept_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="addNum" column="add_num"/>
        <result property="updateNum" column="update_num"/>
        <result property="disuseNum" column="disuse_num"/>
        <result property="total" column="total"/>
    </resultMap>

    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo" id="DocStatisticsChangeFactorResult">
        <result property="deptId" column="dept_id"/>
        <result property="docClass" column="doc_class"/>
        <result property="personNum" column="person_num"/>
        <result property="machineNum" column="machine_num"/>
        <result property="lawNum" column="law_num"/>
        <result property="materialNum" column="material_num"/>
        <result property="linkNum" column="link_num"/>
        <result property="testNum" column="test_num"/>
        <result property="total" column="total"/>
    </resultMap>

    <resultMap type="com.rzdata.process.domain.vo.DocStatisticsDistributeVo" id="DocStatisticsDisResult">
        <result property="deptId" column="dept_id"/>
        <result property="docId" column="doc_id"/>
        <result property="applyId" column="apply_id"/>
        <result property="distributeNum" column="distribute_nums"/>
    </resultMap>

    <select id="selectChangeType" resultMap="DocStatisticsChangeTypeResult"
            parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT doc_class,dept_id,sum(( CASE WHEN (`change_type` = 'ADD') THEN 1 ELSE 0 END )) AS `add_num`,
        sum(( CASE WHEN (`change_type` = 'UPDATE') THEN 1 ELSE 0 END )) AS `update_num` ,
        sum(( CASE WHEN (`change_type` = 'DISUSE') THEN 1 ELSE 0 END )) AS `disuse_num`,
        sum(1) AS total
        FROM
        `doc_change_apply`
        <where>
            <if test="bo.deptId != '' and bo.deptId != null">
                AND dept_id = #{bo.deptId}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                AND doc_class = #{bo.docClass}
            </if>
            <if test="bo.startDateTime != null">
                AND update_time &gt; #{bo.startDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND update_time &lt;= #{bo.endDateTime}
            </if>
        </where>
        GROUP BY doc_class,dept_id;
    </select>


    <select id="selectChangeFactor" resultMap="DocStatisticsChangeFactorResult"
            parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT a.doc_class,a.dept_id,a.person_num,a.machine_num,a.law_num,a.material_num,a.link_num,a.test_num,
        SUM(a.person_num + a.machine_num + a.law_num + a.material_num + a.link_num + a.test_num) AS total
        FROM (
        SELECT
        sum(( CASE WHEN (FIND_IN_SET('人',change_factor)) THEN 1 ELSE 0 END )) AS `person_num`,
        sum(( CASE WHEN (FIND_IN_SET('机',change_factor)) THEN 1 ELSE 0 END )) AS `machine_num`,
        sum(( CASE WHEN (FIND_IN_SET('法',change_factor)) THEN 1 ELSE 0 END )) AS `law_num`,
        sum(( CASE WHEN (FIND_IN_SET('料',change_factor)) THEN 1 ELSE 0 END )) AS `material_num`,
        sum(( CASE WHEN (FIND_IN_SET('环',change_factor)) THEN 1 ELSE 0 END )) AS `link_num`,
        sum(( CASE WHEN (FIND_IN_SET('测',change_factor)) THEN 1 ELSE 0 END )) AS `test_num`,
        doc_class,
        dept_id
        FROM
        `doc_change_apply`
        <where>
            <if test="bo.deptId != '' and bo.deptId != null">
                AND dept_id = #{bo.deptId}
            </if>
            <if test="bo.docClass != '' and bo.docClass != null">
                AND doc_class = #{bo.docClass}
            </if>
            <if test="bo.startDateTime != null">
                AND update_time &gt; #{bo.startDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND update_time &lt;= #{bo.endDateTime}
            </if>
        </where>
        GROUP BY doc_class,dept_id
        ) as a GROUP BY doc_class,dept_id;
    </select>

    <select id="selectRecovery" resultType="com.rzdata.process.domain.vo.DocStatisticsChangeFactorVo">

    </select>

    <select id="selectDistribute" resultMap="DocStatisticsDisResult" parameterType="com.rzdata.process.domain.bo.DocStatisticsBo">
        SELECT doc_id,dept_id,apply_id,SUM(receive_time-distribute_time) as distribute_nums FROM doc_distribute_log
        <where>
            <if test="bo.startDateTime != null">
                AND distribute_time &gt; #{bo.disStartDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND distribute_time &lt;= #{bo.disEndDateTime}
            </if>

            <if test="bo.startDateTime != null">
                AND receive_time &gt; #{bo.recStartDateTime}
            </if>
            <if test="bo.endDateTime != null">
                AND receive_time &lt;= #{bo.recEndDateTime}
            </if>
        </where>
        GROUP BY dept_id
    </select>
</mapper>
