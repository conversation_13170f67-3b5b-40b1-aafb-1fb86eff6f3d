package com.test;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rzdata.AdminApplication;
import com.rzdata.process.domain.DistributeItem;
import com.rzdata.process.domain.ModifyApply;
import com.rzdata.process.domain.ModifyApplyLink;
import com.rzdata.process.enums.ApplyTypeEnum;
import com.rzdata.process.service.IDistributeItemService;
import com.rzdata.process.service.IGenerateIdService;
import com.rzdata.process.service.IModifyApplyLinkService;
import com.rzdata.process.service.IModifyApplyService;
import com.rzdata.setting.domain.CodeRuleLog;
import com.rzdata.setting.domain.bo.CreateNewNoBo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes= AdminApplication.class)
@RunWith(SpringRunner.class)
public class CodeRuleServiceTest {
    @Autowired
    private IGenerateIdService iGenerateIdService;
    @Autowired
    private IModifyApplyService iModifyApplyService;
    @Autowired
    private  IDistributeItemService iDistributeItemService;
    @Autowired
    private IModifyApplyLinkService modifyApplyLinkService;

    @Test
    public void mainTest(){
        long maxValue = 1;
        for(int i=1;i<3;i++){
            maxValue = maxValue * 10;
        }
        System.out.println("maxValue===="+maxValue);
        int n=3;
        String s="1";
        System.out.println("%1$0"+(n-s.length())+"d");
        System.out.println(Long.valueOf(String.format("1%1$0"+(n-1)+"d",0))*10-1);
    }



    @Test
    public void generateDocIdTest()throws Exception{
        ModifyApply modifyApply = iModifyApplyService.getById("WL02-XZ-20220422-121");
        System.out.println(iGenerateIdService.generateDocId(modifyApply));
    }

    @Test
    public void generateRecordIdTest()throws Exception{
        ModifyApply modifyApply = iModifyApplyService.getById("SC02-XZ20220409-052");
        ModifyApplyLink applyLink = modifyApplyLinkService.
                getOne(new LambdaQueryWrapper<ModifyApplyLink>().eq(ModifyApplyLink::getLinkId, "1512622870415060993").eq(ModifyApplyLink::getApplyId,"SC02-XZ20220409-052"));
        System.out.println(iGenerateIdService.generateRecordDocId(modifyApply,applyLink));
    }

    @Test
    public void generateApplyIdTest()throws Exception{
        System.out.println(iGenerateIdService.generateApplyId(ApplyTypeEnum.ADD,"QA02"));
    }

    @Test
    public void generateDistributeIdTest()throws Exception{
        List<DistributeItem> list = iDistributeItemService.list(new QueryWrapper<DistributeItem>(DistributeItem.builder().applyId("WL02-XZ-20220422-121").build()));
        int i=1;
        for(DistributeItem item:list){
            System.out.println("DistributeItem====="+i+"="+iGenerateIdService.generateDistributeId(item,"SMP"));
            i++;
        }

    }

    @Test
    public void updateDocSerialNumberTest() throws Exception {
        CreateNewNoBo bo = new CreateNewNoBo();
        bo.setNum("10787");
        bo.setBusId("WL02-XZ-20220415-179");
        bo.setNewNo("SMP-WL-10787");
        bo.setOldNo("SMP-WL-10785");
        System.out.println(iGenerateIdService.updateDocSerialNumber(bo));
    }
}
