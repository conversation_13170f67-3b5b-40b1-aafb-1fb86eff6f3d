package com.rzdata.generator.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.rzdata.framework.core.mybatisplus.core.BaseMapperPlus;
import com.rzdata.generator.domain.GenTableColumn;

import java.util.List;

/**
 * 业务字段 数据层
 *
 * <AUTHOR>
 */
@InterceptorIgnore(tenantLine = "true")
public interface GenTableColumnMapper extends BaseMapperPlus<GenTableColumn> {
    /**
     * 根据表名称查询列信息
     *
     * @param tableName 表名称
     * @return 列信息
     */
    List<GenTableColumn> selectDbTableColumnsByName(String tableName);

}
