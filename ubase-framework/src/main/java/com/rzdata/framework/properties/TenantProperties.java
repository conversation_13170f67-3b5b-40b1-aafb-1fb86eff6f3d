package com.rzdata.framework.properties;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Data
public class TenantProperties {

    private static TenantProperties instance;

    private Boolean enable = true;

    private String defaultTenantId = "00000000";

    /**
     * 多租户字段名称
     */
    private String column = "tenant_id";

    /**
     * 多租户系统数据表
     */
    private List<String> ignoreTables = new ArrayList<>();


    private TenantProperties() {
    }

    public static TenantProperties getInstance() {
        if (instance == null) {
            instance = new TenantProperties();

            //instance.getIgnoreTables().addAll(Arrays.asList("basic_doc_class","basic_code_rule","basic_code_rule_detail","basic_tenant",
            //        "xxl_job_group","xxl_job_info","xxl_job_lock","xxl_job_log","xxl_job_log_report","xxl_job_logglue","xxl_job_registry","xxl_job_user",
            //        "sys_config","sys_dict_data","sys_dict_type","sys_user_tenant","sys_user","sys_dept","sys_menu","sys_oper_log","sys_user_role","sys_role","sys_logininfor",
            //        "gen_table","gen_table_column","dual"));

            instance.getIgnoreTables().addAll(Arrays.asList("basic_tenant","basic_codraft","basic_doc_class","basic_doc_class_merge","basic_signature","basic_code_rule","basic_code_rule_detail",
                    "xxl_job_group","xxl_job_info","xxl_job_lock","xxl_job_log","xxl_job_log_report","xxl_job_logglue","xxl_job_registry","xxl_job_user",
                    "sys_user_tenant","sys_user","sys_logininfor","sys_dept","sys_role","sys_menu","sys_config","sys_dict_data","sys_dict_type", "sys_role_data_rule",
                    "sys_role_menu","sys_user_post","sys_user_role","sys_post",
                    "columns","tables",
                    "gen_table","gen_table_column","dual"));

        }
        return instance;
    }
}
