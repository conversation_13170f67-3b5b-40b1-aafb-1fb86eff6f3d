package com.rzdata.framework.convert;

import cn.hutool.core.annotation.AnnotationUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.rzdata.framework.annotation.ExcelDictFormat;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * 字典格式化转换处理
 *
 * <AUTHOR> Li
 */
@Slf4j
public class ExcelChangeTypeConvert implements Converter<String> {

	@Override
	public Class<String> supportJavaTypeKey() {
		return String.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public String convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		return "增发".equals(cellData.getStringValue())?"EXTRA":"CHANGE";
	}

	@Override
	public CellData convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		return new CellData(value.equals("EXTRA")?"增发":"分发");
	}


	private ExcelDictFormat getAnnotation(Field field) {
		return AnnotationUtil.getAnnotation(field, ExcelDictFormat.class);
	}
}
