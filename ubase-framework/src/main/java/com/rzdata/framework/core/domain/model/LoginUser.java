package com.rzdata.framework.core.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class LoginUser implements UserDetails {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 对应的二级部门ID
     */
    private String secDeptId;

    /**
     * 对应的二级部门名称
     */
    private String secDeptName;


    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
    /**
     * 当前租户id
     */
    private String tenantId;



    /**
     * 用户名称
     */
    private String nickName;

    public LoginUser(String username, String password, Set<String> permissions) {
        this.username = username;
        this.password = password;
        this.permissions = permissions;
    }

    public LoginUser(String userId, String deptId,String deptName, String secDeptId,String secDeptName, String username, String password, Set<String> permissions, String deptCode,String nickName,String tenantId) {
        this.userId = userId;
        this.deptCode = deptCode;
        this.deptId = deptId;
        this.deptName = deptName;
        this.secDeptId = secDeptId;
        this.secDeptName = secDeptName;
        this.username = username;
        this.password = password;
        this.permissions = permissions;
        this.nickName = nickName;
        this.tenantId = tenantId;
    }

    @JsonIgnore
    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    /**
     * 账户是否未过期,过期无法验证
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     */
    @JsonIgnore
    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }
}
