package com.rzdata.framework.constant;

import java.time.Duration;

/**
 * @author: wangyang
 * @date: 2022年1月21日18:12:11
 */
public class DocConstants {
    public static final String HTTP_SCHEME = "http";
    /**
     * 支持的文档类型  "xls", "xlsx", "ppt", "pptx", "txt",
     */
    public static final String[] FILE_TYPE_SUPPORT_VIEW = {"doc", "docx", "pdf"};

    /**
     * 不支持编辑的类型
     */
    public static final String[] FILE_TYPE_UN_SUPPORT_EDIT = {"pdf"};

    /**
     * 文档文件下载接口地址
     */
    public static final String OFFICE_API_DOC_FILE = "%s/process/doc-preview/file/%s";
    /**
     * 文档信息获取地址
     */
    public static final String OFFICE_API_DOC = "%s/process/doc-preview/doc/%s";
    /**
     * 编辑回调地址
     */
    public static final String OFFICE_API_CALLBACK = "%s/process/doc-preview/callback";
    /**
     * 预览地址
     */
    public static final String OFFICE_VIEWER = "%s/viewer/%s";

    /**
     * 编辑地址
     */
    public static final String OFFICE_EDITOR = "%s/editor/%s";

    /**
     * 文档redis缓存前缀 格式化
     */
    public static final String DOCUMENT_REDIS_KEY_PREFIX_FORMAT = "onlyOffice:document:%s";
    /**
     * 缓存过期时间: 1天
     */
    public static final Duration CACHE_DURATION = Duration.ofDays(1);

    public static final String HASH_KEY = "rzdata";

    public static final String KEY_FILE_ID = "fileId";

    public static final String KEY_PATH = "path";

    public static final String KEY_FILE_NAME = "fileName";
}
