package com.rzdata.framework.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {

    public static final String CHARACTER_DROP = ".";

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 流程实例
     */
    public static final String PROCESS_KEY = "sys.process.key";

    /**
     *  巡检任务状态 未完成
     */
    public static final String PATROL_TASK_STATUS_INCOMPLETE = "incomplete";

    /**
     *  巡检任务状态 已完成
     */
    public static final String PATROL_TASK_STATUS_COMPLETED = "completed";

    /**
     *  空格
     */
    public static final String SPACE = " ";

    /**
     * 0
     */
    public static final String ZERO = "0";

    /**
     * 1
     */
    public static final String ONE = "1";

    /**
     * 1
     */
    public static final String TWO = "2";
    /**
     *  超级管理员 账号
     */
    public static final String ADMIN_USERNAME = "admin";

    /**
     * 爱数access_token
     */
    public static final String ASAS7_TOKEN = "ASAS7_TOKEN";

    /**
     * 是否开启爱数平台上传文件
     */
    public static final String ASAS7_PLATFORM = "ASAS7_PLATFORM";

    public static final String AS_BASE_URL = "AS_BASE_URL";

    public static final String AS_USER_NAME = "AS_USER_NAME";

    public static final String AS_USER_PWD ="AS_USER_PWD";

    public static final String AS_SECRET = "AS_SECRET";

    public static final String AS_FILE_PATH = "AS_FILE_PATH";

    /**
     * 字符串：系统初始化
     */
    public static final String VALUE_INIT = "系统初始化";

    /**
     * 字符串：新增
     */
    public static final String VALUE_ADD = "新增";

    public static final String SLASH = "/";
}
